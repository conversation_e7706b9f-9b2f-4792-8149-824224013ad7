package com.focusflow.data.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import com.focusflow.data.model.PaymentSchedule
import kotlinx.datetime.LocalDate

@Dao
interface PaymentScheduleDao {
    @Query("SELECT * FROM payment_schedules WHERE payoffPlanId = :planId ORDER BY month ASC")
    fun getPaymentScheduleByPlan(planId: Long): Flow<List<PaymentSchedule>>

    @Query("SELECT * FROM payment_schedules WHERE payoffPlanId = :planId ORDER BY month ASC")
    suspend fun getPaymentScheduleByPlanSync(planId: Long): List<PaymentSchedule>

    @Query("SELECT * FROM payment_schedules WHERE creditCardId = :cardId ORDER BY month ASC")
    fun getPaymentScheduleByCard(cardId: Long): Flow<List<PaymentSchedule>>

    @Query("SELECT * FROM payment_schedules WHERE dueDate BETWEEN :startDate AND :endDate ORDER BY dueDate ASC")
    fun getPaymentsDueInRange(startDate: LocalDate, endDate: LocalDate): Flow<List<PaymentSchedule>>

    @Query("SELECT * FROM payment_schedules WHERE dueDate <= :date ORDER BY dueDate ASC")
    fun getOverduePayments(date: LocalDate): Flow<List<PaymentSchedule>>

    @Query("SELECT SUM(scheduledPayment) FROM payment_schedules WHERE payoffPlanId = :planId")
    suspend fun getTotalScheduledPayments(planId: Long): Double?

    @Query("SELECT SUM(principalAmount) FROM payment_schedules WHERE payoffPlanId = :planId")
    suspend fun getTotalPrincipalPayments(planId: Long): Double?

    @Query("SELECT SUM(interestAmount) FROM payment_schedules WHERE payoffPlanId = :planId")
    suspend fun getTotalInterestPayments(planId: Long): Double?

    @Insert
    suspend fun insertPaymentSchedule(paymentSchedule: PaymentSchedule): Long

    @Insert
    suspend fun insertPaymentSchedules(paymentSchedules: List<PaymentSchedule>)

    @Update
    suspend fun updatePaymentSchedule(paymentSchedule: PaymentSchedule)

    @Delete
    suspend fun deletePaymentSchedule(paymentSchedule: PaymentSchedule)

    @Query("DELETE FROM payment_schedules WHERE payoffPlanId = :planId")
    suspend fun deletePaymentSchedulesByPlan(planId: Long)
}
