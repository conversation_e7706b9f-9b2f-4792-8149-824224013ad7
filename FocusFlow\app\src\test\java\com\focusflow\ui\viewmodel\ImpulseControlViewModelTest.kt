package com.focusflow.ui.viewmodel

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.focusflow.data.model.BudgetCategory
import com.focusflow.data.model.UserPreferences
import com.focusflow.data.repository.BudgetCategoryRepository
import com.focusflow.data.repository.UserPreferencesRepository
import com.focusflow.data.repository.WishlistRepository
import com.focusflow.service.PurchaseDelayService
import io.mockk.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

@ExperimentalCoroutinesApi
class ImpulseControlViewModelTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private val testDispatcher = UnconfinedTestDispatcher()

    private lateinit var wishlistRepository: WishlistRepository
    private lateinit var purchaseDelayService: PurchaseDelayService
    private lateinit var userPreferencesRepository: UserPreferencesRepository
    private lateinit var budgetCategoryRepository: BudgetCategoryRepository
    private lateinit var viewModel: ImpulseControlViewModel

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        
        wishlistRepository = mockk()
        purchaseDelayService = mockk()
        userPreferencesRepository = mockk()
        budgetCategoryRepository = mockk()

        // Mock default behavior
        every { wishlistRepository.getAllActiveWishlistItems() } returns flowOf(emptyList())
        every { wishlistRepository.getActiveDelayItems() } returns flowOf(emptyList())
        coEvery { userPreferencesRepository.getUserPreferencesSync() } returns UserPreferences(
            id = 1,
            impulseControlEnabled = true,
            spendingThreshold = 50.0,
            coolingOffPeriodSeconds = 10,
            enableReflectionQuestions = true,
            enableBudgetWarnings = true,
            enableWishlistSuggestions = true,
            budgetPeriod = "weekly"
        )
        every { budgetCategoryRepository.getBudgetCategoriesByPeriod(any()) } returns flowOf(emptyList())
        coEvery { purchaseDelayService.getDelayStatistics() } returns mockk()
        coEvery { purchaseDelayService.processExpiredDelays() } just Runs
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
        clearAllMocks()
    }

    @Test
    fun `initial state should load impulse control settings`() = runTest {
        viewModel = ImpulseControlViewModel(
            wishlistRepository, purchaseDelayService, userPreferencesRepository, budgetCategoryRepository
        )
        
        advanceUntilIdle()

        val state = viewModel.uiState.value
        assertTrue(state.impulseControlEnabled)
        assertEquals(50.0, state.spendingThreshold)
        assertEquals(10, state.coolingOffPeriodSeconds)
        assertTrue(state.enableReflectionQuestions)
        assertTrue(state.enableBudgetWarnings)
        assertTrue(state.enableWishlistSuggestions)
        assertEquals("weekly", state.budgetPeriod)
    }

    @Test
    fun `shouldShowImpulseControl returns true for amounts above threshold`() = runTest {
        viewModel = ImpulseControlViewModel(
            wishlistRepository, purchaseDelayService, userPreferencesRepository, budgetCategoryRepository
        )
        
        advanceUntilIdle()

        // Test amount above threshold
        assertTrue(viewModel.shouldShowImpulseControl(75.0, "Groceries"))
        
        // Test amount below threshold
        assertFalse(viewModel.shouldShowImpulseControl(25.0, "Groceries"))
        
        // Test specific categories that always trigger
        assertTrue(viewModel.shouldShowImpulseControl(25.0, "Shopping"))
        assertTrue(viewModel.shouldShowImpulseControl(25.0, "Entertainment"))
        assertTrue(viewModel.shouldShowImpulseControl(25.0, "Dining"))
    }

    @Test
    fun `shouldShowImpulseControl returns false when disabled`() = runTest {
        coEvery { userPreferencesRepository.getUserPreferencesSync() } returns UserPreferences(
            id = 1,
            impulseControlEnabled = false,
            spendingThreshold = 50.0
        )

        viewModel = ImpulseControlViewModel(
            wishlistRepository, purchaseDelayService, userPreferencesRepository, budgetCategoryRepository
        )
        
        advanceUntilIdle()

        assertFalse(viewModel.shouldShowImpulseControl(100.0, "Shopping"))
    }

    @Test
    fun `getBudgetImpactPreview calculates correctly with target category`() = runTest {
        val mockCategories = listOf(
            BudgetCategory(
                id = 1,
                name = "Groceries",
                allocatedAmount = 300.0,
                spentAmount = 150.0,
                budgetPeriod = "weekly",
                budgetYear = 2024,
                budgetWeek = 1
            ),
            BudgetCategory(
                id = 2,
                name = "Entertainment",
                allocatedAmount = 200.0,
                spentAmount = 50.0,
                budgetPeriod = "weekly",
                budgetYear = 2024,
                budgetWeek = 1
            )
        )

        every { budgetCategoryRepository.getBudgetCategoriesByPeriod("weekly") } returns flowOf(mockCategories)

        viewModel = ImpulseControlViewModel(
            wishlistRepository, purchaseDelayService, userPreferencesRepository, budgetCategoryRepository
        )
        
        advanceUntilIdle()

        val impact = viewModel.getBudgetImpactPreview(100.0, "Groceries")
        
        assertEquals("Groceries", impact.targetCategory?.name)
        assertEquals(100.0, impact.purchaseAmount)
        assertFalse(impact.wouldExceedBudget) // 150 + 100 = 250 < 300
        assertEquals(50.0, impact.remainingAfterPurchase) // 300 - 150 - 100 = 50
        assertEquals(1, impact.alternativeCategories.size) // Entertainment has enough funds
        assertEquals("Entertainment", impact.alternativeCategories.first().name)
    }

    @Test
    fun `getBudgetImpactPreview detects budget overflow`() = runTest {
        val mockCategories = listOf(
            BudgetCategory(
                id = 1,
                name = "Groceries",
                allocatedAmount = 300.0,
                spentAmount = 250.0, // Already spent most of budget
                budgetPeriod = "weekly",
                budgetYear = 2024,
                budgetWeek = 1
            )
        )

        every { budgetCategoryRepository.getBudgetCategoriesByPeriod("weekly") } returns flowOf(mockCategories)

        viewModel = ImpulseControlViewModel(
            wishlistRepository, purchaseDelayService, userPreferencesRepository, budgetCategoryRepository
        )
        
        advanceUntilIdle()

        val impact = viewModel.getBudgetImpactPreview(100.0, "Groceries")
        
        assertTrue(impact.wouldExceedBudget) // 250 + 100 = 350 > 300
        assertEquals(-50.0, impact.remainingAfterPurchase) // 300 - 250 - 100 = -50
        assertTrue(impact.alternativeCategories.isEmpty()) // No alternatives with enough funds
    }

    @Test
    fun `updateSpendingThreshold updates preferences and state`() = runTest {
        coEvery { userPreferencesRepository.updateUserPreferences(any()) } just Runs

        viewModel = ImpulseControlViewModel(
            wishlistRepository, purchaseDelayService, userPreferencesRepository, budgetCategoryRepository
        )
        
        advanceUntilIdle()

        viewModel.updateSpendingThreshold(75.0)
        advanceUntilIdle()

        assertEquals(75.0, viewModel.uiState.value.spendingThreshold)
        
        coVerify { 
            userPreferencesRepository.updateUserPreferences(
                match { it.spendingThreshold == 75.0 }
            )
        }
    }

    @Test
    fun `updateCoolingOffPeriod updates preferences and state`() = runTest {
        coEvery { userPreferencesRepository.updateUserPreferences(any()) } just Runs

        viewModel = ImpulseControlViewModel(
            wishlistRepository, purchaseDelayService, userPreferencesRepository, budgetCategoryRepository
        )
        
        advanceUntilIdle()

        viewModel.updateCoolingOffPeriod(15)
        advanceUntilIdle()

        assertEquals(15, viewModel.uiState.value.coolingOffPeriodSeconds)
        
        coVerify { 
            userPreferencesRepository.updateUserPreferences(
                match { it.coolingOffPeriodSeconds == 15 }
            )
        }
    }

    @Test
    fun `getBudgetImpactPreview handles missing category gracefully`() = runTest {
        val mockCategories = listOf(
            BudgetCategory(
                id = 1,
                name = "Groceries",
                allocatedAmount = 300.0,
                spentAmount = 150.0,
                budgetPeriod = "weekly",
                budgetYear = 2024,
                budgetWeek = 1
            )
        )

        every { budgetCategoryRepository.getBudgetCategoriesByPeriod("weekly") } returns flowOf(mockCategories)

        viewModel = ImpulseControlViewModel(
            wishlistRepository, purchaseDelayService, userPreferencesRepository, budgetCategoryRepository
        )
        
        advanceUntilIdle()

        val impact = viewModel.getBudgetImpactPreview(100.0, "NonExistentCategory")
        
        assertEquals(null, impact.targetCategory)
        assertEquals(100.0, impact.purchaseAmount)
        assertFalse(impact.wouldExceedBudget)
        assertEquals(0.0, impact.remainingAfterPurchase)
        assertEquals(1, impact.alternativeCategories.size) // Groceries has enough funds
    }

    @Test
    fun `getBudgetImpactPreview calculates total budget impact correctly`() = runTest {
        val mockCategories = listOf(
            BudgetCategory(
                id = 1,
                name = "Groceries",
                allocatedAmount = 300.0,
                spentAmount = 150.0,
                budgetPeriod = "weekly",
                budgetYear = 2024,
                budgetWeek = 1
            ),
            BudgetCategory(
                id = 2,
                name = "Entertainment",
                allocatedAmount = 200.0,
                spentAmount = 50.0,
                budgetPeriod = "weekly",
                budgetYear = 2024,
                budgetWeek = 1
            )
        )

        every { budgetCategoryRepository.getBudgetCategoriesByPeriod("weekly") } returns flowOf(mockCategories)

        viewModel = ImpulseControlViewModel(
            wishlistRepository, purchaseDelayService, userPreferencesRepository, budgetCategoryRepository
        )
        
        advanceUntilIdle()

        val impact = viewModel.getBudgetImpactPreview(100.0, "Groceries")
        
        assertEquals(500.0, impact.totalBudgetImpact.totalAllocated) // 300 + 200
        assertEquals(300.0, impact.totalBudgetImpact.totalSpent) // 150 + 50 + 100
        assertEquals(200.0, impact.totalBudgetImpact.totalRemaining) // 500 - 300
    }
}
