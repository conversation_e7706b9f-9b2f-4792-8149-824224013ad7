package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime

@Entity(tableName = "payoff_plans")
data class PayoffPlan(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val strategy: String, // "AVALANCHE" or "SNOWBALL"
    val totalExtraPayment: Double,
    val totalMonths: Int,
    val totalInterestSaved: Double,
    val totalPayments: Double,
    val createdAt: LocalDateTime,
    val isActive: Boolean = true,
    val targetPayoffDate: LocalDate? = null,
    val monthlyBudgetAllocation: Double? = null // From budget envelope
)

@Entity(tableName = "payment_schedules")
data class PaymentSchedule(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val payoffPlanId: Long,
    val creditCardId: Long,
    val month: Int,
    val scheduledPayment: Double,
    val principalAmount: Double,
    val interestAmount: Double,
    val remainingBalance: Double,
    val isExtraPayment: Boolean = false,
    val dueDate: LocalDate
)

@Entity(tableName = "payoff_milestones")
data class PayoffMilestone(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val payoffPlanId: Long,
    val milestoneType: String, // "CARD_PAID_OFF", "HALFWAY_POINT", "QUARTER_COMPLETE", "CUSTOM"
    val targetDate: LocalDate,
    val targetAmount: Double,
    val description: String,
    val isCompleted: Boolean = false,
    val completedDate: LocalDate? = null,
    val celebrationMessage: String? = null
)
