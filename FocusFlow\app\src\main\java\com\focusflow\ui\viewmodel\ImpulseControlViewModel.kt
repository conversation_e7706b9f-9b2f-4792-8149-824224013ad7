package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.model.WishlistItem
import com.focusflow.data.repository.WishlistRepository
import com.focusflow.service.PurchaseDelayService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.*
import javax.inject.Inject

@HiltViewModel
class ImpulseControlViewModel @Inject constructor(
    private val wishlistRepository: WishlistRepository,
    private val purchaseDelayService: PurchaseDelayService
) : ViewModel() {

    private val _uiState = MutableStateFlow(ImpulseControlUiState())
    val uiState: StateFlow<ImpulseControlUiState> = _uiState.asStateFlow()

    val activeWishlistItems = wishlistRepository.getAllActiveWishlistItems()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    val activeDelayItems = wishlistRepository.getActiveDelayItems()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    init {
        loadDelayStatistics()
        checkExpiredDelays()
    }

    fun addItemToWishlist(
        itemName: String,
        estimatedPrice: Double,
        category: String,
        description: String? = null,
        merchant: String? = null,
        delayPeriodHours: Int = 24,
        priority: String = "medium"
    ) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val itemId = purchaseDelayService.addItemToDelayList(
                    itemName = itemName,
                    estimatedPrice = estimatedPrice,
                    category = category,
                    description = description,
                    merchant = merchant,
                    delayPeriodHours = delayPeriodHours,
                    priority = priority
                )
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    lastAddedItemId = itemId
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to add item to wishlist: ${e.message}"
                )
            }
        }
    }

    fun getRecommendedDelayPeriod(amount: Double, category: String) {
        viewModelScope.launch {
            try {
                val recommendation = purchaseDelayService.getRecommendedDelayPeriod(amount, category)
                _uiState.value = _uiState.value.copy(
                    recommendedDelayPeriod = recommendation.hours,
                    delayRecommendationReason = recommendation.reason
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to get delay recommendation: ${e.message}"
                )
            }
        }
    }

    fun extendDelay(itemId: Long, additionalHours: Int) {
        viewModelScope.launch {
            try {
                val success = purchaseDelayService.extendDelay(itemId, additionalHours)
                if (!success) {
                    _uiState.value = _uiState.value.copy(
                        error = "Failed to extend delay period"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to extend delay: ${e.message}"
                )
            }
        }
    }

    fun removeFromDelayList(itemId: Long) {
        viewModelScope.launch {
            try {
                val success = purchaseDelayService.removeFromDelayList(itemId)
                if (!success) {
                    _uiState.value = _uiState.value.copy(
                        error = "Failed to remove item from delay list"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to remove item: ${e.message}"
                )
            }
        }
    }

    fun markAsPurchased(itemId: Long, actualPrice: Double, reflectionNotes: String? = null) {
        viewModelScope.launch {
            try {
                val success = purchaseDelayService.markAsPurchased(itemId, actualPrice, reflectionNotes)
                if (!success) {
                    _uiState.value = _uiState.value.copy(
                        error = "Failed to mark item as purchased"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to mark as purchased: ${e.message}"
                )
            }
        }
    }

    fun addReflection(itemId: Long, stillWanted: Boolean, notes: String?) {
        viewModelScope.launch {
            try {
                val success = purchaseDelayService.addReflection(itemId, stillWanted, notes)
                if (!success) {
                    _uiState.value = _uiState.value.copy(
                        error = "Failed to save reflection"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to save reflection: ${e.message}"
                )
            }
        }
    }

    fun startBreathingExercise() {
        _uiState.value = _uiState.value.copy(
            isBreathingExerciseActive = true
        )
    }

    fun completeBreathingExercise() {
        _uiState.value = _uiState.value.copy(
            isBreathingExerciseActive = false,
            breathingExerciseCompleted = true
        )
    }

    fun dismissBreathingExercise() {
        _uiState.value = _uiState.value.copy(
            isBreathingExerciseActive = false,
            breathingExerciseCompleted = false
        )
    }

    private fun loadDelayStatistics() {
        viewModelScope.launch {
            try {
                val statistics = purchaseDelayService.getDelayStatistics()
                _uiState.value = _uiState.value.copy(
                    delayStatistics = statistics
                )
            } catch (e: Exception) {
                // Don't show error for statistics loading
            }
        }
    }

    private fun checkExpiredDelays() {
        viewModelScope.launch {
            try {
                purchaseDelayService.processExpiredDelays()
            } catch (e: Exception) {
                // Log error but don't show to user
            }
        }
    }

    fun getDelayPeriodOptions() = purchaseDelayService.getDelayPeriodOptions()

    fun calculateRemainingDelayTime(item: WishlistItem): Long {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val endTime = item.delayEndTime
        
        return if (endTime > now) {
            val duration = endTime.toInstant(TimeZone.currentSystemDefault()) - 
                          now.toInstant(TimeZone.currentSystemDefault())
            duration.inWholeHours
        } else {
            0L
        }
    }

    fun shouldShowImpulseControl(amount: Double, category: String): Boolean {
        // Show impulse control for purchases over $25 or in certain categories
        return amount > 25.0 || category in listOf("Shopping", "Entertainment", "Dining")
    }

    fun updateCoolingOffPeriod(newPeriod: Int) {
        viewModelScope.launch {
            try {
                // Update the cooling-off period in user preferences
                // For now, we'll just update the UI state
                _uiState.value = _uiState.value.copy(
                    coolingOffPeriod = newPeriod
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update cooling-off period: ${e.message}"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class ImpulseControlUiState(
    val isLoading: Boolean = false,
    val lastAddedItemId: Long? = null,
    val recommendedDelayPeriod: Int? = null,
    val delayRecommendationReason: String? = null,
    val isBreathingExerciseActive: Boolean = false,
    val breathingExerciseCompleted: Boolean = false,
    val delayStatistics: com.focusflow.service.DelayStatistics? = null,
    val coolingOffPeriod: Int = 10, // Default 10 seconds
    val error: String? = null
)
