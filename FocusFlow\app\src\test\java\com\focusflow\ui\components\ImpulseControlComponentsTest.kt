package com.focusflow.ui.components

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.focusflow.data.model.BudgetCategory
import com.focusflow.ui.theme.FocusFlowTheme
import com.focusflow.ui.viewmodel.BudgetImpactPreview
import com.focusflow.ui.viewmodel.TotalBudgetImpact
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class ImpulseControlComponentsTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun impulseControlDialog_displaysCorrectAmount() {
        composeTestRule.setContent {
            FocusFlowTheme {
                ImpulseControlDialog(
                    amount = 75.0,
                    category = "Entertainment",
                    onConfirm = { },
                    onCancel = { },
                    onAddToWishlist = { },
                    onDelay = { }
                )
            }
        }

        composeTestRule.onNodeWithText("Hold on! 🤔").assertIsDisplayed()
        composeTestRule.onNodeWithText("You're about to spend $75.00 on Entertainment").assertIsDisplayed()
        composeTestRule.onNodeWithText("Yes, I need this").assertIsDisplayed()
        composeTestRule.onNodeWithText("Wait 10 seconds").assertIsDisplayed()
        composeTestRule.onNodeWithText("Add to Wishlist").assertIsDisplayed()
        composeTestRule.onNodeWithText("Cancel purchase").assertIsDisplayed()
    }

    @Test
    fun impulseControlDialog_showsCustomCoolingOffPeriod() {
        composeTestRule.setContent {
            FocusFlowTheme {
                ImpulseControlDialog(
                    amount = 100.0,
                    category = "Shopping",
                    coolingOffSeconds = 15,
                    onConfirm = { },
                    onCancel = { },
                    onAddToWishlist = { },
                    onDelay = { }
                )
            }
        }

        composeTestRule.onNodeWithText("Wait 15 seconds").assertIsDisplayed()
    }

    @Test
    fun impulseControlDialog_hidesWishlistWhenDisabled() {
        composeTestRule.setContent {
            FocusFlowTheme {
                ImpulseControlDialog(
                    amount = 100.0,
                    category = "Shopping",
                    enableWishlistSuggestions = false,
                    onConfirm = { },
                    onCancel = { },
                    onAddToWishlist = { },
                    onDelay = { }
                )
            }
        }

        composeTestRule.onNodeWithText("Add to Wishlist").assertDoesNotExist()
    }

    @Test
    fun impulseControlDialog_hidesReflectionQuestionsWhenDisabled() {
        composeTestRule.setContent {
            FocusFlowTheme {
                ImpulseControlDialog(
                    amount = 100.0,
                    category = "Shopping",
                    enableReflectionQuestions = false,
                    onConfirm = { },
                    onCancel = { },
                    onAddToWishlist = { },
                    onDelay = { }
                )
            }
        }

        composeTestRule.onNodeWithText("Quick check:").assertDoesNotExist()
    }

    @Test
    fun budgetImpactCard_showsOnTrackStatus() {
        val budgetImpact = BudgetImpactPreview(
            targetCategory = BudgetCategory(
                id = 1,
                name = "Groceries",
                allocatedAmount = 300.0,
                spentAmount = 150.0,
                budgetPeriod = "weekly",
                budgetYear = 2024,
                budgetWeek = 1
            ),
            purchaseAmount = 50.0,
            wouldExceedBudget = false,
            remainingAfterPurchase = 100.0,
            alternativeCategories = emptyList(),
            totalBudgetImpact = TotalBudgetImpact(300.0, 200.0, 100.0)
        )

        composeTestRule.setContent {
            FocusFlowTheme {
                BudgetImpactCard(budgetImpact = budgetImpact)
            }
        }

        composeTestRule.onNodeWithText("Budget Impact").assertIsDisplayed()
        composeTestRule.onNodeWithText("Groceries: $100 remaining").assertIsDisplayed()
    }

    @Test
    fun budgetImpactCard_showsOverBudgetWarning() {
        val budgetImpact = BudgetImpactPreview(
            targetCategory = BudgetCategory(
                id = 1,
                name = "Entertainment",
                allocatedAmount = 100.0,
                spentAmount = 80.0,
                budgetPeriod = "weekly",
                budgetYear = 2024,
                budgetWeek = 1
            ),
            purchaseAmount = 50.0,
            wouldExceedBudget = true,
            remainingAfterPurchase = -30.0,
            alternativeCategories = listOf(
                BudgetCategory(
                    id = 2,
                    name = "Groceries",
                    allocatedAmount = 300.0,
                    spentAmount = 150.0,
                    budgetPeriod = "weekly",
                    budgetYear = 2024,
                    budgetWeek = 1
                )
            ),
            totalBudgetImpact = TotalBudgetImpact(400.0, 280.0, 120.0)
        )

        composeTestRule.setContent {
            FocusFlowTheme {
                BudgetImpactCard(budgetImpact = budgetImpact)
            }
        }

        composeTestRule.onNodeWithText("Entertainment: $-30 remaining").assertIsDisplayed()
        composeTestRule.onNodeWithText("💡 Consider using: Groceries").assertIsDisplayed()
    }

    @Test
    fun impulseControlQuestions_displaysAllQuestions() {
        composeTestRule.setContent {
            FocusFlowTheme {
                ImpulseControlQuestions(amount = 75.0)
            }
        }

        composeTestRule.onNodeWithText("Quick check:").assertIsDisplayed()
        composeTestRule.onNodeWithText("Do I really need this right now?").assertIsDisplayed()
        composeTestRule.onNodeWithText("Will I still want this tomorrow?").assertIsDisplayed()
        composeTestRule.onNodeWithText("Do I have something similar already?").assertIsDisplayed()
        composeTestRule.onNodeWithText("Is this within my budget?").assertIsDisplayed()
    }

    @Test
    fun spendingWatchlistCard_displaysCorrectContent() {
        composeTestRule.setContent {
            FocusFlowTheme {
                SpendingWatchlistCard(
                    onAddToWatchlist = { }
                )
            }
        }

        composeTestRule.onNodeWithText("Spending Watchlist").assertIsDisplayed()
        composeTestRule.onNodeWithText("Add items you're considering buying. Come back in 24-48 hours to see if you still want them.").assertIsDisplayed()
        composeTestRule.onNodeWithText("Add to Watchlist").assertIsDisplayed()
    }

    @Test
    fun budgetWarningCard_showsOverBudgetAlert() {
        composeTestRule.setContent {
            FocusFlowTheme {
                BudgetWarningCard(
                    remainingBudget = 30.0,
                    expenseAmount = 50.0
                )
            }
        }

        composeTestRule.onNodeWithText("Budget Alert!").assertIsDisplayed()
        composeTestRule.onNodeWithText("This purchase will put you $20.00 over budget.").assertIsDisplayed()
        composeTestRule.onNodeWithText("💡 Consider waiting until next week or adjusting your budget.").assertIsDisplayed()
    }

    @Test
    fun budgetWarningCard_showsLowBudgetWarning() {
        composeTestRule.setContent {
            FocusFlowTheme {
                BudgetWarningCard(
                    remainingBudget = 25.0,
                    expenseAmount = 15.0
                )
            }
        }

        composeTestRule.onNodeWithText("Budget Warning").assertIsDisplayed()
        composeTestRule.onNodeWithText("You only have $25.00 left in your budget.").assertIsDisplayed()
    }

    @Test
    fun budgetWarningCard_hidesWhenBudgetIsHealthy() {
        composeTestRule.setContent {
            FocusFlowTheme {
                BudgetWarningCard(
                    remainingBudget = 200.0,
                    expenseAmount = 30.0
                )
            }
        }

        // Card should not be displayed when budget is healthy
        composeTestRule.onNodeWithText("Budget Alert!").assertDoesNotExist()
        composeTestRule.onNodeWithText("Budget Warning").assertDoesNotExist()
    }

    @Test
    fun cooldownPeriodCard_displaysCorrectInformation() {
        composeTestRule.setContent {
            FocusFlowTheme {
                CooldownPeriodCard(
                    hoursRemaining = 12,
                    onRemoveFromCooldown = { },
                    onExtendDelay = { }
                )
            }
        }

        composeTestRule.onNodeWithText("Cooling Down").assertIsDisplayed()
        composeTestRule.onNodeWithText("12 hours remaining").assertIsDisplayed()
        composeTestRule.onNodeWithText("Extend").assertIsDisplayed()
        composeTestRule.onNodeWithText("Remove").assertIsDisplayed()
    }

    @Test
    fun delayPeriodSelector_displaysAllOptions() {
        composeTestRule.setContent {
            FocusFlowTheme {
                DelayPeriodSelector(
                    selectedHours = 24,
                    onPeriodSelected = { }
                )
            }
        }

        composeTestRule.onNodeWithText("Choose Delay Period").assertIsDisplayed()
        composeTestRule.onNodeWithText("1 Hour").assertIsDisplayed()
        composeTestRule.onNodeWithText("24 Hours").assertIsDisplayed()
        composeTestRule.onNodeWithText("48 Hours").assertIsDisplayed()
        composeTestRule.onNodeWithText("1 Week").assertIsDisplayed()
        
        // Check descriptions
        composeTestRule.onNodeWithText("Quick reflection for small purchases").assertIsDisplayed()
        composeTestRule.onNodeWithText("Standard cooling-off period").assertIsDisplayed()
        composeTestRule.onNodeWithText("Extended consideration time").assertIsDisplayed()
        composeTestRule.onNodeWithText("Major purchase evaluation").assertIsDisplayed()
    }

    @Test
    fun impulseControlDialog_triggersCorrectCallbacks() {
        var confirmCalled = false
        var cancelCalled = false
        var wishlistCalled = false

        composeTestRule.setContent {
            FocusFlowTheme {
                ImpulseControlDialog(
                    amount = 75.0,
                    category = "Entertainment",
                    onConfirm = { confirmCalled = true },
                    onCancel = { cancelCalled = true },
                    onAddToWishlist = { wishlistCalled = true },
                    onDelay = { }
                )
            }
        }

        // Test confirm button
        composeTestRule.onNodeWithText("Yes, I need this").performClick()
        assert(confirmCalled)

        // Test cancel button
        composeTestRule.onNodeWithText("Cancel purchase").performClick()
        assert(cancelCalled)

        // Test wishlist button
        composeTestRule.onNodeWithText("Add to Wishlist").performClick()
        assert(wishlistCalled)
    }
}
