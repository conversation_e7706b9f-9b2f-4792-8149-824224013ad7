package com.focusflow.data.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.AIInteraction;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDateTime;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AIInteractionDao_Impl implements AIInteractionDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<AIInteraction> __insertionAdapterOfAIInteraction;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<AIInteraction> __deletionAdapterOfAIInteraction;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldInteractions;

  public AIInteractionDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfAIInteraction = new EntityInsertionAdapter<AIInteraction>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `ai_interactions` (`id`,`userMessage`,`aiResponse`,`interactionType`,`timestamp`,`contextData`) VALUES (nullif(?, 0),?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AIInteraction entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getUserMessage() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getUserMessage());
        }
        if (entity.getAiResponse() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getAiResponse());
        }
        if (entity.getInteractionType() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getInteractionType());
        }
        final String _tmp = __converters.fromLocalDateTime(entity.getTimestamp());
        if (_tmp == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp);
        }
        if (entity.getContextData() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getContextData());
        }
      }
    };
    this.__deletionAdapterOfAIInteraction = new EntityDeletionOrUpdateAdapter<AIInteraction>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `ai_interactions` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AIInteraction entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteOldInteractions = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM ai_interactions WHERE timestamp < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertInteraction(final AIInteraction interaction,
      final Continuation<? super Long> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfAIInteraction.insertAndReturnId(interaction);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object deleteInteraction(final AIInteraction interaction,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfAIInteraction.handle(interaction);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object deleteOldInteractions(final LocalDateTime cutoffDate,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldInteractions.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(cutoffDate);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldInteractions.release(_stmt);
        }
      }
    }, arg1);
  }

  @Override
  public Flow<List<AIInteraction>> getAllInteractions() {
    final String _sql = "SELECT `ai_interactions`.`id` AS `id`, `ai_interactions`.`userMessage` AS `userMessage`, `ai_interactions`.`aiResponse` AS `aiResponse`, `ai_interactions`.`interactionType` AS `interactionType`, `ai_interactions`.`timestamp` AS `timestamp`, `ai_interactions`.`contextData` AS `contextData` FROM ai_interactions ORDER BY timestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"ai_interactions"}, new Callable<List<AIInteraction>>() {
      @Override
      @NonNull
      public List<AIInteraction> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfUserMessage = 1;
          final int _cursorIndexOfAiResponse = 2;
          final int _cursorIndexOfInteractionType = 3;
          final int _cursorIndexOfTimestamp = 4;
          final int _cursorIndexOfContextData = 5;
          final List<AIInteraction> _result = new ArrayList<AIInteraction>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AIInteraction _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpUserMessage;
            if (_cursor.isNull(_cursorIndexOfUserMessage)) {
              _tmpUserMessage = null;
            } else {
              _tmpUserMessage = _cursor.getString(_cursorIndexOfUserMessage);
            }
            final String _tmpAiResponse;
            if (_cursor.isNull(_cursorIndexOfAiResponse)) {
              _tmpAiResponse = null;
            } else {
              _tmpAiResponse = _cursor.getString(_cursorIndexOfAiResponse);
            }
            final String _tmpInteractionType;
            if (_cursor.isNull(_cursorIndexOfInteractionType)) {
              _tmpInteractionType = null;
            } else {
              _tmpInteractionType = _cursor.getString(_cursorIndexOfInteractionType);
            }
            final LocalDateTime _tmpTimestamp;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfTimestamp)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfTimestamp);
            }
            _tmpTimestamp = __converters.toLocalDateTime(_tmp);
            final String _tmpContextData;
            if (_cursor.isNull(_cursorIndexOfContextData)) {
              _tmpContextData = null;
            } else {
              _tmpContextData = _cursor.getString(_cursorIndexOfContextData);
            }
            _item = new AIInteraction(_tmpId,_tmpUserMessage,_tmpAiResponse,_tmpInteractionType,_tmpTimestamp,_tmpContextData);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<AIInteraction>> getInteractionsByType(final String type) {
    final String _sql = "SELECT * FROM ai_interactions WHERE interactionType = ? ORDER BY timestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (type == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, type);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"ai_interactions"}, new Callable<List<AIInteraction>>() {
      @Override
      @NonNull
      public List<AIInteraction> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "userMessage");
          final int _cursorIndexOfAiResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "aiResponse");
          final int _cursorIndexOfInteractionType = CursorUtil.getColumnIndexOrThrow(_cursor, "interactionType");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfContextData = CursorUtil.getColumnIndexOrThrow(_cursor, "contextData");
          final List<AIInteraction> _result = new ArrayList<AIInteraction>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AIInteraction _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpUserMessage;
            if (_cursor.isNull(_cursorIndexOfUserMessage)) {
              _tmpUserMessage = null;
            } else {
              _tmpUserMessage = _cursor.getString(_cursorIndexOfUserMessage);
            }
            final String _tmpAiResponse;
            if (_cursor.isNull(_cursorIndexOfAiResponse)) {
              _tmpAiResponse = null;
            } else {
              _tmpAiResponse = _cursor.getString(_cursorIndexOfAiResponse);
            }
            final String _tmpInteractionType;
            if (_cursor.isNull(_cursorIndexOfInteractionType)) {
              _tmpInteractionType = null;
            } else {
              _tmpInteractionType = _cursor.getString(_cursorIndexOfInteractionType);
            }
            final LocalDateTime _tmpTimestamp;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfTimestamp)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfTimestamp);
            }
            _tmpTimestamp = __converters.toLocalDateTime(_tmp);
            final String _tmpContextData;
            if (_cursor.isNull(_cursorIndexOfContextData)) {
              _tmpContextData = null;
            } else {
              _tmpContextData = _cursor.getString(_cursorIndexOfContextData);
            }
            _item = new AIInteraction(_tmpId,_tmpUserMessage,_tmpAiResponse,_tmpInteractionType,_tmpTimestamp,_tmpContextData);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<AIInteraction>> getRecentInteractions(final int limit) {
    final String _sql = "SELECT * FROM ai_interactions ORDER BY timestamp DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"ai_interactions"}, new Callable<List<AIInteraction>>() {
      @Override
      @NonNull
      public List<AIInteraction> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "userMessage");
          final int _cursorIndexOfAiResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "aiResponse");
          final int _cursorIndexOfInteractionType = CursorUtil.getColumnIndexOrThrow(_cursor, "interactionType");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfContextData = CursorUtil.getColumnIndexOrThrow(_cursor, "contextData");
          final List<AIInteraction> _result = new ArrayList<AIInteraction>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AIInteraction _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpUserMessage;
            if (_cursor.isNull(_cursorIndexOfUserMessage)) {
              _tmpUserMessage = null;
            } else {
              _tmpUserMessage = _cursor.getString(_cursorIndexOfUserMessage);
            }
            final String _tmpAiResponse;
            if (_cursor.isNull(_cursorIndexOfAiResponse)) {
              _tmpAiResponse = null;
            } else {
              _tmpAiResponse = _cursor.getString(_cursorIndexOfAiResponse);
            }
            final String _tmpInteractionType;
            if (_cursor.isNull(_cursorIndexOfInteractionType)) {
              _tmpInteractionType = null;
            } else {
              _tmpInteractionType = _cursor.getString(_cursorIndexOfInteractionType);
            }
            final LocalDateTime _tmpTimestamp;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfTimestamp)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfTimestamp);
            }
            _tmpTimestamp = __converters.toLocalDateTime(_tmp);
            final String _tmpContextData;
            if (_cursor.isNull(_cursorIndexOfContextData)) {
              _tmpContextData = null;
            } else {
              _tmpContextData = _cursor.getString(_cursorIndexOfContextData);
            }
            _item = new AIInteraction(_tmpId,_tmpUserMessage,_tmpAiResponse,_tmpInteractionType,_tmpTimestamp,_tmpContextData);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
