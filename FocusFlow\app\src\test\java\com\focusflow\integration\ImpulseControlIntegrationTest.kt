package com.focusflow.integration

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.focusflow.data.database.FocusFlowDatabase
import com.focusflow.data.model.BudgetCategory
import com.focusflow.data.model.UserPreferences
import com.focusflow.data.repository.BudgetCategoryRepository
import com.focusflow.data.repository.UserPreferencesRepository
import com.focusflow.data.repository.WishlistRepository
import com.focusflow.service.PurchaseDelayService
import com.focusflow.ui.viewmodel.ImpulseControlViewModel
import io.mockk.mockk
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

@ExperimentalCoroutinesApi
@RunWith(AndroidJUnit4::class)
class ImpulseControlIntegrationTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private lateinit var database: FocusFlowDatabase
    private lateinit var budgetCategoryRepository: BudgetCategoryRepository
    private lateinit var userPreferencesRepository: UserPreferencesRepository
    private lateinit var wishlistRepository: WishlistRepository
    private lateinit var purchaseDelayService: PurchaseDelayService
    private lateinit var viewModel: ImpulseControlViewModel

    @Before
    fun setup() {
        database = Room.inMemoryDatabaseBuilder(
            ApplicationProvider.getApplicationContext(),
            FocusFlowDatabase::class.java
        ).allowMainThreadQueries().build()

        budgetCategoryRepository = BudgetCategoryRepository(database.budgetCategoryDao())
        userPreferencesRepository = UserPreferencesRepository(database.userPreferencesDao())
        wishlistRepository = WishlistRepository(database.wishlistItemDao())
        purchaseDelayService = mockk(relaxed = true)
    }

    @After
    fun tearDown() {
        database.close()
    }

    @Test
    fun completeImpulseControlFlow_shouldWorkCorrectly() = runTest {
        // Step 1: Set up user preferences with impulse control enabled
        val preferences = UserPreferences(
            id = 1,
            impulseControlEnabled = true,
            spendingThreshold = 50.0,
            coolingOffPeriodSeconds = 10,
            enableReflectionQuestions = true,
            enableBudgetWarnings = true,
            enableWishlistSuggestions = true,
            budgetPeriod = "weekly"
        )
        userPreferencesRepository.insertUserPreferences(preferences)

        // Step 2: Set up budget categories
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val groceriesCategory = BudgetCategory(
            name = "Groceries",
            allocatedAmount = 300.0,
            spentAmount = 200.0, // $100 remaining
            budgetPeriod = "weekly",
            budgetYear = now.year,
            budgetWeek = now.dayOfYear / 7 + 1
        )
        val entertainmentCategory = BudgetCategory(
            name = "Entertainment",
            allocatedAmount = 150.0,
            spentAmount = 50.0, // $100 remaining
            budgetPeriod = "weekly",
            budgetYear = now.year,
            budgetWeek = now.dayOfYear / 7 + 1
        )
        
        budgetCategoryRepository.insertBudgetCategory(groceriesCategory)
        budgetCategoryRepository.insertBudgetCategory(entertainmentCategory)

        // Step 3: Initialize ViewModel
        viewModel = ImpulseControlViewModel(
            wishlistRepository, purchaseDelayService, userPreferencesRepository, budgetCategoryRepository
        )

        // Wait for initial load
        val initialState = viewModel.uiState.first { !it.isLoading }
        
        // Verify initial state
        assertTrue(initialState.impulseControlEnabled)
        assertEquals(50.0, initialState.spendingThreshold)
        assertEquals(10, initialState.coolingOffPeriodSeconds)

        // Step 4: Test impulse control trigger for amount above threshold
        assertTrue(viewModel.shouldShowImpulseControl(75.0, "Groceries"))
        assertFalse(viewModel.shouldShowImpulseControl(25.0, "Groceries"))

        // Step 5: Test budget impact preview for purchase within budget
        val withinBudgetImpact = viewModel.getBudgetImpactPreview(50.0, "Groceries")
        
        assertEquals("Groceries", withinBudgetImpact.targetCategory?.name)
        assertFalse(withinBudgetImpact.wouldExceedBudget)
        assertEquals(50.0, withinBudgetImpact.remainingAfterPurchase) // 100 - 50
        assertEquals(1, withinBudgetImpact.alternativeCategories.size) // Entertainment has enough

        // Step 6: Test budget impact preview for purchase exceeding budget
        val exceedingBudgetImpact = viewModel.getBudgetImpactPreview(150.0, "Groceries")
        
        assertTrue(exceedingBudgetImpact.wouldExceedBudget)
        assertEquals(-50.0, exceedingBudgetImpact.remainingAfterPurchase) // 100 - 150
        assertTrue(exceedingBudgetImpact.alternativeCategories.isEmpty()) // No category has $150

        // Step 7: Test spending threshold update
        viewModel.updateSpendingThreshold(75.0)
        val updatedState = viewModel.uiState.first { it.spendingThreshold == 75.0 }
        assertEquals(75.0, updatedState.spendingThreshold)

        // Verify threshold change affects impulse control trigger
        assertFalse(viewModel.shouldShowImpulseControl(60.0, "Groceries")) // Below new threshold
        assertTrue(viewModel.shouldShowImpulseControl(80.0, "Groceries")) // Above new threshold

        // Step 8: Test cooling-off period update
        viewModel.updateCoolingOffPeriod(15)
        val coolingOffState = viewModel.uiState.first { it.coolingOffPeriodSeconds == 15 }
        assertEquals(15, coolingOffState.coolingOffPeriodSeconds)

        // Step 9: Test category-based triggers (always trigger for certain categories)
        assertTrue(viewModel.shouldShowImpulseControl(30.0, "Shopping")) // Below threshold but trigger category
        assertTrue(viewModel.shouldShowImpulseControl(30.0, "Entertainment")) // Below threshold but trigger category
        assertTrue(viewModel.shouldShowImpulseControl(30.0, "Dining")) // Below threshold but trigger category
    }

    @Test
    fun impulseControlDisabled_shouldNotTrigger() = runTest {
        // Set up user preferences with impulse control disabled
        val preferences = UserPreferences(
            id = 1,
            impulseControlEnabled = false,
            spendingThreshold = 50.0
        )
        userPreferencesRepository.insertUserPreferences(preferences)

        viewModel = ImpulseControlViewModel(
            wishlistRepository, purchaseDelayService, userPreferencesRepository, budgetCategoryRepository
        )

        val initialState = viewModel.uiState.first { !it.isLoading }
        assertFalse(initialState.impulseControlEnabled)

        // Should not trigger impulse control even for large amounts
        assertFalse(viewModel.shouldShowImpulseControl(1000.0, "Shopping"))
        assertFalse(viewModel.shouldShowImpulseControl(500.0, "Entertainment"))
    }

    @Test
    fun budgetImpactWithMultipleAlternatives_shouldShowBestOptions() = runTest {
        // Set up multiple budget categories with different available amounts
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val categories = listOf(
            BudgetCategory(
                name = "Groceries",
                allocatedAmount = 300.0,
                spentAmount = 250.0, // $50 remaining
                budgetPeriod = "weekly",
                budgetYear = now.year,
                budgetWeek = now.dayOfYear / 7 + 1
            ),
            BudgetCategory(
                name = "Entertainment",
                allocatedAmount = 200.0,
                spentAmount = 50.0, // $150 remaining
                budgetPeriod = "weekly",
                budgetYear = now.year,
                budgetWeek = now.dayOfYear / 7 + 1
            ),
            BudgetCategory(
                name = "Transportation",
                allocatedAmount = 150.0,
                spentAmount = 25.0, // $125 remaining
                budgetPeriod = "weekly",
                budgetYear = now.year,
                budgetWeek = now.dayOfYear / 7 + 1
            ),
            BudgetCategory(
                name = "Dining Out",
                allocatedAmount = 100.0,
                spentAmount = 80.0, // $20 remaining
                budgetPeriod = "weekly",
                budgetYear = now.year,
                budgetWeek = now.dayOfYear / 7 + 1
            )
        )

        categories.forEach { budgetCategoryRepository.insertBudgetCategory(it) }

        val preferences = UserPreferences(
            id = 1,
            impulseControlEnabled = true,
            spendingThreshold = 50.0,
            budgetPeriod = "weekly"
        )
        userPreferencesRepository.insertUserPreferences(preferences)

        viewModel = ImpulseControlViewModel(
            wishlistRepository, purchaseDelayService, userPreferencesRepository, budgetCategoryRepository
        )

        // Test purchase that exceeds target category but has alternatives
        val impact = viewModel.getBudgetImpactPreview(100.0, "Groceries")
        
        assertTrue(impact.wouldExceedBudget) // Groceries only has $50
        assertEquals(2, impact.alternativeCategories.size) // Entertainment ($150) and Transportation ($125)
        
        // Verify alternatives are sorted by available amount (highest first)
        assertEquals("Entertainment", impact.alternativeCategories[0].name)
        assertEquals("Transportation", impact.alternativeCategories[1].name)
        
        // Dining Out should not be included as it only has $20 remaining
        assertFalse(impact.alternativeCategories.any { it.name == "Dining Out" })
    }

    @Test
    fun totalBudgetImpactCalculation_shouldBeAccurate() = runTest {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val categories = listOf(
            BudgetCategory(
                name = "Groceries",
                allocatedAmount = 300.0,
                spentAmount = 150.0,
                budgetPeriod = "weekly",
                budgetYear = now.year,
                budgetWeek = now.dayOfYear / 7 + 1
            ),
            BudgetCategory(
                name = "Entertainment",
                allocatedAmount = 200.0,
                spentAmount = 75.0,
                budgetPeriod = "weekly",
                budgetYear = now.year,
                budgetWeek = now.dayOfYear / 7 + 1
            )
        )

        categories.forEach { budgetCategoryRepository.insertBudgetCategory(it) }

        val preferences = UserPreferences(
            id = 1,
            impulseControlEnabled = true,
            budgetPeriod = "weekly"
        )
        userPreferencesRepository.insertUserPreferences(preferences)

        viewModel = ImpulseControlViewModel(
            wishlistRepository, purchaseDelayService, userPreferencesRepository, budgetCategoryRepository
        )

        val impact = viewModel.getBudgetImpactPreview(50.0, "Groceries")
        
        // Total allocated: 300 + 200 = 500
        assertEquals(500.0, impact.totalBudgetImpact.totalAllocated)
        
        // Total spent after purchase: 150 + 75 + 50 = 275
        assertEquals(275.0, impact.totalBudgetImpact.totalSpent)
        
        // Total remaining after purchase: 500 - 275 = 225
        assertEquals(225.0, impact.totalBudgetImpact.totalRemaining)
    }
}
