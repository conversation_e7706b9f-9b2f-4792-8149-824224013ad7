package com.focusflow.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.focusflow.ui.viewmodel.PayoffPlannerViewModel
import com.focusflow.ui.viewmodel.PayoffStrategy
import com.focusflow.ui.components.*
import com.focusflow.data.model.PayoffComparison
import java.text.NumberFormat
import java.util.*

@Composable
fun PayoffPlannerScreen(
    onNavigateBack: () -> Unit,
    viewModel: PayoffPlannerViewModel = hiltViewModel()
) {
    val creditCards by viewModel.allCreditCards.collectAsStateWithLifecycle(initialValue = emptyList())
    val currentPlan by viewModel.currentPayoffPlan.collectAsStateWithLifecycle(initialValue = null)
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    var extraPaymentInput by remember { mutableStateOf("") }
    var showCreatePlanDialog by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        if (uiState.avalancheComparison == null && uiState.snowballComparison == null) {
            viewModel.generatePayoffComparison(0.0)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onNavigateBack) {
                Icon(Icons.Default.ArrowBack, contentDescription = "Back")
            }
            Text(
                text = "Debt Payoff Planner",
                style = MaterialTheme.typography.h5,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Center
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        if (uiState.isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
            return
        }

        if (creditCards.isEmpty()) {
            EmptyStateCard()
            return
        }

        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Current Debt Overview
            item {
                DebtOverviewCard(
                    totalDebt = uiState.totalDebt,
                    minimumPayments = uiState.totalMinimumPayments,
                    availableForDebt = uiState.availableForDebtPayment
                )
            }

            // Extra Payment Input
            item {
                ExtraPaymentInputCard(
                    currentAmount = extraPaymentInput,
                    onAmountChanged = { 
                        extraPaymentInput = it
                        it.toDoubleOrNull()?.let { amount ->
                            viewModel.updateExtraPayment(amount)
                            viewModel.generatePayoffComparison(amount)
                        }
                    },
                    availableAmount = uiState.availableForDebtPayment
                )
            }

            // Strategy Comparison
            if (uiState.avalancheComparison != null && uiState.snowballComparison != null) {
                item {
                    Text(
                        text = "Strategy Comparison",
                        style = MaterialTheme.typography.h6,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(vertical = 8.dp)
                    )
                }

                item {
                    StrategyComparisonCard(
                        avalanche = uiState.avalancheComparison!!,
                        snowball = uiState.snowballComparison!!,
                        selectedStrategy = uiState.selectedStrategy,
                        onStrategySelected = viewModel::selectStrategy
                    )
                }
            }

            // Create Plan Button
            if (uiState.selectedStrategy != null) {
                item {
                    Button(
                        onClick = { showCreatePlanDialog = true },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp),
                        colors = ButtonDefaults.buttonColors(
                            backgroundColor = Color(0xFF4CAF50)
                        ),
                        enabled = !uiState.isCreatingPlan
                    ) {
                        if (uiState.isCreatingPlan) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(20.dp),
                                color = Color.White
                            )
                        } else {
                            Icon(Icons.Default.Add, contentDescription = null)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "Create ${uiState.selectedStrategy?.name?.lowercase()?.replaceFirstChar { it.uppercase() }} Plan",
                                style = MaterialTheme.typography.button,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }
                }
            }

            // Current Active Plan (if exists)
            currentPlan?.let { plan ->
                item {
                    CurrentPlanCard(plan = plan)
                }
            }
        }

        // Error handling
        uiState.error?.let { error ->
            LaunchedEffect(error) {
                // Show snackbar or handle error
            }
        }
    }

    // Create Plan Dialog
    if (showCreatePlanDialog) {
        CreatePlanDialog(
            strategy = uiState.selectedStrategy!!,
            onDismiss = { showCreatePlanDialog = false },
            onCreatePlan = { planName ->
                viewModel.createPayoffPlan(
                    strategy = uiState.selectedStrategy!!,
                    extraPayment = uiState.extraPaymentAmount,
                    planName = planName
                )
                showCreatePlanDialog = false
            }
        )
    }
}

@Composable
fun DebtOverviewCard(
    totalDebt: Double,
    minimumPayments: Double,
    availableForDebt: Double
) {
    ADHDFriendlyCard(
        title = "Your Debt Overview",
        backgroundColor = Color(0xFFFFF3E0)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            DebtMetricColumn(
                label = "Total Debt",
                amount = totalDebt,
                color = Color(0xFFFF5722)
            )
            DebtMetricColumn(
                label = "Min. Payments",
                amount = minimumPayments,
                color = Color(0xFFFF9800)
            )
            DebtMetricColumn(
                label = "Available",
                amount = availableForDebt,
                color = Color(0xFF4CAF50)
            )
        }
    }
}

@Composable
fun DebtMetricColumn(
    label: String,
    amount: Double,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.caption,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
        )
        Text(
            text = NumberFormat.getCurrencyInstance(Locale.US).format(amount),
            style = MaterialTheme.typography.h6,
            fontWeight = FontWeight.Bold,
            color = color
        )
    }
}

@Composable
fun ExtraPaymentInputCard(
    currentAmount: String,
    onAmountChanged: (String) -> Unit,
    availableAmount: Double
) {
    ADHDFriendlyCard(
        title = "Extra Payment Amount",
        backgroundColor = Color(0xFFE8F5E8)
    ) {
        Column {
            OutlinedTextField(
                value = currentAmount,
                onValueChange = onAmountChanged,
                label = { Text("Extra Monthly Payment") },
                placeholder = { Text("0.00") },
                leadingIcon = { Text("$") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Available from budget: ${NumberFormat.getCurrencyInstance(Locale.US).format(availableAmount)}",
                style = MaterialTheme.typography.caption,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
            
            if (availableAmount > 0) {
                TextButton(
                    onClick = { onAmountChanged(availableAmount.toString()) }
                ) {
                    Text("Use Available Amount")
                }
            }
        }
    }
}

@Composable
fun StrategyComparisonCard(
    avalanche: PayoffComparison,
    snowball: PayoffComparison,
    selectedStrategy: PayoffStrategy?,
    onStrategySelected: (PayoffStrategy) -> Unit
) {
    ADHDFriendlyCard(
        title = "Choose Your Strategy",
        backgroundColor = Color(0xFFF3E5F5)
    ) {
        Column {
            // Avalanche Strategy
            StrategyOptionCard(
                strategy = PayoffStrategy.AVALANCHE,
                comparison = avalanche,
                isSelected = selectedStrategy == PayoffStrategy.AVALANCHE,
                onSelected = onStrategySelected,
                title = "Avalanche Method",
                description = "Pay highest interest rates first - saves the most money",
                icon = Icons.Default.TrendingUp
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Snowball Strategy
            StrategyOptionCard(
                strategy = PayoffStrategy.SNOWBALL,
                comparison = snowball,
                isSelected = selectedStrategy == PayoffStrategy.SNOWBALL,
                onSelected = onStrategySelected,
                title = "Snowball Method",
                description = "Pay smallest balances first - builds momentum",
                icon = Icons.Default.TrendingUp
            )
        }
    }
}

@Composable
fun EmptyStateCard() {
    ADHDFriendlyCard(
        title = "No Credit Cards Found",
        backgroundColor = Color(0xFFFFF3E0)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(24.dp)
        ) {
            Icon(
                imageVector = Icons.Default.CreditCard,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colors.onSurface.copy(alpha = 0.5f)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "Add your credit cards first to create a payoff plan",
                style = MaterialTheme.typography.body1,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}
