package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.FocusSession;
import java.lang.Boolean;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDateTime;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class FocusSessionDao_Impl implements FocusSessionDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<FocusSession> __insertionAdapterOfFocusSession;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<FocusSession> __deletionAdapterOfFocusSession;

  private final EntityDeletionOrUpdateAdapter<FocusSession> __updateAdapterOfFocusSession;

  private final SharedSQLiteStatement __preparedStmtOfCompleteFocusSession;

  private final SharedSQLiteStatement __preparedStmtOfRecordInterruption;

  private final SharedSQLiteStatement __preparedStmtOfRecordBreak;

  private final SharedSQLiteStatement __preparedStmtOfUpdateSessionRatings;

  private final SharedSQLiteStatement __preparedStmtOfIncrementPomodoroCount;

  private final SharedSQLiteStatement __preparedStmtOfDeleteFocusSessionsOlderThan;

  public FocusSessionDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfFocusSession = new EntityInsertionAdapter<FocusSession>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `focus_sessions` (`id`,`taskType`,`sessionName`,`plannedDurationMinutes`,`actualDurationMinutes`,`startTime`,`endTime`,`isCompleted`,`wasInterrupted`,`interruptionCount`,`breaksTaken`,`focusQuality`,`productivityScore`,`notes`,`tasksCompleted`,`distractions`,`sessionGoal`,`goalAchieved`,`energyLevelBefore`,`energyLevelAfter`,`moodBefore`,`moodAfter`,`pomodoroCount`,`sessionType`,`backgroundSound`,`isSuccessful`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final FocusSession entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getTaskType() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getTaskType());
        }
        if (entity.getSessionName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getSessionName());
        }
        statement.bindLong(4, entity.getPlannedDurationMinutes());
        if (entity.getActualDurationMinutes() == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, entity.getActualDurationMinutes());
        }
        final String _tmp = __converters.fromLocalDateTime(entity.getStartTime());
        if (_tmp == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, _tmp);
        }
        final String _tmp_1 = __converters.fromLocalDateTime(entity.getEndTime());
        if (_tmp_1 == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp_1);
        }
        final int _tmp_2 = entity.isCompleted() ? 1 : 0;
        statement.bindLong(8, _tmp_2);
        final int _tmp_3 = entity.getWasInterrupted() ? 1 : 0;
        statement.bindLong(9, _tmp_3);
        statement.bindLong(10, entity.getInterruptionCount());
        statement.bindLong(11, entity.getBreaksTaken());
        if (entity.getFocusQuality() == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, entity.getFocusQuality());
        }
        if (entity.getProductivityScore() == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, entity.getProductivityScore());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getNotes());
        }
        if (entity.getTasksCompleted() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getTasksCompleted());
        }
        if (entity.getDistractions() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getDistractions());
        }
        if (entity.getSessionGoal() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getSessionGoal());
        }
        final Integer _tmp_4 = entity.getGoalAchieved() == null ? null : (entity.getGoalAchieved() ? 1 : 0);
        if (_tmp_4 == null) {
          statement.bindNull(18);
        } else {
          statement.bindLong(18, _tmp_4);
        }
        if (entity.getEnergyLevelBefore() == null) {
          statement.bindNull(19);
        } else {
          statement.bindLong(19, entity.getEnergyLevelBefore());
        }
        if (entity.getEnergyLevelAfter() == null) {
          statement.bindNull(20);
        } else {
          statement.bindLong(20, entity.getEnergyLevelAfter());
        }
        if (entity.getMoodBefore() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getMoodBefore());
        }
        if (entity.getMoodAfter() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getMoodAfter());
        }
        statement.bindLong(23, entity.getPomodoroCount());
        if (entity.getSessionType() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getSessionType());
        }
        if (entity.getBackgroundSound() == null) {
          statement.bindNull(25);
        } else {
          statement.bindString(25, entity.getBackgroundSound());
        }
        final Integer _tmp_5 = entity.isSuccessful() == null ? null : (entity.isSuccessful() ? 1 : 0);
        if (_tmp_5 == null) {
          statement.bindNull(26);
        } else {
          statement.bindLong(26, _tmp_5);
        }
      }
    };
    this.__deletionAdapterOfFocusSession = new EntityDeletionOrUpdateAdapter<FocusSession>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `focus_sessions` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final FocusSession entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfFocusSession = new EntityDeletionOrUpdateAdapter<FocusSession>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `focus_sessions` SET `id` = ?,`taskType` = ?,`sessionName` = ?,`plannedDurationMinutes` = ?,`actualDurationMinutes` = ?,`startTime` = ?,`endTime` = ?,`isCompleted` = ?,`wasInterrupted` = ?,`interruptionCount` = ?,`breaksTaken` = ?,`focusQuality` = ?,`productivityScore` = ?,`notes` = ?,`tasksCompleted` = ?,`distractions` = ?,`sessionGoal` = ?,`goalAchieved` = ?,`energyLevelBefore` = ?,`energyLevelAfter` = ?,`moodBefore` = ?,`moodAfter` = ?,`pomodoroCount` = ?,`sessionType` = ?,`backgroundSound` = ?,`isSuccessful` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final FocusSession entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getTaskType() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getTaskType());
        }
        if (entity.getSessionName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getSessionName());
        }
        statement.bindLong(4, entity.getPlannedDurationMinutes());
        if (entity.getActualDurationMinutes() == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, entity.getActualDurationMinutes());
        }
        final String _tmp = __converters.fromLocalDateTime(entity.getStartTime());
        if (_tmp == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, _tmp);
        }
        final String _tmp_1 = __converters.fromLocalDateTime(entity.getEndTime());
        if (_tmp_1 == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp_1);
        }
        final int _tmp_2 = entity.isCompleted() ? 1 : 0;
        statement.bindLong(8, _tmp_2);
        final int _tmp_3 = entity.getWasInterrupted() ? 1 : 0;
        statement.bindLong(9, _tmp_3);
        statement.bindLong(10, entity.getInterruptionCount());
        statement.bindLong(11, entity.getBreaksTaken());
        if (entity.getFocusQuality() == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, entity.getFocusQuality());
        }
        if (entity.getProductivityScore() == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, entity.getProductivityScore());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getNotes());
        }
        if (entity.getTasksCompleted() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getTasksCompleted());
        }
        if (entity.getDistractions() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getDistractions());
        }
        if (entity.getSessionGoal() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getSessionGoal());
        }
        final Integer _tmp_4 = entity.getGoalAchieved() == null ? null : (entity.getGoalAchieved() ? 1 : 0);
        if (_tmp_4 == null) {
          statement.bindNull(18);
        } else {
          statement.bindLong(18, _tmp_4);
        }
        if (entity.getEnergyLevelBefore() == null) {
          statement.bindNull(19);
        } else {
          statement.bindLong(19, entity.getEnergyLevelBefore());
        }
        if (entity.getEnergyLevelAfter() == null) {
          statement.bindNull(20);
        } else {
          statement.bindLong(20, entity.getEnergyLevelAfter());
        }
        if (entity.getMoodBefore() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getMoodBefore());
        }
        if (entity.getMoodAfter() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getMoodAfter());
        }
        statement.bindLong(23, entity.getPomodoroCount());
        if (entity.getSessionType() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getSessionType());
        }
        if (entity.getBackgroundSound() == null) {
          statement.bindNull(25);
        } else {
          statement.bindString(25, entity.getBackgroundSound());
        }
        final Integer _tmp_5 = entity.isSuccessful() == null ? null : (entity.isSuccessful() ? 1 : 0);
        if (_tmp_5 == null) {
          statement.bindNull(26);
        } else {
          statement.bindLong(26, _tmp_5);
        }
        statement.bindLong(27, entity.getId());
      }
    };
    this.__preparedStmtOfCompleteFocusSession = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE focus_sessions SET endTime = ?, actualDurationMinutes = ?, isCompleted = 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfRecordInterruption = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE focus_sessions SET wasInterrupted = 1, interruptionCount = interruptionCount + 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfRecordBreak = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE focus_sessions SET breaksTaken = breaksTaken + 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateSessionRatings = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE focus_sessions SET focusQuality = ?, productivityScore = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfIncrementPomodoroCount = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE focus_sessions SET pomodoroCount = pomodoroCount + 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteFocusSessionsOlderThan = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM focus_sessions WHERE startTime < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertFocusSession(final FocusSession focusSession,
      final Continuation<? super Long> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfFocusSession.insertAndReturnId(focusSession);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object deleteFocusSession(final FocusSession focusSession,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfFocusSession.handle(focusSession);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object updateFocusSession(final FocusSession focusSession,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfFocusSession.handle(focusSession);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object completeFocusSession(final long id, final LocalDateTime endTime, final int duration,
      final Continuation<? super Unit> arg3) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfCompleteFocusSession.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(endTime);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, duration);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfCompleteFocusSession.release(_stmt);
        }
      }
    }, arg3);
  }

  @Override
  public Object recordInterruption(final long id, final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfRecordInterruption.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfRecordInterruption.release(_stmt);
        }
      }
    }, arg1);
  }

  @Override
  public Object recordBreak(final long id, final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfRecordBreak.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfRecordBreak.release(_stmt);
        }
      }
    }, arg1);
  }

  @Override
  public Object updateSessionRatings(final long id, final Integer quality,
      final Integer productivity, final Continuation<? super Unit> arg3) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateSessionRatings.acquire();
        int _argIndex = 1;
        if (quality == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindLong(_argIndex, quality);
        }
        _argIndex = 2;
        if (productivity == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindLong(_argIndex, productivity);
        }
        _argIndex = 3;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateSessionRatings.release(_stmt);
        }
      }
    }, arg3);
  }

  @Override
  public Object incrementPomodoroCount(final long id, final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfIncrementPomodoroCount.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfIncrementPomodoroCount.release(_stmt);
        }
      }
    }, arg1);
  }

  @Override
  public Object deleteFocusSessionsOlderThan(final LocalDateTime cutoffDate,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteFocusSessionsOlderThan.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(cutoffDate);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteFocusSessionsOlderThan.release(_stmt);
        }
      }
    }, arg1);
  }

  @Override
  public Flow<List<FocusSession>> getAllFocusSessions() {
    final String _sql = "SELECT `focus_sessions`.`id` AS `id`, `focus_sessions`.`taskType` AS `taskType`, `focus_sessions`.`sessionName` AS `sessionName`, `focus_sessions`.`plannedDurationMinutes` AS `plannedDurationMinutes`, `focus_sessions`.`actualDurationMinutes` AS `actualDurationMinutes`, `focus_sessions`.`startTime` AS `startTime`, `focus_sessions`.`endTime` AS `endTime`, `focus_sessions`.`isCompleted` AS `isCompleted`, `focus_sessions`.`wasInterrupted` AS `wasInterrupted`, `focus_sessions`.`interruptionCount` AS `interruptionCount`, `focus_sessions`.`breaksTaken` AS `breaksTaken`, `focus_sessions`.`focusQuality` AS `focusQuality`, `focus_sessions`.`productivityScore` AS `productivityScore`, `focus_sessions`.`notes` AS `notes`, `focus_sessions`.`tasksCompleted` AS `tasksCompleted`, `focus_sessions`.`distractions` AS `distractions`, `focus_sessions`.`sessionGoal` AS `sessionGoal`, `focus_sessions`.`goalAchieved` AS `goalAchieved`, `focus_sessions`.`energyLevelBefore` AS `energyLevelBefore`, `focus_sessions`.`energyLevelAfter` AS `energyLevelAfter`, `focus_sessions`.`moodBefore` AS `moodBefore`, `focus_sessions`.`moodAfter` AS `moodAfter`, `focus_sessions`.`pomodoroCount` AS `pomodoroCount`, `focus_sessions`.`sessionType` AS `sessionType`, `focus_sessions`.`backgroundSound` AS `backgroundSound`, `focus_sessions`.`isSuccessful` AS `isSuccessful` FROM focus_sessions ORDER BY startTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"focus_sessions"}, new Callable<List<FocusSession>>() {
      @Override
      @NonNull
      public List<FocusSession> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfTaskType = 1;
          final int _cursorIndexOfSessionName = 2;
          final int _cursorIndexOfPlannedDurationMinutes = 3;
          final int _cursorIndexOfActualDurationMinutes = 4;
          final int _cursorIndexOfStartTime = 5;
          final int _cursorIndexOfEndTime = 6;
          final int _cursorIndexOfIsCompleted = 7;
          final int _cursorIndexOfWasInterrupted = 8;
          final int _cursorIndexOfInterruptionCount = 9;
          final int _cursorIndexOfBreaksTaken = 10;
          final int _cursorIndexOfFocusQuality = 11;
          final int _cursorIndexOfProductivityScore = 12;
          final int _cursorIndexOfNotes = 13;
          final int _cursorIndexOfTasksCompleted = 14;
          final int _cursorIndexOfDistractions = 15;
          final int _cursorIndexOfSessionGoal = 16;
          final int _cursorIndexOfGoalAchieved = 17;
          final int _cursorIndexOfEnergyLevelBefore = 18;
          final int _cursorIndexOfEnergyLevelAfter = 19;
          final int _cursorIndexOfMoodBefore = 20;
          final int _cursorIndexOfMoodAfter = 21;
          final int _cursorIndexOfPomodoroCount = 22;
          final int _cursorIndexOfSessionType = 23;
          final int _cursorIndexOfBackgroundSound = 24;
          final int _cursorIndexOfIsSuccessful = 25;
          final List<FocusSession> _result = new ArrayList<FocusSession>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final FocusSession _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpTaskType;
            if (_cursor.isNull(_cursorIndexOfTaskType)) {
              _tmpTaskType = null;
            } else {
              _tmpTaskType = _cursor.getString(_cursorIndexOfTaskType);
            }
            final String _tmpSessionName;
            if (_cursor.isNull(_cursorIndexOfSessionName)) {
              _tmpSessionName = null;
            } else {
              _tmpSessionName = _cursor.getString(_cursorIndexOfSessionName);
            }
            final int _tmpPlannedDurationMinutes;
            _tmpPlannedDurationMinutes = _cursor.getInt(_cursorIndexOfPlannedDurationMinutes);
            final Integer _tmpActualDurationMinutes;
            if (_cursor.isNull(_cursorIndexOfActualDurationMinutes)) {
              _tmpActualDurationMinutes = null;
            } else {
              _tmpActualDurationMinutes = _cursor.getInt(_cursorIndexOfActualDurationMinutes);
            }
            final LocalDateTime _tmpStartTime;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStartTime);
            }
            _tmpStartTime = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpEndTime;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfEndTime);
            }
            _tmpEndTime = __converters.toLocalDateTime(_tmp_1);
            final boolean _tmpIsCompleted;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_2 != 0;
            final boolean _tmpWasInterrupted;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfWasInterrupted);
            _tmpWasInterrupted = _tmp_3 != 0;
            final int _tmpInterruptionCount;
            _tmpInterruptionCount = _cursor.getInt(_cursorIndexOfInterruptionCount);
            final int _tmpBreaksTaken;
            _tmpBreaksTaken = _cursor.getInt(_cursorIndexOfBreaksTaken);
            final Integer _tmpFocusQuality;
            if (_cursor.isNull(_cursorIndexOfFocusQuality)) {
              _tmpFocusQuality = null;
            } else {
              _tmpFocusQuality = _cursor.getInt(_cursorIndexOfFocusQuality);
            }
            final Integer _tmpProductivityScore;
            if (_cursor.isNull(_cursorIndexOfProductivityScore)) {
              _tmpProductivityScore = null;
            } else {
              _tmpProductivityScore = _cursor.getInt(_cursorIndexOfProductivityScore);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpTasksCompleted;
            if (_cursor.isNull(_cursorIndexOfTasksCompleted)) {
              _tmpTasksCompleted = null;
            } else {
              _tmpTasksCompleted = _cursor.getString(_cursorIndexOfTasksCompleted);
            }
            final String _tmpDistractions;
            if (_cursor.isNull(_cursorIndexOfDistractions)) {
              _tmpDistractions = null;
            } else {
              _tmpDistractions = _cursor.getString(_cursorIndexOfDistractions);
            }
            final String _tmpSessionGoal;
            if (_cursor.isNull(_cursorIndexOfSessionGoal)) {
              _tmpSessionGoal = null;
            } else {
              _tmpSessionGoal = _cursor.getString(_cursorIndexOfSessionGoal);
            }
            final Boolean _tmpGoalAchieved;
            final Integer _tmp_4;
            if (_cursor.isNull(_cursorIndexOfGoalAchieved)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getInt(_cursorIndexOfGoalAchieved);
            }
            _tmpGoalAchieved = _tmp_4 == null ? null : _tmp_4 != 0;
            final Integer _tmpEnergyLevelBefore;
            if (_cursor.isNull(_cursorIndexOfEnergyLevelBefore)) {
              _tmpEnergyLevelBefore = null;
            } else {
              _tmpEnergyLevelBefore = _cursor.getInt(_cursorIndexOfEnergyLevelBefore);
            }
            final Integer _tmpEnergyLevelAfter;
            if (_cursor.isNull(_cursorIndexOfEnergyLevelAfter)) {
              _tmpEnergyLevelAfter = null;
            } else {
              _tmpEnergyLevelAfter = _cursor.getInt(_cursorIndexOfEnergyLevelAfter);
            }
            final String _tmpMoodBefore;
            if (_cursor.isNull(_cursorIndexOfMoodBefore)) {
              _tmpMoodBefore = null;
            } else {
              _tmpMoodBefore = _cursor.getString(_cursorIndexOfMoodBefore);
            }
            final String _tmpMoodAfter;
            if (_cursor.isNull(_cursorIndexOfMoodAfter)) {
              _tmpMoodAfter = null;
            } else {
              _tmpMoodAfter = _cursor.getString(_cursorIndexOfMoodAfter);
            }
            final int _tmpPomodoroCount;
            _tmpPomodoroCount = _cursor.getInt(_cursorIndexOfPomodoroCount);
            final String _tmpSessionType;
            if (_cursor.isNull(_cursorIndexOfSessionType)) {
              _tmpSessionType = null;
            } else {
              _tmpSessionType = _cursor.getString(_cursorIndexOfSessionType);
            }
            final String _tmpBackgroundSound;
            if (_cursor.isNull(_cursorIndexOfBackgroundSound)) {
              _tmpBackgroundSound = null;
            } else {
              _tmpBackgroundSound = _cursor.getString(_cursorIndexOfBackgroundSound);
            }
            final Boolean _tmpIsSuccessful;
            final Integer _tmp_5;
            if (_cursor.isNull(_cursorIndexOfIsSuccessful)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getInt(_cursorIndexOfIsSuccessful);
            }
            _tmpIsSuccessful = _tmp_5 == null ? null : _tmp_5 != 0;
            _item = new FocusSession(_tmpId,_tmpTaskType,_tmpSessionName,_tmpPlannedDurationMinutes,_tmpActualDurationMinutes,_tmpStartTime,_tmpEndTime,_tmpIsCompleted,_tmpWasInterrupted,_tmpInterruptionCount,_tmpBreaksTaken,_tmpFocusQuality,_tmpProductivityScore,_tmpNotes,_tmpTasksCompleted,_tmpDistractions,_tmpSessionGoal,_tmpGoalAchieved,_tmpEnergyLevelBefore,_tmpEnergyLevelAfter,_tmpMoodBefore,_tmpMoodAfter,_tmpPomodoroCount,_tmpSessionType,_tmpBackgroundSound,_tmpIsSuccessful);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<FocusSession>> getFocusSessionsByTaskType(final String taskType) {
    final String _sql = "SELECT * FROM focus_sessions WHERE taskType = ? ORDER BY startTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (taskType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, taskType);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"focus_sessions"}, new Callable<List<FocusSession>>() {
      @Override
      @NonNull
      public List<FocusSession> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTaskType = CursorUtil.getColumnIndexOrThrow(_cursor, "taskType");
          final int _cursorIndexOfSessionName = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionName");
          final int _cursorIndexOfPlannedDurationMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "plannedDurationMinutes");
          final int _cursorIndexOfActualDurationMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "actualDurationMinutes");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfWasInterrupted = CursorUtil.getColumnIndexOrThrow(_cursor, "wasInterrupted");
          final int _cursorIndexOfInterruptionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "interruptionCount");
          final int _cursorIndexOfBreaksTaken = CursorUtil.getColumnIndexOrThrow(_cursor, "breaksTaken");
          final int _cursorIndexOfFocusQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "focusQuality");
          final int _cursorIndexOfProductivityScore = CursorUtil.getColumnIndexOrThrow(_cursor, "productivityScore");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfTasksCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "tasksCompleted");
          final int _cursorIndexOfDistractions = CursorUtil.getColumnIndexOrThrow(_cursor, "distractions");
          final int _cursorIndexOfSessionGoal = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionGoal");
          final int _cursorIndexOfGoalAchieved = CursorUtil.getColumnIndexOrThrow(_cursor, "goalAchieved");
          final int _cursorIndexOfEnergyLevelBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "energyLevelBefore");
          final int _cursorIndexOfEnergyLevelAfter = CursorUtil.getColumnIndexOrThrow(_cursor, "energyLevelAfter");
          final int _cursorIndexOfMoodBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "moodBefore");
          final int _cursorIndexOfMoodAfter = CursorUtil.getColumnIndexOrThrow(_cursor, "moodAfter");
          final int _cursorIndexOfPomodoroCount = CursorUtil.getColumnIndexOrThrow(_cursor, "pomodoroCount");
          final int _cursorIndexOfSessionType = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionType");
          final int _cursorIndexOfBackgroundSound = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundSound");
          final int _cursorIndexOfIsSuccessful = CursorUtil.getColumnIndexOrThrow(_cursor, "isSuccessful");
          final List<FocusSession> _result = new ArrayList<FocusSession>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final FocusSession _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpTaskType;
            if (_cursor.isNull(_cursorIndexOfTaskType)) {
              _tmpTaskType = null;
            } else {
              _tmpTaskType = _cursor.getString(_cursorIndexOfTaskType);
            }
            final String _tmpSessionName;
            if (_cursor.isNull(_cursorIndexOfSessionName)) {
              _tmpSessionName = null;
            } else {
              _tmpSessionName = _cursor.getString(_cursorIndexOfSessionName);
            }
            final int _tmpPlannedDurationMinutes;
            _tmpPlannedDurationMinutes = _cursor.getInt(_cursorIndexOfPlannedDurationMinutes);
            final Integer _tmpActualDurationMinutes;
            if (_cursor.isNull(_cursorIndexOfActualDurationMinutes)) {
              _tmpActualDurationMinutes = null;
            } else {
              _tmpActualDurationMinutes = _cursor.getInt(_cursorIndexOfActualDurationMinutes);
            }
            final LocalDateTime _tmpStartTime;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStartTime);
            }
            _tmpStartTime = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpEndTime;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfEndTime);
            }
            _tmpEndTime = __converters.toLocalDateTime(_tmp_1);
            final boolean _tmpIsCompleted;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_2 != 0;
            final boolean _tmpWasInterrupted;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfWasInterrupted);
            _tmpWasInterrupted = _tmp_3 != 0;
            final int _tmpInterruptionCount;
            _tmpInterruptionCount = _cursor.getInt(_cursorIndexOfInterruptionCount);
            final int _tmpBreaksTaken;
            _tmpBreaksTaken = _cursor.getInt(_cursorIndexOfBreaksTaken);
            final Integer _tmpFocusQuality;
            if (_cursor.isNull(_cursorIndexOfFocusQuality)) {
              _tmpFocusQuality = null;
            } else {
              _tmpFocusQuality = _cursor.getInt(_cursorIndexOfFocusQuality);
            }
            final Integer _tmpProductivityScore;
            if (_cursor.isNull(_cursorIndexOfProductivityScore)) {
              _tmpProductivityScore = null;
            } else {
              _tmpProductivityScore = _cursor.getInt(_cursorIndexOfProductivityScore);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpTasksCompleted;
            if (_cursor.isNull(_cursorIndexOfTasksCompleted)) {
              _tmpTasksCompleted = null;
            } else {
              _tmpTasksCompleted = _cursor.getString(_cursorIndexOfTasksCompleted);
            }
            final String _tmpDistractions;
            if (_cursor.isNull(_cursorIndexOfDistractions)) {
              _tmpDistractions = null;
            } else {
              _tmpDistractions = _cursor.getString(_cursorIndexOfDistractions);
            }
            final String _tmpSessionGoal;
            if (_cursor.isNull(_cursorIndexOfSessionGoal)) {
              _tmpSessionGoal = null;
            } else {
              _tmpSessionGoal = _cursor.getString(_cursorIndexOfSessionGoal);
            }
            final Boolean _tmpGoalAchieved;
            final Integer _tmp_4;
            if (_cursor.isNull(_cursorIndexOfGoalAchieved)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getInt(_cursorIndexOfGoalAchieved);
            }
            _tmpGoalAchieved = _tmp_4 == null ? null : _tmp_4 != 0;
            final Integer _tmpEnergyLevelBefore;
            if (_cursor.isNull(_cursorIndexOfEnergyLevelBefore)) {
              _tmpEnergyLevelBefore = null;
            } else {
              _tmpEnergyLevelBefore = _cursor.getInt(_cursorIndexOfEnergyLevelBefore);
            }
            final Integer _tmpEnergyLevelAfter;
            if (_cursor.isNull(_cursorIndexOfEnergyLevelAfter)) {
              _tmpEnergyLevelAfter = null;
            } else {
              _tmpEnergyLevelAfter = _cursor.getInt(_cursorIndexOfEnergyLevelAfter);
            }
            final String _tmpMoodBefore;
            if (_cursor.isNull(_cursorIndexOfMoodBefore)) {
              _tmpMoodBefore = null;
            } else {
              _tmpMoodBefore = _cursor.getString(_cursorIndexOfMoodBefore);
            }
            final String _tmpMoodAfter;
            if (_cursor.isNull(_cursorIndexOfMoodAfter)) {
              _tmpMoodAfter = null;
            } else {
              _tmpMoodAfter = _cursor.getString(_cursorIndexOfMoodAfter);
            }
            final int _tmpPomodoroCount;
            _tmpPomodoroCount = _cursor.getInt(_cursorIndexOfPomodoroCount);
            final String _tmpSessionType;
            if (_cursor.isNull(_cursorIndexOfSessionType)) {
              _tmpSessionType = null;
            } else {
              _tmpSessionType = _cursor.getString(_cursorIndexOfSessionType);
            }
            final String _tmpBackgroundSound;
            if (_cursor.isNull(_cursorIndexOfBackgroundSound)) {
              _tmpBackgroundSound = null;
            } else {
              _tmpBackgroundSound = _cursor.getString(_cursorIndexOfBackgroundSound);
            }
            final Boolean _tmpIsSuccessful;
            final Integer _tmp_5;
            if (_cursor.isNull(_cursorIndexOfIsSuccessful)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getInt(_cursorIndexOfIsSuccessful);
            }
            _tmpIsSuccessful = _tmp_5 == null ? null : _tmp_5 != 0;
            _item = new FocusSession(_tmpId,_tmpTaskType,_tmpSessionName,_tmpPlannedDurationMinutes,_tmpActualDurationMinutes,_tmpStartTime,_tmpEndTime,_tmpIsCompleted,_tmpWasInterrupted,_tmpInterruptionCount,_tmpBreaksTaken,_tmpFocusQuality,_tmpProductivityScore,_tmpNotes,_tmpTasksCompleted,_tmpDistractions,_tmpSessionGoal,_tmpGoalAchieved,_tmpEnergyLevelBefore,_tmpEnergyLevelAfter,_tmpMoodBefore,_tmpMoodAfter,_tmpPomodoroCount,_tmpSessionType,_tmpBackgroundSound,_tmpIsSuccessful);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<FocusSession>> getCompletedFocusSessions() {
    final String _sql = "SELECT `focus_sessions`.`id` AS `id`, `focus_sessions`.`taskType` AS `taskType`, `focus_sessions`.`sessionName` AS `sessionName`, `focus_sessions`.`plannedDurationMinutes` AS `plannedDurationMinutes`, `focus_sessions`.`actualDurationMinutes` AS `actualDurationMinutes`, `focus_sessions`.`startTime` AS `startTime`, `focus_sessions`.`endTime` AS `endTime`, `focus_sessions`.`isCompleted` AS `isCompleted`, `focus_sessions`.`wasInterrupted` AS `wasInterrupted`, `focus_sessions`.`interruptionCount` AS `interruptionCount`, `focus_sessions`.`breaksTaken` AS `breaksTaken`, `focus_sessions`.`focusQuality` AS `focusQuality`, `focus_sessions`.`productivityScore` AS `productivityScore`, `focus_sessions`.`notes` AS `notes`, `focus_sessions`.`tasksCompleted` AS `tasksCompleted`, `focus_sessions`.`distractions` AS `distractions`, `focus_sessions`.`sessionGoal` AS `sessionGoal`, `focus_sessions`.`goalAchieved` AS `goalAchieved`, `focus_sessions`.`energyLevelBefore` AS `energyLevelBefore`, `focus_sessions`.`energyLevelAfter` AS `energyLevelAfter`, `focus_sessions`.`moodBefore` AS `moodBefore`, `focus_sessions`.`moodAfter` AS `moodAfter`, `focus_sessions`.`pomodoroCount` AS `pomodoroCount`, `focus_sessions`.`sessionType` AS `sessionType`, `focus_sessions`.`backgroundSound` AS `backgroundSound`, `focus_sessions`.`isSuccessful` AS `isSuccessful` FROM focus_sessions WHERE isCompleted = 1 ORDER BY startTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"focus_sessions"}, new Callable<List<FocusSession>>() {
      @Override
      @NonNull
      public List<FocusSession> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfTaskType = 1;
          final int _cursorIndexOfSessionName = 2;
          final int _cursorIndexOfPlannedDurationMinutes = 3;
          final int _cursorIndexOfActualDurationMinutes = 4;
          final int _cursorIndexOfStartTime = 5;
          final int _cursorIndexOfEndTime = 6;
          final int _cursorIndexOfIsCompleted = 7;
          final int _cursorIndexOfWasInterrupted = 8;
          final int _cursorIndexOfInterruptionCount = 9;
          final int _cursorIndexOfBreaksTaken = 10;
          final int _cursorIndexOfFocusQuality = 11;
          final int _cursorIndexOfProductivityScore = 12;
          final int _cursorIndexOfNotes = 13;
          final int _cursorIndexOfTasksCompleted = 14;
          final int _cursorIndexOfDistractions = 15;
          final int _cursorIndexOfSessionGoal = 16;
          final int _cursorIndexOfGoalAchieved = 17;
          final int _cursorIndexOfEnergyLevelBefore = 18;
          final int _cursorIndexOfEnergyLevelAfter = 19;
          final int _cursorIndexOfMoodBefore = 20;
          final int _cursorIndexOfMoodAfter = 21;
          final int _cursorIndexOfPomodoroCount = 22;
          final int _cursorIndexOfSessionType = 23;
          final int _cursorIndexOfBackgroundSound = 24;
          final int _cursorIndexOfIsSuccessful = 25;
          final List<FocusSession> _result = new ArrayList<FocusSession>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final FocusSession _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpTaskType;
            if (_cursor.isNull(_cursorIndexOfTaskType)) {
              _tmpTaskType = null;
            } else {
              _tmpTaskType = _cursor.getString(_cursorIndexOfTaskType);
            }
            final String _tmpSessionName;
            if (_cursor.isNull(_cursorIndexOfSessionName)) {
              _tmpSessionName = null;
            } else {
              _tmpSessionName = _cursor.getString(_cursorIndexOfSessionName);
            }
            final int _tmpPlannedDurationMinutes;
            _tmpPlannedDurationMinutes = _cursor.getInt(_cursorIndexOfPlannedDurationMinutes);
            final Integer _tmpActualDurationMinutes;
            if (_cursor.isNull(_cursorIndexOfActualDurationMinutes)) {
              _tmpActualDurationMinutes = null;
            } else {
              _tmpActualDurationMinutes = _cursor.getInt(_cursorIndexOfActualDurationMinutes);
            }
            final LocalDateTime _tmpStartTime;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStartTime);
            }
            _tmpStartTime = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpEndTime;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfEndTime);
            }
            _tmpEndTime = __converters.toLocalDateTime(_tmp_1);
            final boolean _tmpIsCompleted;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_2 != 0;
            final boolean _tmpWasInterrupted;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfWasInterrupted);
            _tmpWasInterrupted = _tmp_3 != 0;
            final int _tmpInterruptionCount;
            _tmpInterruptionCount = _cursor.getInt(_cursorIndexOfInterruptionCount);
            final int _tmpBreaksTaken;
            _tmpBreaksTaken = _cursor.getInt(_cursorIndexOfBreaksTaken);
            final Integer _tmpFocusQuality;
            if (_cursor.isNull(_cursorIndexOfFocusQuality)) {
              _tmpFocusQuality = null;
            } else {
              _tmpFocusQuality = _cursor.getInt(_cursorIndexOfFocusQuality);
            }
            final Integer _tmpProductivityScore;
            if (_cursor.isNull(_cursorIndexOfProductivityScore)) {
              _tmpProductivityScore = null;
            } else {
              _tmpProductivityScore = _cursor.getInt(_cursorIndexOfProductivityScore);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpTasksCompleted;
            if (_cursor.isNull(_cursorIndexOfTasksCompleted)) {
              _tmpTasksCompleted = null;
            } else {
              _tmpTasksCompleted = _cursor.getString(_cursorIndexOfTasksCompleted);
            }
            final String _tmpDistractions;
            if (_cursor.isNull(_cursorIndexOfDistractions)) {
              _tmpDistractions = null;
            } else {
              _tmpDistractions = _cursor.getString(_cursorIndexOfDistractions);
            }
            final String _tmpSessionGoal;
            if (_cursor.isNull(_cursorIndexOfSessionGoal)) {
              _tmpSessionGoal = null;
            } else {
              _tmpSessionGoal = _cursor.getString(_cursorIndexOfSessionGoal);
            }
            final Boolean _tmpGoalAchieved;
            final Integer _tmp_4;
            if (_cursor.isNull(_cursorIndexOfGoalAchieved)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getInt(_cursorIndexOfGoalAchieved);
            }
            _tmpGoalAchieved = _tmp_4 == null ? null : _tmp_4 != 0;
            final Integer _tmpEnergyLevelBefore;
            if (_cursor.isNull(_cursorIndexOfEnergyLevelBefore)) {
              _tmpEnergyLevelBefore = null;
            } else {
              _tmpEnergyLevelBefore = _cursor.getInt(_cursorIndexOfEnergyLevelBefore);
            }
            final Integer _tmpEnergyLevelAfter;
            if (_cursor.isNull(_cursorIndexOfEnergyLevelAfter)) {
              _tmpEnergyLevelAfter = null;
            } else {
              _tmpEnergyLevelAfter = _cursor.getInt(_cursorIndexOfEnergyLevelAfter);
            }
            final String _tmpMoodBefore;
            if (_cursor.isNull(_cursorIndexOfMoodBefore)) {
              _tmpMoodBefore = null;
            } else {
              _tmpMoodBefore = _cursor.getString(_cursorIndexOfMoodBefore);
            }
            final String _tmpMoodAfter;
            if (_cursor.isNull(_cursorIndexOfMoodAfter)) {
              _tmpMoodAfter = null;
            } else {
              _tmpMoodAfter = _cursor.getString(_cursorIndexOfMoodAfter);
            }
            final int _tmpPomodoroCount;
            _tmpPomodoroCount = _cursor.getInt(_cursorIndexOfPomodoroCount);
            final String _tmpSessionType;
            if (_cursor.isNull(_cursorIndexOfSessionType)) {
              _tmpSessionType = null;
            } else {
              _tmpSessionType = _cursor.getString(_cursorIndexOfSessionType);
            }
            final String _tmpBackgroundSound;
            if (_cursor.isNull(_cursorIndexOfBackgroundSound)) {
              _tmpBackgroundSound = null;
            } else {
              _tmpBackgroundSound = _cursor.getString(_cursorIndexOfBackgroundSound);
            }
            final Boolean _tmpIsSuccessful;
            final Integer _tmp_5;
            if (_cursor.isNull(_cursorIndexOfIsSuccessful)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getInt(_cursorIndexOfIsSuccessful);
            }
            _tmpIsSuccessful = _tmp_5 == null ? null : _tmp_5 != 0;
            _item = new FocusSession(_tmpId,_tmpTaskType,_tmpSessionName,_tmpPlannedDurationMinutes,_tmpActualDurationMinutes,_tmpStartTime,_tmpEndTime,_tmpIsCompleted,_tmpWasInterrupted,_tmpInterruptionCount,_tmpBreaksTaken,_tmpFocusQuality,_tmpProductivityScore,_tmpNotes,_tmpTasksCompleted,_tmpDistractions,_tmpSessionGoal,_tmpGoalAchieved,_tmpEnergyLevelBefore,_tmpEnergyLevelAfter,_tmpMoodBefore,_tmpMoodAfter,_tmpPomodoroCount,_tmpSessionType,_tmpBackgroundSound,_tmpIsSuccessful);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getActiveFocusSession(final Continuation<? super FocusSession> arg0) {
    final String _sql = "SELECT `focus_sessions`.`id` AS `id`, `focus_sessions`.`taskType` AS `taskType`, `focus_sessions`.`sessionName` AS `sessionName`, `focus_sessions`.`plannedDurationMinutes` AS `plannedDurationMinutes`, `focus_sessions`.`actualDurationMinutes` AS `actualDurationMinutes`, `focus_sessions`.`startTime` AS `startTime`, `focus_sessions`.`endTime` AS `endTime`, `focus_sessions`.`isCompleted` AS `isCompleted`, `focus_sessions`.`wasInterrupted` AS `wasInterrupted`, `focus_sessions`.`interruptionCount` AS `interruptionCount`, `focus_sessions`.`breaksTaken` AS `breaksTaken`, `focus_sessions`.`focusQuality` AS `focusQuality`, `focus_sessions`.`productivityScore` AS `productivityScore`, `focus_sessions`.`notes` AS `notes`, `focus_sessions`.`tasksCompleted` AS `tasksCompleted`, `focus_sessions`.`distractions` AS `distractions`, `focus_sessions`.`sessionGoal` AS `sessionGoal`, `focus_sessions`.`goalAchieved` AS `goalAchieved`, `focus_sessions`.`energyLevelBefore` AS `energyLevelBefore`, `focus_sessions`.`energyLevelAfter` AS `energyLevelAfter`, `focus_sessions`.`moodBefore` AS `moodBefore`, `focus_sessions`.`moodAfter` AS `moodAfter`, `focus_sessions`.`pomodoroCount` AS `pomodoroCount`, `focus_sessions`.`sessionType` AS `sessionType`, `focus_sessions`.`backgroundSound` AS `backgroundSound`, `focus_sessions`.`isSuccessful` AS `isSuccessful` FROM focus_sessions WHERE isCompleted = 0 AND endTime IS NULL ORDER BY startTime DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<FocusSession>() {
      @Override
      @Nullable
      public FocusSession call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfTaskType = 1;
          final int _cursorIndexOfSessionName = 2;
          final int _cursorIndexOfPlannedDurationMinutes = 3;
          final int _cursorIndexOfActualDurationMinutes = 4;
          final int _cursorIndexOfStartTime = 5;
          final int _cursorIndexOfEndTime = 6;
          final int _cursorIndexOfIsCompleted = 7;
          final int _cursorIndexOfWasInterrupted = 8;
          final int _cursorIndexOfInterruptionCount = 9;
          final int _cursorIndexOfBreaksTaken = 10;
          final int _cursorIndexOfFocusQuality = 11;
          final int _cursorIndexOfProductivityScore = 12;
          final int _cursorIndexOfNotes = 13;
          final int _cursorIndexOfTasksCompleted = 14;
          final int _cursorIndexOfDistractions = 15;
          final int _cursorIndexOfSessionGoal = 16;
          final int _cursorIndexOfGoalAchieved = 17;
          final int _cursorIndexOfEnergyLevelBefore = 18;
          final int _cursorIndexOfEnergyLevelAfter = 19;
          final int _cursorIndexOfMoodBefore = 20;
          final int _cursorIndexOfMoodAfter = 21;
          final int _cursorIndexOfPomodoroCount = 22;
          final int _cursorIndexOfSessionType = 23;
          final int _cursorIndexOfBackgroundSound = 24;
          final int _cursorIndexOfIsSuccessful = 25;
          final FocusSession _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpTaskType;
            if (_cursor.isNull(_cursorIndexOfTaskType)) {
              _tmpTaskType = null;
            } else {
              _tmpTaskType = _cursor.getString(_cursorIndexOfTaskType);
            }
            final String _tmpSessionName;
            if (_cursor.isNull(_cursorIndexOfSessionName)) {
              _tmpSessionName = null;
            } else {
              _tmpSessionName = _cursor.getString(_cursorIndexOfSessionName);
            }
            final int _tmpPlannedDurationMinutes;
            _tmpPlannedDurationMinutes = _cursor.getInt(_cursorIndexOfPlannedDurationMinutes);
            final Integer _tmpActualDurationMinutes;
            if (_cursor.isNull(_cursorIndexOfActualDurationMinutes)) {
              _tmpActualDurationMinutes = null;
            } else {
              _tmpActualDurationMinutes = _cursor.getInt(_cursorIndexOfActualDurationMinutes);
            }
            final LocalDateTime _tmpStartTime;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStartTime);
            }
            _tmpStartTime = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpEndTime;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfEndTime);
            }
            _tmpEndTime = __converters.toLocalDateTime(_tmp_1);
            final boolean _tmpIsCompleted;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_2 != 0;
            final boolean _tmpWasInterrupted;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfWasInterrupted);
            _tmpWasInterrupted = _tmp_3 != 0;
            final int _tmpInterruptionCount;
            _tmpInterruptionCount = _cursor.getInt(_cursorIndexOfInterruptionCount);
            final int _tmpBreaksTaken;
            _tmpBreaksTaken = _cursor.getInt(_cursorIndexOfBreaksTaken);
            final Integer _tmpFocusQuality;
            if (_cursor.isNull(_cursorIndexOfFocusQuality)) {
              _tmpFocusQuality = null;
            } else {
              _tmpFocusQuality = _cursor.getInt(_cursorIndexOfFocusQuality);
            }
            final Integer _tmpProductivityScore;
            if (_cursor.isNull(_cursorIndexOfProductivityScore)) {
              _tmpProductivityScore = null;
            } else {
              _tmpProductivityScore = _cursor.getInt(_cursorIndexOfProductivityScore);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpTasksCompleted;
            if (_cursor.isNull(_cursorIndexOfTasksCompleted)) {
              _tmpTasksCompleted = null;
            } else {
              _tmpTasksCompleted = _cursor.getString(_cursorIndexOfTasksCompleted);
            }
            final String _tmpDistractions;
            if (_cursor.isNull(_cursorIndexOfDistractions)) {
              _tmpDistractions = null;
            } else {
              _tmpDistractions = _cursor.getString(_cursorIndexOfDistractions);
            }
            final String _tmpSessionGoal;
            if (_cursor.isNull(_cursorIndexOfSessionGoal)) {
              _tmpSessionGoal = null;
            } else {
              _tmpSessionGoal = _cursor.getString(_cursorIndexOfSessionGoal);
            }
            final Boolean _tmpGoalAchieved;
            final Integer _tmp_4;
            if (_cursor.isNull(_cursorIndexOfGoalAchieved)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getInt(_cursorIndexOfGoalAchieved);
            }
            _tmpGoalAchieved = _tmp_4 == null ? null : _tmp_4 != 0;
            final Integer _tmpEnergyLevelBefore;
            if (_cursor.isNull(_cursorIndexOfEnergyLevelBefore)) {
              _tmpEnergyLevelBefore = null;
            } else {
              _tmpEnergyLevelBefore = _cursor.getInt(_cursorIndexOfEnergyLevelBefore);
            }
            final Integer _tmpEnergyLevelAfter;
            if (_cursor.isNull(_cursorIndexOfEnergyLevelAfter)) {
              _tmpEnergyLevelAfter = null;
            } else {
              _tmpEnergyLevelAfter = _cursor.getInt(_cursorIndexOfEnergyLevelAfter);
            }
            final String _tmpMoodBefore;
            if (_cursor.isNull(_cursorIndexOfMoodBefore)) {
              _tmpMoodBefore = null;
            } else {
              _tmpMoodBefore = _cursor.getString(_cursorIndexOfMoodBefore);
            }
            final String _tmpMoodAfter;
            if (_cursor.isNull(_cursorIndexOfMoodAfter)) {
              _tmpMoodAfter = null;
            } else {
              _tmpMoodAfter = _cursor.getString(_cursorIndexOfMoodAfter);
            }
            final int _tmpPomodoroCount;
            _tmpPomodoroCount = _cursor.getInt(_cursorIndexOfPomodoroCount);
            final String _tmpSessionType;
            if (_cursor.isNull(_cursorIndexOfSessionType)) {
              _tmpSessionType = null;
            } else {
              _tmpSessionType = _cursor.getString(_cursorIndexOfSessionType);
            }
            final String _tmpBackgroundSound;
            if (_cursor.isNull(_cursorIndexOfBackgroundSound)) {
              _tmpBackgroundSound = null;
            } else {
              _tmpBackgroundSound = _cursor.getString(_cursorIndexOfBackgroundSound);
            }
            final Boolean _tmpIsSuccessful;
            final Integer _tmp_5;
            if (_cursor.isNull(_cursorIndexOfIsSuccessful)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getInt(_cursorIndexOfIsSuccessful);
            }
            _tmpIsSuccessful = _tmp_5 == null ? null : _tmp_5 != 0;
            _result = new FocusSession(_tmpId,_tmpTaskType,_tmpSessionName,_tmpPlannedDurationMinutes,_tmpActualDurationMinutes,_tmpStartTime,_tmpEndTime,_tmpIsCompleted,_tmpWasInterrupted,_tmpInterruptionCount,_tmpBreaksTaken,_tmpFocusQuality,_tmpProductivityScore,_tmpNotes,_tmpTasksCompleted,_tmpDistractions,_tmpSessionGoal,_tmpGoalAchieved,_tmpEnergyLevelBefore,_tmpEnergyLevelAfter,_tmpMoodBefore,_tmpMoodAfter,_tmpPomodoroCount,_tmpSessionType,_tmpBackgroundSound,_tmpIsSuccessful);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Flow<List<FocusSession>> getFocusSessionsByDateRange(final LocalDateTime startDate,
      final LocalDateTime endDate) {
    final String _sql = "SELECT * FROM focus_sessions WHERE startTime >= ? AND startTime <= ? ORDER BY startTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final String _tmp = __converters.fromLocalDateTime(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    _argIndex = 2;
    final String _tmp_1 = __converters.fromLocalDateTime(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp_1);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"focus_sessions"}, new Callable<List<FocusSession>>() {
      @Override
      @NonNull
      public List<FocusSession> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTaskType = CursorUtil.getColumnIndexOrThrow(_cursor, "taskType");
          final int _cursorIndexOfSessionName = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionName");
          final int _cursorIndexOfPlannedDurationMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "plannedDurationMinutes");
          final int _cursorIndexOfActualDurationMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "actualDurationMinutes");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfWasInterrupted = CursorUtil.getColumnIndexOrThrow(_cursor, "wasInterrupted");
          final int _cursorIndexOfInterruptionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "interruptionCount");
          final int _cursorIndexOfBreaksTaken = CursorUtil.getColumnIndexOrThrow(_cursor, "breaksTaken");
          final int _cursorIndexOfFocusQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "focusQuality");
          final int _cursorIndexOfProductivityScore = CursorUtil.getColumnIndexOrThrow(_cursor, "productivityScore");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfTasksCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "tasksCompleted");
          final int _cursorIndexOfDistractions = CursorUtil.getColumnIndexOrThrow(_cursor, "distractions");
          final int _cursorIndexOfSessionGoal = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionGoal");
          final int _cursorIndexOfGoalAchieved = CursorUtil.getColumnIndexOrThrow(_cursor, "goalAchieved");
          final int _cursorIndexOfEnergyLevelBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "energyLevelBefore");
          final int _cursorIndexOfEnergyLevelAfter = CursorUtil.getColumnIndexOrThrow(_cursor, "energyLevelAfter");
          final int _cursorIndexOfMoodBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "moodBefore");
          final int _cursorIndexOfMoodAfter = CursorUtil.getColumnIndexOrThrow(_cursor, "moodAfter");
          final int _cursorIndexOfPomodoroCount = CursorUtil.getColumnIndexOrThrow(_cursor, "pomodoroCount");
          final int _cursorIndexOfSessionType = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionType");
          final int _cursorIndexOfBackgroundSound = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundSound");
          final int _cursorIndexOfIsSuccessful = CursorUtil.getColumnIndexOrThrow(_cursor, "isSuccessful");
          final List<FocusSession> _result = new ArrayList<FocusSession>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final FocusSession _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpTaskType;
            if (_cursor.isNull(_cursorIndexOfTaskType)) {
              _tmpTaskType = null;
            } else {
              _tmpTaskType = _cursor.getString(_cursorIndexOfTaskType);
            }
            final String _tmpSessionName;
            if (_cursor.isNull(_cursorIndexOfSessionName)) {
              _tmpSessionName = null;
            } else {
              _tmpSessionName = _cursor.getString(_cursorIndexOfSessionName);
            }
            final int _tmpPlannedDurationMinutes;
            _tmpPlannedDurationMinutes = _cursor.getInt(_cursorIndexOfPlannedDurationMinutes);
            final Integer _tmpActualDurationMinutes;
            if (_cursor.isNull(_cursorIndexOfActualDurationMinutes)) {
              _tmpActualDurationMinutes = null;
            } else {
              _tmpActualDurationMinutes = _cursor.getInt(_cursorIndexOfActualDurationMinutes);
            }
            final LocalDateTime _tmpStartTime;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfStartTime);
            }
            _tmpStartTime = __converters.toLocalDateTime(_tmp_2);
            final LocalDateTime _tmpEndTime;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfEndTime);
            }
            _tmpEndTime = __converters.toLocalDateTime(_tmp_3);
            final boolean _tmpIsCompleted;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_4 != 0;
            final boolean _tmpWasInterrupted;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfWasInterrupted);
            _tmpWasInterrupted = _tmp_5 != 0;
            final int _tmpInterruptionCount;
            _tmpInterruptionCount = _cursor.getInt(_cursorIndexOfInterruptionCount);
            final int _tmpBreaksTaken;
            _tmpBreaksTaken = _cursor.getInt(_cursorIndexOfBreaksTaken);
            final Integer _tmpFocusQuality;
            if (_cursor.isNull(_cursorIndexOfFocusQuality)) {
              _tmpFocusQuality = null;
            } else {
              _tmpFocusQuality = _cursor.getInt(_cursorIndexOfFocusQuality);
            }
            final Integer _tmpProductivityScore;
            if (_cursor.isNull(_cursorIndexOfProductivityScore)) {
              _tmpProductivityScore = null;
            } else {
              _tmpProductivityScore = _cursor.getInt(_cursorIndexOfProductivityScore);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpTasksCompleted;
            if (_cursor.isNull(_cursorIndexOfTasksCompleted)) {
              _tmpTasksCompleted = null;
            } else {
              _tmpTasksCompleted = _cursor.getString(_cursorIndexOfTasksCompleted);
            }
            final String _tmpDistractions;
            if (_cursor.isNull(_cursorIndexOfDistractions)) {
              _tmpDistractions = null;
            } else {
              _tmpDistractions = _cursor.getString(_cursorIndexOfDistractions);
            }
            final String _tmpSessionGoal;
            if (_cursor.isNull(_cursorIndexOfSessionGoal)) {
              _tmpSessionGoal = null;
            } else {
              _tmpSessionGoal = _cursor.getString(_cursorIndexOfSessionGoal);
            }
            final Boolean _tmpGoalAchieved;
            final Integer _tmp_6;
            if (_cursor.isNull(_cursorIndexOfGoalAchieved)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getInt(_cursorIndexOfGoalAchieved);
            }
            _tmpGoalAchieved = _tmp_6 == null ? null : _tmp_6 != 0;
            final Integer _tmpEnergyLevelBefore;
            if (_cursor.isNull(_cursorIndexOfEnergyLevelBefore)) {
              _tmpEnergyLevelBefore = null;
            } else {
              _tmpEnergyLevelBefore = _cursor.getInt(_cursorIndexOfEnergyLevelBefore);
            }
            final Integer _tmpEnergyLevelAfter;
            if (_cursor.isNull(_cursorIndexOfEnergyLevelAfter)) {
              _tmpEnergyLevelAfter = null;
            } else {
              _tmpEnergyLevelAfter = _cursor.getInt(_cursorIndexOfEnergyLevelAfter);
            }
            final String _tmpMoodBefore;
            if (_cursor.isNull(_cursorIndexOfMoodBefore)) {
              _tmpMoodBefore = null;
            } else {
              _tmpMoodBefore = _cursor.getString(_cursorIndexOfMoodBefore);
            }
            final String _tmpMoodAfter;
            if (_cursor.isNull(_cursorIndexOfMoodAfter)) {
              _tmpMoodAfter = null;
            } else {
              _tmpMoodAfter = _cursor.getString(_cursorIndexOfMoodAfter);
            }
            final int _tmpPomodoroCount;
            _tmpPomodoroCount = _cursor.getInt(_cursorIndexOfPomodoroCount);
            final String _tmpSessionType;
            if (_cursor.isNull(_cursorIndexOfSessionType)) {
              _tmpSessionType = null;
            } else {
              _tmpSessionType = _cursor.getString(_cursorIndexOfSessionType);
            }
            final String _tmpBackgroundSound;
            if (_cursor.isNull(_cursorIndexOfBackgroundSound)) {
              _tmpBackgroundSound = null;
            } else {
              _tmpBackgroundSound = _cursor.getString(_cursorIndexOfBackgroundSound);
            }
            final Boolean _tmpIsSuccessful;
            final Integer _tmp_7;
            if (_cursor.isNull(_cursorIndexOfIsSuccessful)) {
              _tmp_7 = null;
            } else {
              _tmp_7 = _cursor.getInt(_cursorIndexOfIsSuccessful);
            }
            _tmpIsSuccessful = _tmp_7 == null ? null : _tmp_7 != 0;
            _item = new FocusSession(_tmpId,_tmpTaskType,_tmpSessionName,_tmpPlannedDurationMinutes,_tmpActualDurationMinutes,_tmpStartTime,_tmpEndTime,_tmpIsCompleted,_tmpWasInterrupted,_tmpInterruptionCount,_tmpBreaksTaken,_tmpFocusQuality,_tmpProductivityScore,_tmpNotes,_tmpTasksCompleted,_tmpDistractions,_tmpSessionGoal,_tmpGoalAchieved,_tmpEnergyLevelBefore,_tmpEnergyLevelAfter,_tmpMoodBefore,_tmpMoodAfter,_tmpPomodoroCount,_tmpSessionType,_tmpBackgroundSound,_tmpIsSuccessful);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getFocusSessionById(final long id, final Continuation<? super FocusSession> arg1) {
    final String _sql = "SELECT * FROM focus_sessions WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<FocusSession>() {
      @Override
      @Nullable
      public FocusSession call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTaskType = CursorUtil.getColumnIndexOrThrow(_cursor, "taskType");
          final int _cursorIndexOfSessionName = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionName");
          final int _cursorIndexOfPlannedDurationMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "plannedDurationMinutes");
          final int _cursorIndexOfActualDurationMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "actualDurationMinutes");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfWasInterrupted = CursorUtil.getColumnIndexOrThrow(_cursor, "wasInterrupted");
          final int _cursorIndexOfInterruptionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "interruptionCount");
          final int _cursorIndexOfBreaksTaken = CursorUtil.getColumnIndexOrThrow(_cursor, "breaksTaken");
          final int _cursorIndexOfFocusQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "focusQuality");
          final int _cursorIndexOfProductivityScore = CursorUtil.getColumnIndexOrThrow(_cursor, "productivityScore");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfTasksCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "tasksCompleted");
          final int _cursorIndexOfDistractions = CursorUtil.getColumnIndexOrThrow(_cursor, "distractions");
          final int _cursorIndexOfSessionGoal = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionGoal");
          final int _cursorIndexOfGoalAchieved = CursorUtil.getColumnIndexOrThrow(_cursor, "goalAchieved");
          final int _cursorIndexOfEnergyLevelBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "energyLevelBefore");
          final int _cursorIndexOfEnergyLevelAfter = CursorUtil.getColumnIndexOrThrow(_cursor, "energyLevelAfter");
          final int _cursorIndexOfMoodBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "moodBefore");
          final int _cursorIndexOfMoodAfter = CursorUtil.getColumnIndexOrThrow(_cursor, "moodAfter");
          final int _cursorIndexOfPomodoroCount = CursorUtil.getColumnIndexOrThrow(_cursor, "pomodoroCount");
          final int _cursorIndexOfSessionType = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionType");
          final int _cursorIndexOfBackgroundSound = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundSound");
          final int _cursorIndexOfIsSuccessful = CursorUtil.getColumnIndexOrThrow(_cursor, "isSuccessful");
          final FocusSession _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpTaskType;
            if (_cursor.isNull(_cursorIndexOfTaskType)) {
              _tmpTaskType = null;
            } else {
              _tmpTaskType = _cursor.getString(_cursorIndexOfTaskType);
            }
            final String _tmpSessionName;
            if (_cursor.isNull(_cursorIndexOfSessionName)) {
              _tmpSessionName = null;
            } else {
              _tmpSessionName = _cursor.getString(_cursorIndexOfSessionName);
            }
            final int _tmpPlannedDurationMinutes;
            _tmpPlannedDurationMinutes = _cursor.getInt(_cursorIndexOfPlannedDurationMinutes);
            final Integer _tmpActualDurationMinutes;
            if (_cursor.isNull(_cursorIndexOfActualDurationMinutes)) {
              _tmpActualDurationMinutes = null;
            } else {
              _tmpActualDurationMinutes = _cursor.getInt(_cursorIndexOfActualDurationMinutes);
            }
            final LocalDateTime _tmpStartTime;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStartTime);
            }
            _tmpStartTime = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpEndTime;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfEndTime);
            }
            _tmpEndTime = __converters.toLocalDateTime(_tmp_1);
            final boolean _tmpIsCompleted;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_2 != 0;
            final boolean _tmpWasInterrupted;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfWasInterrupted);
            _tmpWasInterrupted = _tmp_3 != 0;
            final int _tmpInterruptionCount;
            _tmpInterruptionCount = _cursor.getInt(_cursorIndexOfInterruptionCount);
            final int _tmpBreaksTaken;
            _tmpBreaksTaken = _cursor.getInt(_cursorIndexOfBreaksTaken);
            final Integer _tmpFocusQuality;
            if (_cursor.isNull(_cursorIndexOfFocusQuality)) {
              _tmpFocusQuality = null;
            } else {
              _tmpFocusQuality = _cursor.getInt(_cursorIndexOfFocusQuality);
            }
            final Integer _tmpProductivityScore;
            if (_cursor.isNull(_cursorIndexOfProductivityScore)) {
              _tmpProductivityScore = null;
            } else {
              _tmpProductivityScore = _cursor.getInt(_cursorIndexOfProductivityScore);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpTasksCompleted;
            if (_cursor.isNull(_cursorIndexOfTasksCompleted)) {
              _tmpTasksCompleted = null;
            } else {
              _tmpTasksCompleted = _cursor.getString(_cursorIndexOfTasksCompleted);
            }
            final String _tmpDistractions;
            if (_cursor.isNull(_cursorIndexOfDistractions)) {
              _tmpDistractions = null;
            } else {
              _tmpDistractions = _cursor.getString(_cursorIndexOfDistractions);
            }
            final String _tmpSessionGoal;
            if (_cursor.isNull(_cursorIndexOfSessionGoal)) {
              _tmpSessionGoal = null;
            } else {
              _tmpSessionGoal = _cursor.getString(_cursorIndexOfSessionGoal);
            }
            final Boolean _tmpGoalAchieved;
            final Integer _tmp_4;
            if (_cursor.isNull(_cursorIndexOfGoalAchieved)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getInt(_cursorIndexOfGoalAchieved);
            }
            _tmpGoalAchieved = _tmp_4 == null ? null : _tmp_4 != 0;
            final Integer _tmpEnergyLevelBefore;
            if (_cursor.isNull(_cursorIndexOfEnergyLevelBefore)) {
              _tmpEnergyLevelBefore = null;
            } else {
              _tmpEnergyLevelBefore = _cursor.getInt(_cursorIndexOfEnergyLevelBefore);
            }
            final Integer _tmpEnergyLevelAfter;
            if (_cursor.isNull(_cursorIndexOfEnergyLevelAfter)) {
              _tmpEnergyLevelAfter = null;
            } else {
              _tmpEnergyLevelAfter = _cursor.getInt(_cursorIndexOfEnergyLevelAfter);
            }
            final String _tmpMoodBefore;
            if (_cursor.isNull(_cursorIndexOfMoodBefore)) {
              _tmpMoodBefore = null;
            } else {
              _tmpMoodBefore = _cursor.getString(_cursorIndexOfMoodBefore);
            }
            final String _tmpMoodAfter;
            if (_cursor.isNull(_cursorIndexOfMoodAfter)) {
              _tmpMoodAfter = null;
            } else {
              _tmpMoodAfter = _cursor.getString(_cursorIndexOfMoodAfter);
            }
            final int _tmpPomodoroCount;
            _tmpPomodoroCount = _cursor.getInt(_cursorIndexOfPomodoroCount);
            final String _tmpSessionType;
            if (_cursor.isNull(_cursorIndexOfSessionType)) {
              _tmpSessionType = null;
            } else {
              _tmpSessionType = _cursor.getString(_cursorIndexOfSessionType);
            }
            final String _tmpBackgroundSound;
            if (_cursor.isNull(_cursorIndexOfBackgroundSound)) {
              _tmpBackgroundSound = null;
            } else {
              _tmpBackgroundSound = _cursor.getString(_cursorIndexOfBackgroundSound);
            }
            final Boolean _tmpIsSuccessful;
            final Integer _tmp_5;
            if (_cursor.isNull(_cursorIndexOfIsSuccessful)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getInt(_cursorIndexOfIsSuccessful);
            }
            _tmpIsSuccessful = _tmp_5 == null ? null : _tmp_5 != 0;
            _result = new FocusSession(_tmpId,_tmpTaskType,_tmpSessionName,_tmpPlannedDurationMinutes,_tmpActualDurationMinutes,_tmpStartTime,_tmpEndTime,_tmpIsCompleted,_tmpWasInterrupted,_tmpInterruptionCount,_tmpBreaksTaken,_tmpFocusQuality,_tmpProductivityScore,_tmpNotes,_tmpTasksCompleted,_tmpDistractions,_tmpSessionGoal,_tmpGoalAchieved,_tmpEnergyLevelBefore,_tmpEnergyLevelAfter,_tmpMoodBefore,_tmpMoodAfter,_tmpPomodoroCount,_tmpSessionType,_tmpBackgroundSound,_tmpIsSuccessful);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg1);
  }

  @Override
  public Object getCompletedSessionCount(final Continuation<? super Integer> arg0) {
    final String _sql = "SELECT COUNT(*) FROM focus_sessions WHERE isCompleted = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Object getAverageSessionDuration(final Continuation<? super Double> arg0) {
    final String _sql = "SELECT AVG(actualDurationMinutes) FROM focus_sessions WHERE isCompleted = 1 AND actualDurationMinutes IS NOT NULL";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Object getAverageFocusQuality(final Continuation<? super Double> arg0) {
    final String _sql = "SELECT AVG(focusQuality) FROM focus_sessions WHERE focusQuality IS NOT NULL";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Object getAverageProductivityScore(final Continuation<? super Double> arg0) {
    final String _sql = "SELECT AVG(productivityScore) FROM focus_sessions WHERE productivityScore IS NOT NULL";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Object getTotalFocusTime(final Continuation<? super Long> arg0) {
    final String _sql = "SELECT SUM(actualDurationMinutes) FROM focus_sessions WHERE isCompleted = 1 AND actualDurationMinutes IS NOT NULL";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Long>() {
      @Override
      @Nullable
      public Long call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Long _result;
          if (_cursor.moveToFirst()) {
            final Long _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Object getSessionCountSince(final LocalDateTime startDate,
      final Continuation<? super Integer> arg1) {
    final String _sql = "SELECT COUNT(*) FROM focus_sessions WHERE isCompleted = 1 AND startTime >= ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromLocalDateTime(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp_1;
            if (_cursor.isNull(0)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(0);
            }
            _result = _tmp_1;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg1);
  }

  @Override
  public Object getRecentCompletedSessions(final int limit,
      final Continuation<? super List<FocusSession>> arg1) {
    final String _sql = "SELECT * FROM focus_sessions WHERE isCompleted = 1 ORDER BY startTime DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<FocusSession>>() {
      @Override
      @NonNull
      public List<FocusSession> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTaskType = CursorUtil.getColumnIndexOrThrow(_cursor, "taskType");
          final int _cursorIndexOfSessionName = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionName");
          final int _cursorIndexOfPlannedDurationMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "plannedDurationMinutes");
          final int _cursorIndexOfActualDurationMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "actualDurationMinutes");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfWasInterrupted = CursorUtil.getColumnIndexOrThrow(_cursor, "wasInterrupted");
          final int _cursorIndexOfInterruptionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "interruptionCount");
          final int _cursorIndexOfBreaksTaken = CursorUtil.getColumnIndexOrThrow(_cursor, "breaksTaken");
          final int _cursorIndexOfFocusQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "focusQuality");
          final int _cursorIndexOfProductivityScore = CursorUtil.getColumnIndexOrThrow(_cursor, "productivityScore");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfTasksCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "tasksCompleted");
          final int _cursorIndexOfDistractions = CursorUtil.getColumnIndexOrThrow(_cursor, "distractions");
          final int _cursorIndexOfSessionGoal = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionGoal");
          final int _cursorIndexOfGoalAchieved = CursorUtil.getColumnIndexOrThrow(_cursor, "goalAchieved");
          final int _cursorIndexOfEnergyLevelBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "energyLevelBefore");
          final int _cursorIndexOfEnergyLevelAfter = CursorUtil.getColumnIndexOrThrow(_cursor, "energyLevelAfter");
          final int _cursorIndexOfMoodBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "moodBefore");
          final int _cursorIndexOfMoodAfter = CursorUtil.getColumnIndexOrThrow(_cursor, "moodAfter");
          final int _cursorIndexOfPomodoroCount = CursorUtil.getColumnIndexOrThrow(_cursor, "pomodoroCount");
          final int _cursorIndexOfSessionType = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionType");
          final int _cursorIndexOfBackgroundSound = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundSound");
          final int _cursorIndexOfIsSuccessful = CursorUtil.getColumnIndexOrThrow(_cursor, "isSuccessful");
          final List<FocusSession> _result = new ArrayList<FocusSession>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final FocusSession _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpTaskType;
            if (_cursor.isNull(_cursorIndexOfTaskType)) {
              _tmpTaskType = null;
            } else {
              _tmpTaskType = _cursor.getString(_cursorIndexOfTaskType);
            }
            final String _tmpSessionName;
            if (_cursor.isNull(_cursorIndexOfSessionName)) {
              _tmpSessionName = null;
            } else {
              _tmpSessionName = _cursor.getString(_cursorIndexOfSessionName);
            }
            final int _tmpPlannedDurationMinutes;
            _tmpPlannedDurationMinutes = _cursor.getInt(_cursorIndexOfPlannedDurationMinutes);
            final Integer _tmpActualDurationMinutes;
            if (_cursor.isNull(_cursorIndexOfActualDurationMinutes)) {
              _tmpActualDurationMinutes = null;
            } else {
              _tmpActualDurationMinutes = _cursor.getInt(_cursorIndexOfActualDurationMinutes);
            }
            final LocalDateTime _tmpStartTime;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStartTime);
            }
            _tmpStartTime = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpEndTime;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfEndTime);
            }
            _tmpEndTime = __converters.toLocalDateTime(_tmp_1);
            final boolean _tmpIsCompleted;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_2 != 0;
            final boolean _tmpWasInterrupted;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfWasInterrupted);
            _tmpWasInterrupted = _tmp_3 != 0;
            final int _tmpInterruptionCount;
            _tmpInterruptionCount = _cursor.getInt(_cursorIndexOfInterruptionCount);
            final int _tmpBreaksTaken;
            _tmpBreaksTaken = _cursor.getInt(_cursorIndexOfBreaksTaken);
            final Integer _tmpFocusQuality;
            if (_cursor.isNull(_cursorIndexOfFocusQuality)) {
              _tmpFocusQuality = null;
            } else {
              _tmpFocusQuality = _cursor.getInt(_cursorIndexOfFocusQuality);
            }
            final Integer _tmpProductivityScore;
            if (_cursor.isNull(_cursorIndexOfProductivityScore)) {
              _tmpProductivityScore = null;
            } else {
              _tmpProductivityScore = _cursor.getInt(_cursorIndexOfProductivityScore);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpTasksCompleted;
            if (_cursor.isNull(_cursorIndexOfTasksCompleted)) {
              _tmpTasksCompleted = null;
            } else {
              _tmpTasksCompleted = _cursor.getString(_cursorIndexOfTasksCompleted);
            }
            final String _tmpDistractions;
            if (_cursor.isNull(_cursorIndexOfDistractions)) {
              _tmpDistractions = null;
            } else {
              _tmpDistractions = _cursor.getString(_cursorIndexOfDistractions);
            }
            final String _tmpSessionGoal;
            if (_cursor.isNull(_cursorIndexOfSessionGoal)) {
              _tmpSessionGoal = null;
            } else {
              _tmpSessionGoal = _cursor.getString(_cursorIndexOfSessionGoal);
            }
            final Boolean _tmpGoalAchieved;
            final Integer _tmp_4;
            if (_cursor.isNull(_cursorIndexOfGoalAchieved)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getInt(_cursorIndexOfGoalAchieved);
            }
            _tmpGoalAchieved = _tmp_4 == null ? null : _tmp_4 != 0;
            final Integer _tmpEnergyLevelBefore;
            if (_cursor.isNull(_cursorIndexOfEnergyLevelBefore)) {
              _tmpEnergyLevelBefore = null;
            } else {
              _tmpEnergyLevelBefore = _cursor.getInt(_cursorIndexOfEnergyLevelBefore);
            }
            final Integer _tmpEnergyLevelAfter;
            if (_cursor.isNull(_cursorIndexOfEnergyLevelAfter)) {
              _tmpEnergyLevelAfter = null;
            } else {
              _tmpEnergyLevelAfter = _cursor.getInt(_cursorIndexOfEnergyLevelAfter);
            }
            final String _tmpMoodBefore;
            if (_cursor.isNull(_cursorIndexOfMoodBefore)) {
              _tmpMoodBefore = null;
            } else {
              _tmpMoodBefore = _cursor.getString(_cursorIndexOfMoodBefore);
            }
            final String _tmpMoodAfter;
            if (_cursor.isNull(_cursorIndexOfMoodAfter)) {
              _tmpMoodAfter = null;
            } else {
              _tmpMoodAfter = _cursor.getString(_cursorIndexOfMoodAfter);
            }
            final int _tmpPomodoroCount;
            _tmpPomodoroCount = _cursor.getInt(_cursorIndexOfPomodoroCount);
            final String _tmpSessionType;
            if (_cursor.isNull(_cursorIndexOfSessionType)) {
              _tmpSessionType = null;
            } else {
              _tmpSessionType = _cursor.getString(_cursorIndexOfSessionType);
            }
            final String _tmpBackgroundSound;
            if (_cursor.isNull(_cursorIndexOfBackgroundSound)) {
              _tmpBackgroundSound = null;
            } else {
              _tmpBackgroundSound = _cursor.getString(_cursorIndexOfBackgroundSound);
            }
            final Boolean _tmpIsSuccessful;
            final Integer _tmp_5;
            if (_cursor.isNull(_cursorIndexOfIsSuccessful)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getInt(_cursorIndexOfIsSuccessful);
            }
            _tmpIsSuccessful = _tmp_5 == null ? null : _tmp_5 != 0;
            _item = new FocusSession(_tmpId,_tmpTaskType,_tmpSessionName,_tmpPlannedDurationMinutes,_tmpActualDurationMinutes,_tmpStartTime,_tmpEndTime,_tmpIsCompleted,_tmpWasInterrupted,_tmpInterruptionCount,_tmpBreaksTaken,_tmpFocusQuality,_tmpProductivityScore,_tmpNotes,_tmpTasksCompleted,_tmpDistractions,_tmpSessionGoal,_tmpGoalAchieved,_tmpEnergyLevelBefore,_tmpEnergyLevelAfter,_tmpMoodBefore,_tmpMoodAfter,_tmpPomodoroCount,_tmpSessionType,_tmpBackgroundSound,_tmpIsSuccessful);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg1);
  }

  @Override
  public Object getSessionCountByTaskType(final Continuation<? super List<TaskTypeCount>> arg0) {
    final String _sql = "SELECT taskType, COUNT(*) as count FROM focus_sessions WHERE isCompleted = 1 GROUP BY taskType ORDER BY count DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<TaskTypeCount>>() {
      @Override
      @NonNull
      public List<TaskTypeCount> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfTaskType = 0;
          final int _cursorIndexOfCount = 1;
          final List<TaskTypeCount> _result = new ArrayList<TaskTypeCount>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TaskTypeCount _item;
            final String _tmpTaskType;
            if (_cursor.isNull(_cursorIndexOfTaskType)) {
              _tmpTaskType = null;
            } else {
              _tmpTaskType = _cursor.getString(_cursorIndexOfTaskType);
            }
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            _item = new TaskTypeCount(_tmpTaskType,_tmpCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Object getAverageDurationByTaskType(final String taskType,
      final Continuation<? super Double> arg1) {
    final String _sql = "SELECT AVG(actualDurationMinutes) FROM focus_sessions WHERE taskType = ? AND isCompleted = 1 AND actualDurationMinutes IS NOT NULL";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (taskType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, taskType);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg1);
  }

  @Override
  public Object getInterruptedSessionCount(final Continuation<? super Integer> arg0) {
    final String _sql = "SELECT COUNT(*) FROM focus_sessions WHERE wasInterrupted = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Object getAverageInterruptionCount(final Continuation<? super Double> arg0) {
    final String _sql = "SELECT AVG(interruptionCount) FROM focus_sessions WHERE isCompleted = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Flow<List<FocusSession>> getFocusSessionsByType(final String sessionType) {
    final String _sql = "SELECT * FROM focus_sessions WHERE sessionType = ? ORDER BY startTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (sessionType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, sessionType);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"focus_sessions"}, new Callable<List<FocusSession>>() {
      @Override
      @NonNull
      public List<FocusSession> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTaskType = CursorUtil.getColumnIndexOrThrow(_cursor, "taskType");
          final int _cursorIndexOfSessionName = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionName");
          final int _cursorIndexOfPlannedDurationMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "plannedDurationMinutes");
          final int _cursorIndexOfActualDurationMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "actualDurationMinutes");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfWasInterrupted = CursorUtil.getColumnIndexOrThrow(_cursor, "wasInterrupted");
          final int _cursorIndexOfInterruptionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "interruptionCount");
          final int _cursorIndexOfBreaksTaken = CursorUtil.getColumnIndexOrThrow(_cursor, "breaksTaken");
          final int _cursorIndexOfFocusQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "focusQuality");
          final int _cursorIndexOfProductivityScore = CursorUtil.getColumnIndexOrThrow(_cursor, "productivityScore");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfTasksCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "tasksCompleted");
          final int _cursorIndexOfDistractions = CursorUtil.getColumnIndexOrThrow(_cursor, "distractions");
          final int _cursorIndexOfSessionGoal = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionGoal");
          final int _cursorIndexOfGoalAchieved = CursorUtil.getColumnIndexOrThrow(_cursor, "goalAchieved");
          final int _cursorIndexOfEnergyLevelBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "energyLevelBefore");
          final int _cursorIndexOfEnergyLevelAfter = CursorUtil.getColumnIndexOrThrow(_cursor, "energyLevelAfter");
          final int _cursorIndexOfMoodBefore = CursorUtil.getColumnIndexOrThrow(_cursor, "moodBefore");
          final int _cursorIndexOfMoodAfter = CursorUtil.getColumnIndexOrThrow(_cursor, "moodAfter");
          final int _cursorIndexOfPomodoroCount = CursorUtil.getColumnIndexOrThrow(_cursor, "pomodoroCount");
          final int _cursorIndexOfSessionType = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionType");
          final int _cursorIndexOfBackgroundSound = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundSound");
          final int _cursorIndexOfIsSuccessful = CursorUtil.getColumnIndexOrThrow(_cursor, "isSuccessful");
          final List<FocusSession> _result = new ArrayList<FocusSession>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final FocusSession _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpTaskType;
            if (_cursor.isNull(_cursorIndexOfTaskType)) {
              _tmpTaskType = null;
            } else {
              _tmpTaskType = _cursor.getString(_cursorIndexOfTaskType);
            }
            final String _tmpSessionName;
            if (_cursor.isNull(_cursorIndexOfSessionName)) {
              _tmpSessionName = null;
            } else {
              _tmpSessionName = _cursor.getString(_cursorIndexOfSessionName);
            }
            final int _tmpPlannedDurationMinutes;
            _tmpPlannedDurationMinutes = _cursor.getInt(_cursorIndexOfPlannedDurationMinutes);
            final Integer _tmpActualDurationMinutes;
            if (_cursor.isNull(_cursorIndexOfActualDurationMinutes)) {
              _tmpActualDurationMinutes = null;
            } else {
              _tmpActualDurationMinutes = _cursor.getInt(_cursorIndexOfActualDurationMinutes);
            }
            final LocalDateTime _tmpStartTime;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStartTime);
            }
            _tmpStartTime = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpEndTime;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfEndTime);
            }
            _tmpEndTime = __converters.toLocalDateTime(_tmp_1);
            final boolean _tmpIsCompleted;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_2 != 0;
            final boolean _tmpWasInterrupted;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfWasInterrupted);
            _tmpWasInterrupted = _tmp_3 != 0;
            final int _tmpInterruptionCount;
            _tmpInterruptionCount = _cursor.getInt(_cursorIndexOfInterruptionCount);
            final int _tmpBreaksTaken;
            _tmpBreaksTaken = _cursor.getInt(_cursorIndexOfBreaksTaken);
            final Integer _tmpFocusQuality;
            if (_cursor.isNull(_cursorIndexOfFocusQuality)) {
              _tmpFocusQuality = null;
            } else {
              _tmpFocusQuality = _cursor.getInt(_cursorIndexOfFocusQuality);
            }
            final Integer _tmpProductivityScore;
            if (_cursor.isNull(_cursorIndexOfProductivityScore)) {
              _tmpProductivityScore = null;
            } else {
              _tmpProductivityScore = _cursor.getInt(_cursorIndexOfProductivityScore);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpTasksCompleted;
            if (_cursor.isNull(_cursorIndexOfTasksCompleted)) {
              _tmpTasksCompleted = null;
            } else {
              _tmpTasksCompleted = _cursor.getString(_cursorIndexOfTasksCompleted);
            }
            final String _tmpDistractions;
            if (_cursor.isNull(_cursorIndexOfDistractions)) {
              _tmpDistractions = null;
            } else {
              _tmpDistractions = _cursor.getString(_cursorIndexOfDistractions);
            }
            final String _tmpSessionGoal;
            if (_cursor.isNull(_cursorIndexOfSessionGoal)) {
              _tmpSessionGoal = null;
            } else {
              _tmpSessionGoal = _cursor.getString(_cursorIndexOfSessionGoal);
            }
            final Boolean _tmpGoalAchieved;
            final Integer _tmp_4;
            if (_cursor.isNull(_cursorIndexOfGoalAchieved)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getInt(_cursorIndexOfGoalAchieved);
            }
            _tmpGoalAchieved = _tmp_4 == null ? null : _tmp_4 != 0;
            final Integer _tmpEnergyLevelBefore;
            if (_cursor.isNull(_cursorIndexOfEnergyLevelBefore)) {
              _tmpEnergyLevelBefore = null;
            } else {
              _tmpEnergyLevelBefore = _cursor.getInt(_cursorIndexOfEnergyLevelBefore);
            }
            final Integer _tmpEnergyLevelAfter;
            if (_cursor.isNull(_cursorIndexOfEnergyLevelAfter)) {
              _tmpEnergyLevelAfter = null;
            } else {
              _tmpEnergyLevelAfter = _cursor.getInt(_cursorIndexOfEnergyLevelAfter);
            }
            final String _tmpMoodBefore;
            if (_cursor.isNull(_cursorIndexOfMoodBefore)) {
              _tmpMoodBefore = null;
            } else {
              _tmpMoodBefore = _cursor.getString(_cursorIndexOfMoodBefore);
            }
            final String _tmpMoodAfter;
            if (_cursor.isNull(_cursorIndexOfMoodAfter)) {
              _tmpMoodAfter = null;
            } else {
              _tmpMoodAfter = _cursor.getString(_cursorIndexOfMoodAfter);
            }
            final int _tmpPomodoroCount;
            _tmpPomodoroCount = _cursor.getInt(_cursorIndexOfPomodoroCount);
            final String _tmpSessionType;
            if (_cursor.isNull(_cursorIndexOfSessionType)) {
              _tmpSessionType = null;
            } else {
              _tmpSessionType = _cursor.getString(_cursorIndexOfSessionType);
            }
            final String _tmpBackgroundSound;
            if (_cursor.isNull(_cursorIndexOfBackgroundSound)) {
              _tmpBackgroundSound = null;
            } else {
              _tmpBackgroundSound = _cursor.getString(_cursorIndexOfBackgroundSound);
            }
            final Boolean _tmpIsSuccessful;
            final Integer _tmp_5;
            if (_cursor.isNull(_cursorIndexOfIsSuccessful)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getInt(_cursorIndexOfIsSuccessful);
            }
            _tmpIsSuccessful = _tmp_5 == null ? null : _tmp_5 != 0;
            _item = new FocusSession(_tmpId,_tmpTaskType,_tmpSessionName,_tmpPlannedDurationMinutes,_tmpActualDurationMinutes,_tmpStartTime,_tmpEndTime,_tmpIsCompleted,_tmpWasInterrupted,_tmpInterruptionCount,_tmpBreaksTaken,_tmpFocusQuality,_tmpProductivityScore,_tmpNotes,_tmpTasksCompleted,_tmpDistractions,_tmpSessionGoal,_tmpGoalAchieved,_tmpEnergyLevelBefore,_tmpEnergyLevelAfter,_tmpMoodBefore,_tmpMoodAfter,_tmpPomodoroCount,_tmpSessionType,_tmpBackgroundSound,_tmpIsSuccessful);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getSuccessfulSessionCount(final Continuation<? super Integer> arg0) {
    final String _sql = "SELECT COUNT(*) FROM focus_sessions WHERE isSuccessful = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
