package com.focusflow.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000b\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\b\u001a*\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\b\u0010\t\u001a \u0010\n\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\u00052\u0006\u0010\f\u001a\u00020\u00052\u0006\u0010\r\u001a\u00020\u0005H\u0007\u001a\b\u0010\u000e\u001a\u00020\u0001H\u0007\u001a\b\u0010\u000f\u001a\u00020\u0001H\u0007\u001a;\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00170\u00162\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0012H\u0007\u00a2\u0006\u0002\u0010\u001b\u001aX\u0010\u001c\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\u00142\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00010\u001f2\u0006\u0010 \u001a\u00020\u00032\u0006\u0010!\u001a\u00020\u00032\u0006\u0010\"\u001a\u00020\u00032\u0006\u0010#\u001a\u00020$2\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\u0014H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b&\u0010\'\u001aH\u0010(\u001a\u00020\u00012\u0006\u0010)\u001a\u00020*2\u0012\u0010+\u001a\u000e\u0012\u0004\u0012\u00020*\u0012\u0004\u0012\u00020\u00010,2\u0006\u0010-\u001a\u00020\u00142\f\u0010.\u001a\b\u0012\u0004\u0012\u00020\u00010\u001f2\f\u0010/\u001a\b\u0012\u0004\u0012\u00020\u00010\u001fH\u0007\u001a,\u00100\u001a\u00020\u00012\u0006\u00101\u001a\u00020\u00032\u0012\u00102\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010,2\u0006\u00103\u001a\u00020\u0005H\u0007\u001ax\u00104\u001a\u00020\u00012\u0006\u0010\u0018\u001a\u00020\u00192\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u00010,2\u0006\u00106\u001a\u00020\u00032\u0012\u00107\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010,2\u0006\u00108\u001a\u00020\u00032\u0012\u00109\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010,2\u0006\u0010:\u001a\u00020\u00032\u0012\u0010;\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010,H\u0007\u001a6\u0010<\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\u00142\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00010\u001f2\u0006\u0010 \u001a\u00020\u00032\u0006\u0010\"\u001a\u00020\u00032\u0006\u0010#\u001a\u00020$H\u0007\u001a \u0010=\u001a\u00020\u00012\f\u0010>\u001a\b\u0012\u0004\u0012\u00020\u00010\u001f2\b\b\u0002\u0010\u001a\u001a\u00020?H\u0007\u001a3\u0010@\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00170\u00162\u0006\u0010\u001a\u001a\u00020\u0012H\u0007\u00a2\u0006\u0002\u0010A\u001a\u0015\u0010B\u001a\u00020\u00012\u0006\u0010C\u001a\u00020\u0012H\u0007\u00a2\u0006\u0002\u0010D\u001a\u0015\u0010E\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u0012H\u0007\u00a2\u0006\u0002\u0010D\u001a6\u0010F\u001a\u00020\u00012\u0006\u0010G\u001a\u00020H2\u0006\u0010I\u001a\u00020H2\b\u0010)\u001a\u0004\u0018\u00010*2\u0012\u0010+\u001a\u000e\u0012\u0004\u0012\u00020*\u0012\u0004\u0012\u00020\u00010,H\u0007\u001a#\u0010F\u001a\u00020\u00012\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00170\u00162\u0006\u0010\u001a\u001a\u00020\u0012H\u0007\u00a2\u0006\u0002\u0010J\u001aN\u0010K\u001a\u00020\u00012\u0006\u0010)\u001a\u00020*2\u0012\u0010+\u001a\u000e\u0012\u0004\u0012\u00020*\u0012\u0004\u0012\u00020\u00010,2\u0006\u0010:\u001a\u00020\u00032\u0012\u0010;\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010,2\f\u0010/\u001a\b\u0012\u0004\u0012\u00020\u00010\u001fH\u0007\u001a*\u0010L\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010M\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\bN\u0010O\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006P"}, d2 = {"DebtMetricColumn", "", "label", "", "amount", "", "color", "Landroidx/compose/ui/graphics/Color;", "DebtMetricColumn-mxwnekA", "(Ljava/lang/String;DJ)V", "DebtOverviewCard", "totalDebt", "minimumPayments", "availableForDebt", "EmptyStateCard", "EmptyStateMessage", "EnhancedPayoffResultsSection", "payoffPlan", "error/NonExistentClass", "showComparison", "", "creditCards", "", "Lcom/focusflow/data/model/CreditCard;", "goalType", "Lcom/focusflow/ui/screens/PayoffGoalType;", "viewModel", "(Lerror/NonExistentClass;ZLjava/util/List;Lcom/focusflow/ui/screens/PayoffGoalType;Lerror/NonExistentClass;)V", "EnhancedStrategyOption", "selected", "onClick", "Lkotlin/Function0;", "title", "subtitle", "description", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "showAdvanced", "EnhancedStrategyOption-vmayBhU", "(ZLkotlin/jvm/functions/Function0;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/graphics/vector/ImageVector;JZ)V", "EnhancedStrategySelectionCard", "selectedStrategy", "Lcom/focusflow/ui/viewmodel/PayoffStrategy;", "onStrategySelected", "Lkotlin/Function1;", "showAdvancedOptions", "onToggleAdvancedOptions", "onGeneratePlan", "ExtraPaymentInputCard", "currentAmount", "onAmountChanged", "availableAmount", "GoalSelectionCard", "onGoalTypeChanged", "targetDate", "onTargetDateChanged", "targetPayment", "onTargetPaymentChanged", "extraPayment", "onExtraPaymentChanged", "GoalTypeOption", "PayoffPlannerScreen", "onNavigateBack", "Lcom/focusflow/ui/viewmodel/PayoffPlannerViewModel;", "PayoffResultsSection", "(Lerror/NonExistentClass;ZLjava/util/List;Lerror/NonExistentClass;)V", "PayoffStepItem", "step", "(Lerror/NonExistentClass;)V", "PayoffSummaryCard", "StrategyComparisonCard", "avalanche", "Lcom/focusflow/data/model/PayoffComparison;", "snowball", "(Ljava/util/List;Lerror/NonExistentClass;)V", "StrategySelectionCard", "SummaryItem", "value", "SummaryItem-mxwnekA", "(Ljava/lang/String;Ljava/lang/String;J)V", "app_debug"})
public final class PayoffPlannerScreenKt {
    
    @androidx.compose.runtime.Composable
    public static final void PayoffPlannerScreen(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffPlannerViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void DebtOverviewCard(double totalDebt, double minimumPayments, double availableForDebt) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void ExtraPaymentInputCard(@org.jetbrains.annotations.NotNull
    java.lang.String currentAmount, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onAmountChanged, double availableAmount) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void StrategyComparisonCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PayoffComparison avalanche, @org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PayoffComparison snowball, @org.jetbrains.annotations.Nullable
    com.focusflow.ui.viewmodel.PayoffStrategy selectedStrategy, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.ui.viewmodel.PayoffStrategy, kotlin.Unit> onStrategySelected) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EmptyStateCard() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void GoalSelectionCard(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.screens.PayoffGoalType goalType, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.ui.screens.PayoffGoalType, kotlin.Unit> onGoalTypeChanged, @org.jetbrains.annotations.NotNull
    java.lang.String targetDate, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTargetDateChanged, @org.jetbrains.annotations.NotNull
    java.lang.String targetPayment, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTargetPaymentChanged, @org.jetbrains.annotations.NotNull
    java.lang.String extraPayment, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onExtraPaymentChanged) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void GoalTypeOption(boolean selected, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.graphics.vector.ImageVector icon) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EmptyStateMessage() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EnhancedStrategySelectionCard(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffStrategy selectedStrategy, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.ui.viewmodel.PayoffStrategy, kotlin.Unit> onStrategySelected, boolean showAdvancedOptions, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onToggleAdvancedOptions, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onGeneratePlan) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void StrategySelectionCard(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffStrategy selectedStrategy, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.ui.viewmodel.PayoffStrategy, kotlin.Unit> onStrategySelected, @org.jetbrains.annotations.NotNull
    java.lang.String extraPayment, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onExtraPaymentChanged, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onGeneratePlan) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EnhancedPayoffResultsSection(@org.jetbrains.annotations.NotNull
    error.NonExistentClass payoffPlan, boolean showComparison, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.CreditCard> creditCards, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.screens.PayoffGoalType goalType, @org.jetbrains.annotations.NotNull
    error.NonExistentClass viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void PayoffResultsSection(@org.jetbrains.annotations.NotNull
    error.NonExistentClass payoffPlan, boolean showComparison, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.CreditCard> creditCards, @org.jetbrains.annotations.NotNull
    error.NonExistentClass viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void PayoffSummaryCard(@org.jetbrains.annotations.NotNull
    error.NonExistentClass payoffPlan) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void StrategyComparisonCard(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.CreditCard> creditCards, @org.jetbrains.annotations.NotNull
    error.NonExistentClass viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void PayoffStepItem(@org.jetbrains.annotations.NotNull
    error.NonExistentClass step) {
    }
}