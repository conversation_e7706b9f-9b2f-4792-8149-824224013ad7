plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'dagger.hilt.android.plugin'
    id 'kotlin-parcelize'
}

android {
    namespace 'com.focusflow'
    compileSdk 34

    defaultConfig {
        applicationId "com.focusflow"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
        
        // Room schema export
        kapt {
            arguments {
                arg("room.schemaLocation", "$projectDir/schemas")
            }
        }
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
            // Optimize for memory usage during debug builds
            javaCompileOptions {
                annotationProcessorOptions {
                    arguments += [
                        "room.incremental": "true",
                        "room.expandProjection": "true"
                    ]
                }
            }
        }
        
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            debuggable false
            
            // Signing config would be added here for production
            // signingConfig signingConfigs.release
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
        coreLibraryDesugaringEnabled true
    }
    
    kotlinOptions {
        jvmTarget = '11'
    }
    
    buildFeatures {
        compose true
    }
    
    composeOptions {
        kotlinCompilerExtensionVersion '1.5.4'
    }
    
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
    
    testOptions {
        unitTests {
            includeAndroidResources = true
        }
    }
    
    // Fix KAPT Java module access issues with JDK 11+
    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KaptGenerateStubs).configureEach {
        kotlinOptions.jvmTarget = "11"
    }
    
    // Configure KAPT with JVM arguments for Java module access
    kapt {
        javacOptions {
            option("-Xms512m")
            option("-Xmx2048m")
            option("--add-exports=jdk.compiler/com.sun.tools.javac.main=ALL-UNNAMED")
            option("--add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED")
        }
    }
}

repositories {
    google()
    mavenCentral()
    maven { url "https://jitpack.io" }
}

dependencies {
    // Core Android
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.activity:activity-compose:1.8.2'
    
    // Compose BOM
    implementation platform('androidx.compose:compose-bom:2023.10.01')
    implementation 'androidx.compose.ui:ui'
    implementation 'androidx.compose.ui:ui-graphics'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    implementation 'androidx.compose.material:material'
    implementation 'androidx.compose.material:material-icons-extended'
    
    // Navigation
    implementation 'androidx.navigation:navigation-compose:2.7.5'
    
    // ViewModel
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-compose:2.7.0'
    
    // Hilt
    implementation 'com.google.dagger:hilt-android:2.48'
    implementation 'androidx.hilt:hilt-navigation-compose:1.1.0'
    kapt 'com.google.dagger:hilt-compiler:2.48'
    
    // Room
    implementation 'androidx.room:room-runtime:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'
    kapt 'androidx.room:room-compiler:2.6.1'
    
    // Kotlinx DateTime
    implementation 'org.jetbrains.kotlinx:kotlinx-datetime:0.5.0'
    
    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    
    // Networking
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    
    // Image loading
    implementation 'io.coil-kt:coil-compose:2.5.0'
    
    // Biometric authentication
    implementation 'androidx.biometric:biometric:1.1.0'
    
    // Encrypted SharedPreferences
    implementation 'androidx.security:security-crypto:1.1.0-alpha06'
    
    // Work Manager for background tasks
    implementation 'androidx.work:work-runtime-ktx:2.9.0'
    implementation 'androidx.hilt:hilt-work:1.1.0'
    
    // Permissions
    implementation 'com.google.accompanist:accompanist-permissions:0.32.0'

    // Speech recognition and text-to-speech
    implementation 'androidx.core:core-ktx:1.12.0'
    
    // Date picker
    implementation 'io.github.vanpra.compose-material-dialogs:datetime:0.9.0'
    
    // Charts for visualizations - temporarily disabled to reduce memory usage
    // implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
    // If above fails, try uncommenting this alternative version
    // implementation 'com.github.PhilJay:MPAndroidChart:3.1.0'
    
    // Desugaring for Java 8+ APIs
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'
    
    // Testing
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'androidx.test:core:1.5.0'
    testImplementation 'androidx.test.ext:junit:1.1.5'
    testImplementation 'androidx.room:room-testing:2.6.1'
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3'
    testImplementation 'com.google.truth:truth:1.1.4'
    testImplementation 'androidx.arch.core:core-testing:2.2.0'
    
    // Android Testing
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation platform('androidx.compose:compose-bom:2023.10.01')
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4'
    androidTestImplementation 'androidx.navigation:navigation-testing:2.7.5'
    androidTestImplementation 'com.google.dagger:hilt-android-testing:2.48'
    kaptAndroidTest 'com.google.dagger:hilt-compiler:2.48'
    
    // Debug tools
    debugImplementation 'androidx.compose.ui:ui-tooling'
    debugImplementation 'androidx.compose.ui:ui-test-manifest'
}



