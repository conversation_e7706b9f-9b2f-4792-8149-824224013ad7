package com.focusflow.data.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import com.focusflow.data.model.PayoffPlan
import kotlinx.datetime.LocalDate

@Dao
interface PayoffPlanDao {
    @Query("SELECT * FROM payoff_plans WHERE isActive = 1 ORDER BY createdAt DESC")
    fun getAllActivePayoffPlans(): Flow<List<PayoffPlan>>

    @Query("SELECT * FROM payoff_plans WHERE id = :planId")
    suspend fun getPayoffPlanById(planId: Long): PayoffPlan?

    @Query("SELECT * FROM payoff_plans WHERE isActive = 1 ORDER BY createdAt DESC LIMIT 1")
    suspend fun getCurrentActivePayoffPlan(): PayoffPlan?

    @Query("SELECT * FROM payoff_plans WHERE isActive = 1 ORDER BY createdAt DESC LIMIT 1")
    fun getCurrentActivePayoffPlanFlow(): Flow<PayoffPlan?>

    @Insert
    suspend fun insertPayoffPlan(payoffPlan: PayoffPlan): Long

    @Update
    suspend fun updatePayoffPlan(payoffPlan: PayoffPlan)

    @Delete
    suspend fun deletePayoffPlan(payoffPlan: PayoffPlan)

    @Query("UPDATE payoff_plans SET isActive = 0 WHERE id != :activeId")
    suspend fun deactivateOtherPlans(activeId: Long)

    @Query("SELECT COUNT(*) FROM payoff_plans WHERE isActive = 1")
    suspend fun getActivePayoffPlanCount(): Int

    @Query("SELECT * FROM payoff_plans WHERE strategy = :strategy AND isActive = 1")
    fun getPayoffPlansByStrategy(strategy: String): Flow<List<PayoffPlan>>
}
