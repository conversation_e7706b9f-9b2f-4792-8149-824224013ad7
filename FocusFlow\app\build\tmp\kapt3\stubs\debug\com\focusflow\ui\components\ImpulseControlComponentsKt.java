package com.focusflow.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000>\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0013\u001a\u001f\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u00a2\u0006\u0002\u0010\u0006\u001a\u0018\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\tH\u0007\u001a.\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\u000f2\u000e\b\u0002\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u000fH\u0007\u001a.\u0010\u0011\u001a\u00020\u00012\u0006\u0010\u0012\u001a\u00020\r2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00010\u000fH\u0003\u001a.\u0010\u0018\u001a\u00020\u00012\u0006\u0010\u0019\u001a\u00020\r2\u0012\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\u001b2\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\u0087\u0001\u0010\u001c\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\t2\u0006\u0010\u001e\u001a\u00020\u00142\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u001f\u001a\u00020\r2\b\b\u0002\u0010 \u001a\u00020\u00162\b\b\u0002\u0010!\u001a\u00020\u00162\b\b\u0002\u0010\"\u001a\u00020\u00162\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00010\u000f2\f\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00010\u000f2\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00010\u000f2\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00010\u000fH\u0007\u00a2\u0006\u0002\u0010\'\u001a\u0010\u0010(\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\tH\u0007\u001a \u0010)\u001a\u00020\u00012\f\u0010*\u001a\b\u0012\u0004\u0012\u00020\u00010\u000f2\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\u0016\u0010+\u001a\u00020\u00012\f\u0010,\u001a\b\u0012\u0004\u0012\u00020\u00010\u000fH\u0007\u001a\u0010\u0010-\u001a\u00020\u00142\u0006\u0010\u0012\u001a\u00020\rH\u0002\u00a8\u0006."}, d2 = {"BudgetImpactCard", "", "budgetImpact", "error/NonExistentClass", "modifier", "Landroidx/compose/ui/Modifier;", "(Lerror/NonExistentClass;Landroidx/compose/ui/Modifier;)V", "BudgetWarningCard", "remainingBudget", "", "expenseAmount", "CooldownPeriodCard", "hoursRemaining", "", "onRemoveFromCooldown", "Lkotlin/Function0;", "onExtendDelay", "DelayOptionItem", "hours", "label", "", "isSelected", "", "onClick", "DelayPeriodSelector", "selectedHours", "onPeriodSelected", "Lkotlin/Function1;", "ImpulseControlDialog", "amount", "category", "coolingOffSeconds", "enableReflectionQuestions", "enableBudgetWarnings", "enableWishlistSuggestions", "onConfirm", "onCancel", "onAddToWishlist", "onDelay", "(DLjava/lang/String;Lerror/NonExistentClass;IZZZLkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V", "ImpulseControlQuestions", "MindfulnessBreathingExercise", "onComplete", "SpendingWatchlistCard", "onAddToWatchlist", "getDelayDescription", "app_debug"})
public final class ImpulseControlComponentsKt {
    
    @androidx.compose.runtime.Composable
    public static final void ImpulseControlDialog(double amount, @org.jetbrains.annotations.NotNull
    java.lang.String category, @org.jetbrains.annotations.Nullable
    error.NonExistentClass budgetImpact, int coolingOffSeconds, boolean enableReflectionQuestions, boolean enableBudgetWarnings, boolean enableWishlistSuggestions, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onConfirm, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onCancel, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onAddToWishlist, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDelay) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void BudgetImpactCard(@org.jetbrains.annotations.NotNull
    error.NonExistentClass budgetImpact, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void ImpulseControlQuestions(double amount) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void SpendingWatchlistCard(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onAddToWatchlist) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void BudgetWarningCard(double remainingBudget, double expenseAmount) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void CooldownPeriodCard(int hoursRemaining, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onRemoveFromCooldown, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onExtendDelay) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void DelayPeriodSelector(int selectedHours, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onPeriodSelected, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void DelayOptionItem(int hours, java.lang.String label, boolean isSelected, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    private static final java.lang.String getDelayDescription(int hours) {
        return null;
    }
    
    @androidx.compose.runtime.Composable
    public static final void MindfulnessBreathingExercise(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onComplete, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
}