package com.focusflow.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.focusflow.data.dao.*
import com.focusflow.data.model.*

@Database(
    entities = [
        Expense::class,
        CreditCard::class,
        BudgetCategory::class,
        HabitLog::class,
        Task::class,
        AIInteraction::class,
        UserPreferences::class,
        Achievement::class,
        UserStats::class,
        VirtualPet::class,
        WishlistItem::class,
        BudgetRecommendation::class,
        SpendingReflection::class,
        BudgetAnalytics::class,
        FocusSession::class,
        SpendingPattern::class,
        AlternativeProduct::class,
        AccountabilityContact::class,
        DashboardWidget::class,
        VoiceCommand::class,
        PayoffPlan::class,
        PaymentSchedule::class,
        PayoffMilestone::class
    ],
    version = 8,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class FocusFlowDatabase : RoomDatabase() {
    abstract fun expenseDao(): ExpenseDao
    abstract fun creditCardDao(): CreditCardDao
    abstract fun budgetCategoryDao(): BudgetCategoryDao
    abstract fun habitLogDao(): HabitLogDao
    abstract fun taskDao(): TaskDao
    abstract fun aiInteractionDao(): AIInteractionDao
    abstract fun userPreferencesDao(): UserPreferencesDao
    abstract fun achievementDao(): AchievementDao
    abstract fun userStatsDao(): UserStatsDao
    abstract fun virtualPetDao(): VirtualPetDao
    abstract fun wishlistItemDao(): WishlistItemDao
    abstract fun budgetRecommendationDao(): BudgetRecommendationDao
    abstract fun spendingReflectionDao(): SpendingReflectionDao
    abstract fun budgetAnalyticsDao(): BudgetAnalyticsDao
    abstract fun focusSessionDao(): FocusSessionDao
    abstract fun spendingPatternDao(): SpendingPatternDao
    abstract fun alternativeProductDao(): AlternativeProductDao
    abstract fun accountabilityContactDao(): AccountabilityContactDao
    abstract fun dashboardWidgetDao(): DashboardWidgetDao
    abstract fun voiceCommandDao(): VoiceCommandDao
    abstract fun payoffPlanDao(): PayoffPlanDao
    abstract fun paymentScheduleDao(): PaymentScheduleDao
    abstract fun payoffMilestoneDao(): PayoffMilestoneDao

    companion object {
        @Volatile
        private var INSTANCE: FocusFlowDatabase? = null

        fun getDatabase(context: Context): FocusFlowDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    FocusFlowDatabase::class.java,
                    "focusflow_database"
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}

