package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000f\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0006\u0010\u0010\u001a\u00020\u0011J\u0016\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0013H\u0082@\u00a2\u0006\u0002\u0010\u0015J\b\u0010\u0016\u001a\u00020\u0013H\u0002J\u000e\u0010\u0017\u001a\u00020\u0013H\u0082@\u00a2\u0006\u0002\u0010\u0018J\u0010\u0010\u0019\u001a\u00020\u00132\u0006\u0010\u001a\u001a\u00020\u0013H\u0002J\b\u0010\u001b\u001a\u00020\u0013H\u0002J\b\u0010\u001c\u001a\u00020\u0013H\u0002J\u000e\u0010\u001d\u001a\u00020\u0013H\u0082@\u00a2\u0006\u0002\u0010\u0018J\u0010\u0010\u001e\u001a\u00020\u00132\u0006\u0010\u001f\u001a\u00020\u0013H\u0002J\u000e\u0010 \u001a\u00020\u00112\u0006\u0010\u001a\u001a\u00020\u0013J\u000e\u0010!\u001a\u00020\u00112\u0006\u0010\u001a\u001a\u00020\u0013R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\""}, d2 = {"Lcom/focusflow/ui/viewmodel/AICoachViewModel;", "Landroidx/lifecycle/ViewModel;", "userPreferencesRepository", "Lcom/focusflow/data/repository/UserPreferencesRepository;", "expenseRepository", "Lcom/focusflow/data/repository/ExpenseRepository;", "creditCardRepository", "Lcom/focusflow/data/repository/CreditCardRepository;", "(Lcom/focusflow/data/repository/UserPreferencesRepository;Lcom/focusflow/data/repository/ExpenseRepository;Lcom/focusflow/data/repository/CreditCardRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/focusflow/ui/viewmodel/AICoachUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "clearError", "", "generateAIResponse", "", "userMessage", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateBudgetAdvice", "generateDebtAdvice", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateGeneralResponse", "message", "generateMoneyTip", "generateProgressReport", "generateSpendingAnalysis", "generateTaskBreakdown", "task", "sendMessage", "updateCurrentMessage", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class AICoachViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.ExpenseRepository expenseRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.CreditCardRepository creditCardRepository = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.AICoachUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.AICoachUiState> uiState = null;
    
    @javax.inject.Inject
    public AICoachViewModel(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.ExpenseRepository expenseRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.CreditCardRepository creditCardRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.AICoachUiState> getUiState() {
        return null;
    }
    
    public final void updateCurrentMessage(@org.jetbrains.annotations.NotNull
    java.lang.String message) {
    }
    
    public final void sendMessage(@org.jetbrains.annotations.NotNull
    java.lang.String message) {
    }
    
    private final java.lang.Object generateAIResponse(java.lang.String userMessage, kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    private final java.lang.Object generateSpendingAnalysis(kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    private final java.lang.String generateBudgetAdvice() {
        return null;
    }
    
    private final java.lang.Object generateDebtAdvice(kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    private final java.lang.String generateTaskBreakdown(java.lang.String task) {
        return null;
    }
    
    private final java.lang.String generateProgressReport() {
        return null;
    }
    
    private final java.lang.String generateMoneyTip() {
        return null;
    }
    
    private final java.lang.String generateGeneralResponse(java.lang.String message) {
        return null;
    }
    
    public final void clearError() {
    }
}