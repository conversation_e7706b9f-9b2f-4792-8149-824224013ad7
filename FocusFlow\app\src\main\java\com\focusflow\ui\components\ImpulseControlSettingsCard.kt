package com.focusflow.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.focusflow.ui.viewmodel.ImpulseControlViewModel

@Composable
fun ImpulseControlSettingsCard(
    impulseControlViewModel: ImpulseControlViewModel = hiltViewModel(),
    modifier: Modifier = Modifier
) {
    val uiState by impulseControlViewModel.uiState.collectAsStateWithLifecycle()
    
    var showThresholdDialog by remember { mutableStateOf(false) }
    var showCoolingOffDialog by remember { mutableStateOf(false) }

    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Header
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Psychology,
                    contentDescription = null,
                    tint = MaterialTheme.colors.primary,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Impulse Control",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Enable/Disable impulse control
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Enable Impulse Control",
                        style = MaterialTheme.typography.subtitle1,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "Show confirmation dialogs for large purchases",
                        style = MaterialTheme.typography.body2,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                }
                Switch(
                    checked = uiState.impulseControlEnabled,
                    onCheckedChange = { /* TODO: Implement toggle */ },
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = MaterialTheme.colors.primary
                    )
                )
            }
            
            if (uiState.impulseControlEnabled) {
                Spacer(modifier = Modifier.height(16.dp))
                Divider()
                Spacer(modifier = Modifier.height(16.dp))
                
                // Spending threshold setting
                SettingItem(
                    title = "Spending Threshold",
                    subtitle = "Trigger impulse control for purchases over $${String.format("%.0f", uiState.spendingThreshold)}",
                    icon = Icons.Default.AttachMoney,
                    onClick = { showThresholdDialog = true }
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // Cooling-off period setting
                SettingItem(
                    title = "Cooling-off Period",
                    subtitle = "${uiState.coolingOffPeriodSeconds} seconds reflection time",
                    icon = Icons.Default.Timer,
                    onClick = { showCoolingOffDialog = true }
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // Reflection questions toggle
                SettingToggleItem(
                    title = "Reflection Questions",
                    subtitle = "Show mindful spending questions",
                    icon = Icons.Default.QuestionMark,
                    checked = uiState.enableReflectionQuestions,
                    onCheckedChange = { /* TODO: Implement toggle */ }
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // Budget warnings toggle
                SettingToggleItem(
                    title = "Budget Warnings",
                    subtitle = "Show budget impact previews",
                    icon = Icons.Default.Warning,
                    checked = uiState.enableBudgetWarnings,
                    onCheckedChange = { /* TODO: Implement toggle */ }
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // Wishlist suggestions toggle
                SettingToggleItem(
                    title = "Wishlist Suggestions",
                    subtitle = "Offer to delay purchases",
                    icon = Icons.Default.Star,
                    checked = uiState.enableWishlistSuggestions,
                    onCheckedChange = { /* TODO: Implement toggle */ }
                )
            }
        }
    }

    // Spending threshold dialog
    if (showThresholdDialog) {
        SpendingThresholdDialog(
            currentThreshold = uiState.spendingThreshold,
            onDismiss = { showThresholdDialog = false },
            onUpdateThreshold = { newThreshold ->
                impulseControlViewModel.updateSpendingThreshold(newThreshold)
                showThresholdDialog = false
            }
        )
    }

    // Cooling-off period dialog
    if (showCoolingOffDialog) {
        CoolingOffPeriodDialog(
            currentPeriod = uiState.coolingOffPeriodSeconds,
            onDismiss = { showCoolingOffDialog = false },
            onUpdatePeriod = { newPeriod ->
                impulseControlViewModel.updateCoolingOffPeriod(newPeriod)
                showCoolingOffDialog = false
            }
        )
    }
}

@Composable
private fun SettingItem(
    title: String,
    subtitle: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        elevation = 1.dp,
        backgroundColor = MaterialTheme.colors.surface
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                icon,
                contentDescription = null,
                tint = MaterialTheme.colors.primary,
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(12.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.subtitle2,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
            }
            IconButton(onClick = onClick) {
                Icon(
                    Icons.Default.Edit,
                    contentDescription = "Edit",
                    modifier = Modifier.size(18.dp)
                )
            }
        }
    }
}

@Composable
private fun SettingToggleItem(
    title: String,
    subtitle: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 1.dp,
        backgroundColor = MaterialTheme.colors.surface
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                icon,
                contentDescription = null,
                tint = MaterialTheme.colors.primary,
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(12.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.subtitle2,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
            }
            Switch(
                checked = checked,
                onCheckedChange = onCheckedChange,
                colors = SwitchDefaults.colors(
                    checkedThumbColor = MaterialTheme.colors.primary
                )
            )
        }
    }
}

@Composable
private fun SpendingThresholdDialog(
    currentThreshold: Double,
    onDismiss: () -> Unit,
    onUpdateThreshold: (Double) -> Unit
) {
    var thresholdText by remember { mutableStateOf(currentThreshold.toString()) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Spending Threshold",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                Text(
                    text = "Set the minimum amount that triggers impulse control:",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                OutlinedTextField(
                    value = thresholdText,
                    onValueChange = { thresholdText = it },
                    label = { Text("Threshold Amount") },
                    leadingIcon = { Text("$") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    thresholdText.toDoubleOrNull()?.let { threshold ->
                        if (threshold > 0) {
                            onUpdateThreshold(threshold)
                        }
                    }
                },
                enabled = thresholdText.toDoubleOrNull() != null && 
                         (thresholdText.toDoubleOrNull() ?: 0.0) > 0
            ) {
                Text("Update")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
private fun CoolingOffPeriodDialog(
    currentPeriod: Int,
    onDismiss: () -> Unit,
    onUpdatePeriod: (Int) -> Unit
) {
    val periodOptions = listOf(5, 10, 15, 30, 60)
    var selectedPeriod by remember { mutableStateOf(currentPeriod) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Cooling-off Period",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                Text(
                    text = "Choose how long to pause before allowing purchases:",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                periodOptions.forEach { period ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selectedPeriod == period,
                            onClick = { selectedPeriod = period }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "$period seconds",
                            style = MaterialTheme.typography.body2
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = { onUpdatePeriod(selectedPeriod) }
            ) {
                Text("Update")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}
