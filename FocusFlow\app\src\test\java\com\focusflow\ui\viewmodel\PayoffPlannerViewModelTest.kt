package com.focusflow.ui.viewmodel

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.focusflow.data.model.*
import com.focusflow.data.repository.*
import io.mockk.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.*
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@ExperimentalCoroutinesApi
class PayoffPlannerViewModelTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private val testDispatcher = StandardTestDispatcher()

    private lateinit var creditCardRepository: CreditCardRepository
    private lateinit var payoffPlanRepository: PayoffPlanRepository
    private lateinit var budgetCategoryRepository: BudgetCategoryRepository
    private lateinit var userPreferencesRepository: UserPreferencesRepository
    private lateinit var viewModel: PayoffPlannerViewModel

    private val testCreditCards = listOf(
        CreditCard(
            id = 1,
            name = "High Interest Card",
            currentBalance = 5000.0,
            creditLimit = 10000.0,
            minimumPayment = 150.0,
            dueDate = LocalDate(2024, 1, 15),
            interestRate = 24.99
        ),
        CreditCard(
            id = 2,
            name = "Low Balance Card",
            currentBalance = 1500.0,
            creditLimit = 5000.0,
            minimumPayment = 50.0,
            dueDate = LocalDate(2024, 1, 20),
            interestRate = 18.99
        ),
        CreditCard(
            id = 3,
            name = "Medium Card",
            currentBalance = 3000.0,
            creditLimit = 8000.0,
            minimumPayment = 100.0,
            dueDate = LocalDate(2024, 1, 25),
            interestRate = 21.99
        )
    )

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        
        creditCardRepository = mockk()
        payoffPlanRepository = mockk()
        budgetCategoryRepository = mockk()
        userPreferencesRepository = mockk()

        // Mock default behavior
        every { creditCardRepository.getAllActiveCreditCards() } returns flowOf(testCreditCards)
        every { payoffPlanRepository.getCurrentActivePayoffPlanFlow() } returns flowOf(null)
        
        coEvery { creditCardRepository.getTotalDebt() } returns 9500.0
        coEvery { creditCardRepository.getTotalMinimumPaymentsDue(any()) } returns 300.0
        coEvery { userPreferencesRepository.getUserPreferencesSync() } returns UserPreferences(
            id = 1,
            budgetPeriod = "weekly"
        )
        coEvery { budgetCategoryRepository.getBudgetCategoryByName("Debt Payment") } returns BudgetCategory(
            id = 1,
            name = "Debt Payment",
            allocatedAmount = 500.0,
            spentAmount = 200.0,
            budgetPeriod = "weekly",
            budgetYear = 2024,
            budgetMonth = 1
        )

        viewModel = PayoffPlannerViewModel(
            creditCardRepository,
            payoffPlanRepository,
            budgetCategoryRepository,
            userPreferencesRepository
        )
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `initial state is correct`() = runTest {
        testScheduler.advanceUntilIdle()
        
        val uiState = viewModel.uiState.value
        assertEquals(9500.0, uiState.totalDebt)
        assertEquals(300.0, uiState.totalMinimumPayments)
        assertEquals("weekly", uiState.budgetPeriod)
        assertEquals(300.0, uiState.availableForDebtPayment) // 500 - 200
        assertEquals(false, uiState.isLoading)
    }

    @Test
    fun `generatePayoffComparison creates both strategies`() = runTest {
        testScheduler.advanceUntilIdle()
        
        viewModel.generatePayoffComparison(200.0)
        testScheduler.advanceUntilIdle()
        
        val uiState = viewModel.uiState.value
        assertNotNull(uiState.avalancheComparison)
        assertNotNull(uiState.snowballComparison)
        assertEquals(false, uiState.isGeneratingPlan)
        
        // Avalanche should target highest interest rate first (24.99%)
        assertEquals(PayoffStrategy.AVALANCHE, uiState.avalancheComparison!!.strategy)
        assertEquals(PayoffStrategy.SNOWBALL, uiState.snowballComparison!!.strategy)
        
        // Avalanche should generally save more money (lower total interest)
        assertTrue(uiState.avalancheComparison!!.totalInterestPaid <= uiState.snowballComparison!!.totalInterestPaid)
    }

    @Test
    fun `avalanche strategy prioritizes highest interest rate`() = runTest {
        testScheduler.advanceUntilIdle()
        
        viewModel.generatePayoffComparison(100.0)
        testScheduler.advanceUntilIdle()
        
        val avalancheComparison = viewModel.uiState.value.avalancheComparison
        assertNotNull(avalancheComparison)
        
        // With extra payment, should pay off debt faster than minimum payments only
        assertTrue(avalancheComparison.totalMonths > 0)
        assertTrue(avalancheComparison.monthlyPayment > 300.0) // minimum payments + extra
    }

    @Test
    fun `snowball strategy prioritizes smallest balance`() = runTest {
        testScheduler.advanceUntilIdle()
        
        viewModel.generatePayoffComparison(100.0)
        testScheduler.advanceUntilIdle()
        
        val snowballComparison = viewModel.uiState.value.snowballComparison
        assertNotNull(snowballComparison)
        
        // Should target the $1500 card first in snowball method
        assertTrue(snowballComparison.totalMonths > 0)
        assertTrue(snowballComparison.monthlyPayment > 300.0)
    }

    @Test
    fun `createPayoffPlan saves plan to repository`() = runTest {
        testScheduler.advanceUntilIdle()
        
        // First generate comparison
        viewModel.generatePayoffComparison(150.0)
        testScheduler.advanceUntilIdle()
        
        // Select a strategy
        viewModel.selectStrategy(PayoffStrategy.AVALANCHE)
        
        // Mock repository calls
        coEvery { payoffPlanRepository.createCompletePayoffPlan(any(), any(), any()) } returns 1L
        coEvery { payoffPlanRepository.setActivePayoffPlan(1L) } just Runs
        
        // Create the plan
        viewModel.createPayoffPlan(PayoffStrategy.AVALANCHE, 150.0, "My Avalanche Plan")
        testScheduler.advanceUntilIdle()
        
        // Verify repository was called
        coVerify { payoffPlanRepository.createCompletePayoffPlan(any(), any(), any()) }
        coVerify { payoffPlanRepository.setActivePayoffPlan(1L) }
        
        val uiState = viewModel.uiState.value
        assertEquals(PayoffStrategy.AVALANCHE, uiState.selectedStrategy)
        assertEquals(false, uiState.isCreatingPlan)
    }

    @Test
    fun `updateExtraPayment updates state`() = runTest {
        viewModel.updateExtraPayment(250.0)
        
        val uiState = viewModel.uiState.value
        assertEquals(250.0, uiState.extraPaymentAmount)
    }

    @Test
    fun `selectStrategy updates state`() = runTest {
        viewModel.selectStrategy(PayoffStrategy.SNOWBALL)
        
        val uiState = viewModel.uiState.value
        assertEquals(PayoffStrategy.SNOWBALL, uiState.selectedStrategy)
    }

    @Test
    fun `error handling when no credit cards`() = runTest {
        // Mock empty credit cards
        every { creditCardRepository.getAllActiveCreditCards() } returns flowOf(emptyList())
        
        val emptyViewModel = PayoffPlannerViewModel(
            creditCardRepository,
            payoffPlanRepository,
            budgetCategoryRepository,
            userPreferencesRepository
        )
        
        testScheduler.advanceUntilIdle()
        
        emptyViewModel.generatePayoffComparison(100.0)
        testScheduler.advanceUntilIdle()
        
        val uiState = emptyViewModel.uiState.value
        assertEquals("No credit cards found. Add credit cards first.", uiState.error)
    }

    @Test
    fun `clearError resets error state`() = runTest {
        // Trigger an error first
        every { creditCardRepository.getAllActiveCreditCards() } returns flowOf(emptyList())
        
        val errorViewModel = PayoffPlannerViewModel(
            creditCardRepository,
            payoffPlanRepository,
            budgetCategoryRepository,
            userPreferencesRepository
        )
        
        testScheduler.advanceUntilIdle()
        
        errorViewModel.generatePayoffComparison(100.0)
        testScheduler.advanceUntilIdle()
        
        // Verify error exists
        assertTrue(errorViewModel.uiState.value.error != null)
        
        // Clear error
        errorViewModel.clearError()
        
        // Verify error is cleared
        assertEquals(null, errorViewModel.uiState.value.error)
    }

    @Test
    fun `payoff comparison calculates correct timeline`() = runTest {
        testScheduler.advanceUntilIdle()
        
        viewModel.generatePayoffComparison(0.0) // No extra payment
        testScheduler.advanceUntilIdle()
        
        val avalancheComparison = viewModel.uiState.value.avalancheComparison
        assertNotNull(avalancheComparison)
        
        // With only minimum payments, should take longer
        assertTrue(avalancheComparison.totalMonths > 12) // Should take more than a year
        
        // Now test with extra payment
        viewModel.generatePayoffComparison(300.0) // Significant extra payment
        testScheduler.advanceUntilIdle()
        
        val fasterComparison = viewModel.uiState.value.avalancheComparison
        assertNotNull(fasterComparison)
        
        // With extra payment, should be faster
        assertTrue(fasterComparison.totalMonths < avalancheComparison.totalMonths)
    }
}
