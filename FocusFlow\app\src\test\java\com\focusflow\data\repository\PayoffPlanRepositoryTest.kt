package com.focusflow.data.repository

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.focusflow.data.dao.PayoffPlanDao
import com.focusflow.data.dao.PaymentScheduleDao
import com.focusflow.data.dao.PayoffMilestoneDao
import com.focusflow.data.model.*
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import kotlin.test.assertEquals

@ExperimentalCoroutinesApi
class PayoffPlanRepositoryTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private lateinit var payoffPlanDao: PayoffPlanDao
    private lateinit var paymentScheduleDao: PaymentScheduleDao
    private lateinit var payoffMilestoneDao: PayoffMilestoneDao
    private lateinit var repository: PayoffPlanRepository

    private val testPayoffPlan = PayoffPlan(
        id = 1,
        name = "Test Avalanche Plan",
        strategy = "AVALANCHE",
        totalExtraPayment = 200.0,
        totalMonths = 24,
        totalInterestSaved = 1500.0,
        totalPayments = 12000.0,
        createdAt = LocalDateTime(2024, 1, 1, 10, 0),
        isActive = true,
        targetPayoffDate = LocalDate(2026, 1, 1),
        monthlyBudgetAllocation = 200.0
    )

    private val testPaymentSchedules = listOf(
        PaymentSchedule(
            id = 1,
            payoffPlanId = 1,
            creditCardId = 1,
            month = 1,
            scheduledPayment = 350.0,
            principalAmount = 300.0,
            interestAmount = 50.0,
            remainingBalance = 4700.0,
            isExtraPayment = false,
            dueDate = LocalDate(2024, 2, 15)
        ),
        PaymentSchedule(
            id = 2,
            payoffPlanId = 1,
            creditCardId = 1,
            month = 1,
            scheduledPayment = 200.0,
            principalAmount = 200.0,
            interestAmount = 0.0,
            remainingBalance = 4500.0,
            isExtraPayment = true,
            dueDate = LocalDate(2024, 2, 15)
        )
    )

    private val testMilestones = listOf(
        PayoffMilestone(
            id = 1,
            payoffPlanId = 1,
            milestoneType = "QUARTER_COMPLETE",
            targetDate = LocalDate(2024, 7, 1),
            targetAmount = 7500.0,
            description = "25% of debt paid off! 🎉",
            isCompleted = false,
            celebrationMessage = "Great progress! You're a quarter of the way to debt freedom!"
        ),
        PayoffMilestone(
            id = 2,
            payoffPlanId = 1,
            milestoneType = "CARD_PAID_OFF",
            targetDate = LocalDate(2025, 1, 1),
            targetAmount = 0.0,
            description = "High Interest Card paid off! 🎊",
            isCompleted = false,
            celebrationMessage = "Congratulations! You've completely paid off High Interest Card!"
        )
    )

    @Before
    fun setup() {
        payoffPlanDao = mockk()
        paymentScheduleDao = mockk()
        payoffMilestoneDao = mockk()

        repository = PayoffPlanRepository(
            payoffPlanDao,
            paymentScheduleDao,
            payoffMilestoneDao
        )
    }

    @Test
    fun `getAllActivePayoffPlans returns flow from dao`() = runTest {
        val expectedPlans = listOf(testPayoffPlan)
        every { payoffPlanDao.getAllActivePayoffPlans() } returns flowOf(expectedPlans)

        val result = repository.getAllActivePayoffPlans()
        
        verify { payoffPlanDao.getAllActivePayoffPlans() }
        // Flow testing would require collecting the flow, but this verifies the delegation
    }

    @Test
    fun `getPayoffPlanById delegates to dao`() = runTest {
        coEvery { payoffPlanDao.getPayoffPlanById(1) } returns testPayoffPlan

        val result = repository.getPayoffPlanById(1)

        assertEquals(testPayoffPlan, result)
        coVerify { payoffPlanDao.getPayoffPlanById(1) }
    }

    @Test
    fun `getCurrentActivePayoffPlan delegates to dao`() = runTest {
        coEvery { payoffPlanDao.getCurrentActivePayoffPlan() } returns testPayoffPlan

        val result = repository.getCurrentActivePayoffPlan()

        assertEquals(testPayoffPlan, result)
        coVerify { payoffPlanDao.getCurrentActivePayoffPlan() }
    }

    @Test
    fun `insertPayoffPlan delegates to dao`() = runTest {
        coEvery { payoffPlanDao.insertPayoffPlan(testPayoffPlan) } returns 1L

        val result = repository.insertPayoffPlan(testPayoffPlan)

        assertEquals(1L, result)
        coVerify { payoffPlanDao.insertPayoffPlan(testPayoffPlan) }
    }

    @Test
    fun `setActivePayoffPlan deactivates other plans`() = runTest {
        coEvery { payoffPlanDao.deactivateOtherPlans(1L) } just Runs

        repository.setActivePayoffPlan(1L)

        coVerify { payoffPlanDao.deactivateOtherPlans(1L) }
    }

    @Test
    fun `getPaymentScheduleByPlan delegates to dao`() = runTest {
        every { paymentScheduleDao.getPaymentScheduleByPlan(1L) } returns flowOf(testPaymentSchedules)

        val result = repository.getPaymentScheduleByPlan(1L)

        verify { paymentScheduleDao.getPaymentScheduleByPlan(1L) }
    }

    @Test
    fun `insertPaymentSchedules delegates to dao`() = runTest {
        coEvery { paymentScheduleDao.insertPaymentSchedules(testPaymentSchedules) } just Runs

        repository.insertPaymentSchedules(testPaymentSchedules)

        coVerify { paymentScheduleDao.insertPaymentSchedules(testPaymentSchedules) }
    }

    @Test
    fun `getMilestonesByPlan delegates to dao`() = runTest {
        every { payoffMilestoneDao.getMilestonesByPlan(1L) } returns flowOf(testMilestones)

        val result = repository.getMilestonesByPlan(1L)

        verify { payoffMilestoneDao.getMilestonesByPlan(1L) }
    }

    @Test
    fun `getNextMilestone delegates to dao`() = runTest {
        coEvery { payoffMilestoneDao.getNextMilestone(1L) } returns testMilestones.first()

        val result = repository.getNextMilestone(1L)

        assertEquals(testMilestones.first(), result)
        coVerify { payoffMilestoneDao.getNextMilestone(1L) }
    }

    @Test
    fun `markMilestoneCompleted delegates to dao`() = runTest {
        val completedDate = LocalDate(2024, 7, 1)
        coEvery { payoffMilestoneDao.markMilestoneCompleted(1L, completedDate) } just Runs

        repository.markMilestoneCompleted(1L, completedDate)

        coVerify { payoffMilestoneDao.markMilestoneCompleted(1L, completedDate) }
    }

    @Test
    fun `createCompletePayoffPlan creates plan with schedules and milestones`() = runTest {
        // Mock the insert operations
        coEvery { payoffPlanDao.insertPayoffPlan(testPayoffPlan) } returns 1L
        coEvery { paymentScheduleDao.insertPaymentSchedules(any()) } just Runs
        coEvery { payoffMilestoneDao.insertMilestones(any()) } just Runs

        val result = repository.createCompletePayoffPlan(
            testPayoffPlan,
            testPaymentSchedules,
            testMilestones
        )

        assertEquals(1L, result)
        
        // Verify all components were inserted
        coVerify { payoffPlanDao.insertPayoffPlan(testPayoffPlan) }
        coVerify { 
            paymentScheduleDao.insertPaymentSchedules(
                match { schedules ->
                    schedules.all { it.payoffPlanId == 1L }
                }
            )
        }
        coVerify { 
            payoffMilestoneDao.insertMilestones(
                match { milestones ->
                    milestones.all { it.payoffPlanId == 1L }
                }
            )
        }
    }

    @Test
    fun `deleteCompletePayoffPlan removes all related data`() = runTest {
        coEvery { paymentScheduleDao.deletePaymentSchedulesByPlan(1L) } just Runs
        coEvery { payoffMilestoneDao.deleteMilestonesByPlan(1L) } just Runs
        coEvery { payoffPlanDao.getPayoffPlanById(1L) } returns testPayoffPlan
        coEvery { payoffPlanDao.deletePayoffPlan(testPayoffPlan) } just Runs

        repository.deleteCompletePayoffPlan(1L)

        // Verify deletion order: schedules, milestones, then plan
        coVerifyOrder {
            paymentScheduleDao.deletePaymentSchedulesByPlan(1L)
            payoffMilestoneDao.deleteMilestonesByPlan(1L)
            payoffPlanDao.getPayoffPlanById(1L)
            payoffPlanDao.deletePayoffPlan(testPayoffPlan)
        }
    }

    @Test
    fun `deleteCompletePayoffPlan handles missing plan gracefully`() = runTest {
        coEvery { paymentScheduleDao.deletePaymentSchedulesByPlan(1L) } just Runs
        coEvery { payoffMilestoneDao.deleteMilestonesByPlan(1L) } just Runs
        coEvery { payoffPlanDao.getPayoffPlanById(1L) } returns null

        repository.deleteCompletePayoffPlan(1L)

        // Should still delete schedules and milestones, but not attempt to delete null plan
        coVerify { paymentScheduleDao.deletePaymentSchedulesByPlan(1L) }
        coVerify { payoffMilestoneDao.deleteMilestonesByPlan(1L) }
        coVerify { payoffPlanDao.getPayoffPlanById(1L) }
        coVerify(exactly = 0) { payoffPlanDao.deletePayoffPlan(any()) }
    }

    @Test
    fun `getPaymentsDueInRange delegates to dao with correct parameters`() = runTest {
        val startDate = LocalDate(2024, 1, 1)
        val endDate = LocalDate(2024, 1, 31)
        every { paymentScheduleDao.getPaymentsDueInRange(startDate, endDate) } returns flowOf(testPaymentSchedules)

        val result = repository.getPaymentsDueInRange(startDate, endDate)

        verify { paymentScheduleDao.getPaymentsDueInRange(startDate, endDate) }
    }

    @Test
    fun `getUpcomingMilestones delegates to dao with correct date`() = runTest {
        val targetDate = LocalDate(2024, 6, 1)
        every { payoffMilestoneDao.getUpcomingMilestones(targetDate) } returns flowOf(testMilestones)

        val result = repository.getUpcomingMilestones(targetDate)

        verify { payoffMilestoneDao.getUpcomingMilestones(targetDate) }
    }
}
