  Manifest android  AlarmManager android.app  Application android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  Service android.app  Bundle android.app.Activity  
Composable android.app.Activity  BroadcastReceiver android.content  Context android.content  Intent android.content  Context !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  NotificationCompat !android.content.BroadcastReceiver  String !android.content.BroadcastReceiver  
ALARM_SERVICE android.content.Context  Bundle android.content.Context  
Composable android.content.Context  getSystemService android.content.Context  Bundle android.content.ContextWrapper  
Composable android.content.ContextWrapper  PackageManager android.content.pm  Build 
android.os  Bundle 
android.os  IBinder 
android.os  
getFloatArray android.os.BaseBundle  getStringArrayList android.os.BaseBundle  
getFloatArray android.os.Bundle  getStringArrayList android.os.Bundle  RecognitionListener android.speech  RecognizerIntent android.speech  SpeechRecognizer android.speech  CONFIDENCE_SCORES android.speech.SpeechRecognizer  ERROR_AUDIO android.speech.SpeechRecognizer  ERROR_CLIENT android.speech.SpeechRecognizer  ERROR_INSUFFICIENT_PERMISSIONS android.speech.SpeechRecognizer  
ERROR_NETWORK android.speech.SpeechRecognizer  ERROR_NETWORK_TIMEOUT android.speech.SpeechRecognizer  ERROR_NO_MATCH android.speech.SpeechRecognizer  ERROR_RECOGNIZER_BUSY android.speech.SpeechRecognizer  ERROR_SERVER android.speech.SpeechRecognizer  ERROR_SPEECH_TIMEOUT android.speech.SpeechRecognizer  RESULTS_RECOGNITION android.speech.SpeechRecognizer  Log android.util  Bundle  android.view.ContextThemeWrapper  
Composable  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  
Composable #androidx.activity.ComponentActivity  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  ActivityResultContracts !androidx.activity.result.contract  
Composable androidx.compose.animation  	LocalDate androidx.compose.animation  androidx androidx.compose.animation  com androidx.compose.animation  
Composable androidx.compose.animation.core  	LocalDate androidx.compose.animation.core  com androidx.compose.animation.core  BorderStroke androidx.compose.foundation  Canvas androidx.compose.foundation  Image androidx.compose.foundation  
background androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  	LocalDate "androidx.compose.foundation.layout  Pair "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  	GridCells %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  
selectable %androidx.compose.foundation.selection  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardOptions  androidx.compose.foundation.text  Colors androidx.compose.material  ColumnScope androidx.compose.material  
Composable androidx.compose.material  	LocalDate androidx.compose.material  
MaterialTheme androidx.compose.material  Pair androidx.compose.material  Shapes androidx.compose.material  SnackbarDuration androidx.compose.material  SnackbarHostState androidx.compose.material  
Typography androidx.compose.material  androidx androidx.compose.material  com androidx.compose.material  
darkColors androidx.compose.material  lightColors androidx.compose.material  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  CheckCircle ,androidx.compose.material.icons.Icons.Filled  Favorite ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  List ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  ShoppingCart ,androidx.compose.material.icons.Icons.Filled  Star ,androidx.compose.material.icons.Icons.Filled  
TrendingUp ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  CheckCircle &androidx.compose.material.icons.filled  ColumnScope &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  EnhancedBudgetUiState &androidx.compose.material.icons.filled  Favorite &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  List &androidx.compose.material.icons.filled  	LocalDate &androidx.compose.material.icons.filled  
LocalDateTime &androidx.compose.material.icons.filled  MutableStateFlow &androidx.compose.material.icons.filled  Pair &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  Screen &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  ShoppingCart &androidx.compose.material.icons.filled  Star &androidx.compose.material.icons.filled  	StateFlow &androidx.compose.material.icons.filled  
TrendingUp &androidx.compose.material.icons.filled  Triple &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  asStateFlow &androidx.compose.material.icons.filled  com &androidx.compose.material.icons.filled  listOf &androidx.compose.material.icons.filled  ColumnScope androidx.compose.runtime  
Composable androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  	LocalDate androidx.compose.runtime  Pair androidx.compose.runtime  androidx androidx.compose.runtime  com androidx.compose.runtime  getValue androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  clip androidx.compose.ui.draw  scale androidx.compose.ui.draw  Color androidx.compose.ui.graphics  Path androidx.compose.ui.graphics  	StrokeCap androidx.compose.ui.graphics  White "androidx.compose.ui.graphics.Color  White ,androidx.compose.ui.graphics.Color.Companion  invoke ,androidx.compose.ui.graphics.Color.Companion  	DrawScope &androidx.compose.ui.graphics.drawscope  Stroke &androidx.compose.ui.graphics.drawscope  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  contentDescription androidx.compose.ui.semantics  	semantics androidx.compose.ui.semantics  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  	TextAlign androidx.compose.ui.text.style  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Dialog androidx.compose.ui.window  NotificationCompat androidx.core.app  NotificationManagerCompat androidx.core.app  Bundle #androidx.core.app.ComponentActivity  
Composable #androidx.core.app.ComponentActivity  Builder $androidx.core.app.NotificationCompat  from +androidx.core.app.NotificationManagerCompat  
ContextCompat androidx.core.content  
hiltViewModel  androidx.hilt.navigation.compose  LiveData androidx.lifecycle  MutableLiveData androidx.lifecycle  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  getVALUE "androidx.lifecycle.MutableLiveData  getValue "androidx.lifecycle.MutableLiveData  setValue "androidx.lifecycle.MutableLiveData  value "androidx.lifecycle.MutableLiveData  AICoachUiState androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  BudgetCategory androidx.lifecycle.ViewModel  BudgetCategoryRepository androidx.lifecycle.ViewModel  BudgetRecommendationRepository androidx.lifecycle.ViewModel  BudgetRecommendationService androidx.lifecycle.ViewModel  
BudgetUiState androidx.lifecycle.ViewModel  
CreditCard androidx.lifecycle.ViewModel  CreditCardRepository androidx.lifecycle.ViewModel  DashboardUiState androidx.lifecycle.ViewModel  DebtUiState androidx.lifecycle.ViewModel  Double androidx.lifecycle.ViewModel  EnhancedBudgetUiState androidx.lifecycle.ViewModel  Expense androidx.lifecycle.ViewModel  ExpenseRepository androidx.lifecycle.ViewModel  ExpenseUiState androidx.lifecycle.ViewModel  Flow androidx.lifecycle.ViewModel  ImpulseControlUiState androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  	LocalDate androidx.lifecycle.ViewModel  
LocalDateTime androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  MainUiState androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  NotificationRepository androidx.lifecycle.ViewModel  OnboardingUiState androidx.lifecycle.ViewModel  Pair androidx.lifecycle.ViewModel  PayoffCalculation androidx.lifecycle.ViewModel  
PayoffPlan androidx.lifecycle.ViewModel  PayoffStrategy androidx.lifecycle.ViewModel  PurchaseDelayService androidx.lifecycle.ViewModel  SettingsUiState androidx.lifecycle.ViewModel  SharingStarted androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  	ThemeMode androidx.lifecycle.ViewModel  Triple androidx.lifecycle.ViewModel  UserPreferencesRepository androidx.lifecycle.ViewModel  WishlistItem androidx.lifecycle.ViewModel  WishlistRepository androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  com androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  stateIn androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  collectAsStateWithLifecycle androidx.lifecycle.compose  
NavController androidx.navigation  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  currentBackStackEntryAsState androidx.navigation.compose  rememberNavController androidx.navigation.compose  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  kotlinx 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  AIInteractionDao androidx.room.RoomDatabase  AccountabilityContactDao androidx.room.RoomDatabase  AchievementDao androidx.room.RoomDatabase  AlternativeProductDao androidx.room.RoomDatabase  BudgetAnalyticsDao androidx.room.RoomDatabase  BudgetCategoryDao androidx.room.RoomDatabase  BudgetRecommendationDao androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  
CreditCardDao androidx.room.RoomDatabase  DashboardWidgetDao androidx.room.RoomDatabase  
ExpenseDao androidx.room.RoomDatabase  FocusFlowDatabase androidx.room.RoomDatabase  FocusSessionDao androidx.room.RoomDatabase  HabitLogDao androidx.room.RoomDatabase  SpendingPatternDao androidx.room.RoomDatabase  SpendingReflectionDao androidx.room.RoomDatabase  TaskDao androidx.room.RoomDatabase  UserPreferencesDao androidx.room.RoomDatabase  UserStatsDao androidx.room.RoomDatabase  
VirtualPetDao androidx.room.RoomDatabase  VoiceCommandDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  WishlistItemDao androidx.room.RoomDatabase  BudgetAnalytics 
androidx.work  CoroutineScope 
androidx.work  CoroutineWorker 
androidx.work  Dispatchers 
androidx.work  SpendingPattern 
androidx.work  
SupervisorJob 
androidx.work  WorkerParameters 
androidx.work  mutableMapOf 
androidx.work  Context androidx.work.CoroutineWorker  Result androidx.work.CoroutineWorker  WorkerParameters androidx.work.CoroutineWorker  Context androidx.work.ListenableWorker  Result androidx.work.ListenableWorker  WorkerParameters androidx.work.ListenableWorker  
Composable 
com.focusflow  FocusFlowApp 
com.focusflow  FocusFlowApplication 
com.focusflow  MainActivity 
com.focusflow  MainAppContent 
com.focusflow  R 
com.focusflow  Bundle com.focusflow.MainActivity  
AIInteraction com.focusflow.data.dao  AIInteractionDao com.focusflow.data.dao  AccountabilityContact com.focusflow.data.dao  AccountabilityContactDao com.focusflow.data.dao  Achievement com.focusflow.data.dao  AchievementDao com.focusflow.data.dao  AlternativeProduct com.focusflow.data.dao  AlternativeProductDao com.focusflow.data.dao  Boolean com.focusflow.data.dao  BudgetAnalytics com.focusflow.data.dao  BudgetAnalyticsDao com.focusflow.data.dao  BudgetCategory com.focusflow.data.dao  BudgetCategoryDao com.focusflow.data.dao  BudgetRecommendation com.focusflow.data.dao  BudgetRecommendationDao com.focusflow.data.dao  CategoryVarianceAverage com.focusflow.data.dao  CommandTypeUsage com.focusflow.data.dao  
Converters com.focusflow.data.dao  
CreditCard com.focusflow.data.dao  
CreditCardDao com.focusflow.data.dao  Dao com.focusflow.data.dao  DashboardWidget com.focusflow.data.dao  DashboardWidgetDao com.focusflow.data.dao  Delete com.focusflow.data.dao  Double com.focusflow.data.dao  Expense com.focusflow.data.dao  
ExpenseDao com.focusflow.data.dao  Float com.focusflow.data.dao  FocusSession com.focusflow.data.dao  FocusSessionDao com.focusflow.data.dao  HabitLog com.focusflow.data.dao  HabitLogDao com.focusflow.data.dao  Insert com.focusflow.data.dao  Int com.focusflow.data.dao  List com.focusflow.data.dao  Long com.focusflow.data.dao  OnConflictStrategy com.focusflow.data.dao  PatternCategoryCount com.focusflow.data.dao  PatternTypeCount com.focusflow.data.dao  Query com.focusflow.data.dao  SingletonComponent com.focusflow.data.dao  SpendingPattern com.focusflow.data.dao  SpendingPatternDao com.focusflow.data.dao  SpendingReflection com.focusflow.data.dao  SpendingReflectionDao com.focusflow.data.dao  String com.focusflow.data.dao  Task com.focusflow.data.dao  TaskDao com.focusflow.data.dao  
TaskTypeCount com.focusflow.data.dao  Update com.focusflow.data.dao  UserPreferences com.focusflow.data.dao  UserPreferencesDao com.focusflow.data.dao  	UserStats com.focusflow.data.dao  UserStatsDao com.focusflow.data.dao  
VirtualPet com.focusflow.data.dao  
VirtualPetDao com.focusflow.data.dao  VoiceCommand com.focusflow.data.dao  VoiceCommandDao com.focusflow.data.dao  Volatile com.focusflow.data.dao  WishlistItem com.focusflow.data.dao  WishlistItemDao com.focusflow.data.dao  kotlinx com.focusflow.data.dao  
AIInteraction 'com.focusflow.data.dao.AIInteractionDao  Delete 'com.focusflow.data.dao.AIInteractionDao  Flow 'com.focusflow.data.dao.AIInteractionDao  Insert 'com.focusflow.data.dao.AIInteractionDao  Int 'com.focusflow.data.dao.AIInteractionDao  List 'com.focusflow.data.dao.AIInteractionDao  Long 'com.focusflow.data.dao.AIInteractionDao  Query 'com.focusflow.data.dao.AIInteractionDao  String 'com.focusflow.data.dao.AIInteractionDao  kotlinx 'com.focusflow.data.dao.AIInteractionDao  AccountabilityContact /com.focusflow.data.dao.AccountabilityContactDao  Delete /com.focusflow.data.dao.AccountabilityContactDao  Double /com.focusflow.data.dao.AccountabilityContactDao  Flow /com.focusflow.data.dao.AccountabilityContactDao  Insert /com.focusflow.data.dao.AccountabilityContactDao  Int /com.focusflow.data.dao.AccountabilityContactDao  List /com.focusflow.data.dao.AccountabilityContactDao  
LocalDateTime /com.focusflow.data.dao.AccountabilityContactDao  Long /com.focusflow.data.dao.AccountabilityContactDao  Query /com.focusflow.data.dao.AccountabilityContactDao  String /com.focusflow.data.dao.AccountabilityContactDao  Update /com.focusflow.data.dao.AccountabilityContactDao  Achievement %com.focusflow.data.dao.AchievementDao  Flow %com.focusflow.data.dao.AchievementDao  Insert %com.focusflow.data.dao.AchievementDao  Int %com.focusflow.data.dao.AchievementDao  List %com.focusflow.data.dao.AchievementDao  Long %com.focusflow.data.dao.AchievementDao  OnConflictStrategy %com.focusflow.data.dao.AchievementDao  Query %com.focusflow.data.dao.AchievementDao  String %com.focusflow.data.dao.AchievementDao  Update %com.focusflow.data.dao.AchievementDao  kotlinx %com.focusflow.data.dao.AchievementDao  AlternativeProduct ,com.focusflow.data.dao.AlternativeProductDao  Delete ,com.focusflow.data.dao.AlternativeProductDao  Double ,com.focusflow.data.dao.AlternativeProductDao  Flow ,com.focusflow.data.dao.AlternativeProductDao  Insert ,com.focusflow.data.dao.AlternativeProductDao  Int ,com.focusflow.data.dao.AlternativeProductDao  List ,com.focusflow.data.dao.AlternativeProductDao  
LocalDateTime ,com.focusflow.data.dao.AlternativeProductDao  Long ,com.focusflow.data.dao.AlternativeProductDao  Query ,com.focusflow.data.dao.AlternativeProductDao  String ,com.focusflow.data.dao.AlternativeProductDao  Update ,com.focusflow.data.dao.AlternativeProductDao  BudgetAnalytics )com.focusflow.data.dao.BudgetAnalyticsDao  CategoryVarianceAverage )com.focusflow.data.dao.BudgetAnalyticsDao  Delete )com.focusflow.data.dao.BudgetAnalyticsDao  Double )com.focusflow.data.dao.BudgetAnalyticsDao  Flow )com.focusflow.data.dao.BudgetAnalyticsDao  Insert )com.focusflow.data.dao.BudgetAnalyticsDao  Int )com.focusflow.data.dao.BudgetAnalyticsDao  List )com.focusflow.data.dao.BudgetAnalyticsDao  
LocalDateTime )com.focusflow.data.dao.BudgetAnalyticsDao  Long )com.focusflow.data.dao.BudgetAnalyticsDao  Query )com.focusflow.data.dao.BudgetAnalyticsDao  String )com.focusflow.data.dao.BudgetAnalyticsDao  Update )com.focusflow.data.dao.BudgetAnalyticsDao  deleteAnalytics )com.focusflow.data.dao.BudgetAnalyticsDao  deleteAnalyticsForPeriod )com.focusflow.data.dao.BudgetAnalyticsDao  deleteAnalyticsOlderThan )com.focusflow.data.dao.BudgetAnalyticsDao  updateAnalytics )com.focusflow.data.dao.BudgetAnalyticsDao  BudgetCategory (com.focusflow.data.dao.BudgetCategoryDao  Delete (com.focusflow.data.dao.BudgetCategoryDao  Double (com.focusflow.data.dao.BudgetCategoryDao  Flow (com.focusflow.data.dao.BudgetCategoryDao  Insert (com.focusflow.data.dao.BudgetCategoryDao  Int (com.focusflow.data.dao.BudgetCategoryDao  List (com.focusflow.data.dao.BudgetCategoryDao  Long (com.focusflow.data.dao.BudgetCategoryDao  Query (com.focusflow.data.dao.BudgetCategoryDao  String (com.focusflow.data.dao.BudgetCategoryDao  Update (com.focusflow.data.dao.BudgetCategoryDao  deleteBudgetCategory (com.focusflow.data.dao.BudgetCategoryDao  updateBudgetCategory (com.focusflow.data.dao.BudgetCategoryDao  updateSpentAmount (com.focusflow.data.dao.BudgetCategoryDao  Boolean .com.focusflow.data.dao.BudgetRecommendationDao  BudgetRecommendation .com.focusflow.data.dao.BudgetRecommendationDao  Delete .com.focusflow.data.dao.BudgetRecommendationDao  Double .com.focusflow.data.dao.BudgetRecommendationDao  Flow .com.focusflow.data.dao.BudgetRecommendationDao  Insert .com.focusflow.data.dao.BudgetRecommendationDao  Int .com.focusflow.data.dao.BudgetRecommendationDao  List .com.focusflow.data.dao.BudgetRecommendationDao  
LocalDateTime .com.focusflow.data.dao.BudgetRecommendationDao  Long .com.focusflow.data.dao.BudgetRecommendationDao  Query .com.focusflow.data.dao.BudgetRecommendationDao  String .com.focusflow.data.dao.BudgetRecommendationDao  Update .com.focusflow.data.dao.BudgetRecommendationDao  'deactivateOldRecommendationsForCategory .com.focusflow.data.dao.BudgetRecommendationDao  !deleteOldProcessedRecommendations .com.focusflow.data.dao.BudgetRecommendationDao  deleteRecommendation .com.focusflow.data.dao.BudgetRecommendationDao  updateRecommendation .com.focusflow.data.dao.BudgetRecommendationDao  updateRecommendationResponse .com.focusflow.data.dao.BudgetRecommendationDao  Double .com.focusflow.data.dao.CategoryVarianceAverage  String .com.focusflow.data.dao.CategoryVarianceAverage  Int 'com.focusflow.data.dao.CommandTypeUsage  String 'com.focusflow.data.dao.CommandTypeUsage  
CreditCard $com.focusflow.data.dao.CreditCardDao  Delete $com.focusflow.data.dao.CreditCardDao  Double $com.focusflow.data.dao.CreditCardDao  Flow $com.focusflow.data.dao.CreditCardDao  Insert $com.focusflow.data.dao.CreditCardDao  List $com.focusflow.data.dao.CreditCardDao  	LocalDate $com.focusflow.data.dao.CreditCardDao  Long $com.focusflow.data.dao.CreditCardDao  Query $com.focusflow.data.dao.CreditCardDao  Update $com.focusflow.data.dao.CreditCardDao  deactivateCreditCard $com.focusflow.data.dao.CreditCardDao  deleteCreditCard $com.focusflow.data.dao.CreditCardDao  updateCreditCard $com.focusflow.data.dao.CreditCardDao  Boolean )com.focusflow.data.dao.DashboardWidgetDao  DashboardWidget )com.focusflow.data.dao.DashboardWidgetDao  Delete )com.focusflow.data.dao.DashboardWidgetDao  Flow )com.focusflow.data.dao.DashboardWidgetDao  Insert )com.focusflow.data.dao.DashboardWidgetDao  Int )com.focusflow.data.dao.DashboardWidgetDao  List )com.focusflow.data.dao.DashboardWidgetDao  
LocalDateTime )com.focusflow.data.dao.DashboardWidgetDao  Long )com.focusflow.data.dao.DashboardWidgetDao  Query )com.focusflow.data.dao.DashboardWidgetDao  String )com.focusflow.data.dao.DashboardWidgetDao  Update )com.focusflow.data.dao.DashboardWidgetDao  Delete !com.focusflow.data.dao.ExpenseDao  Double !com.focusflow.data.dao.ExpenseDao  Expense !com.focusflow.data.dao.ExpenseDao  Flow !com.focusflow.data.dao.ExpenseDao  Insert !com.focusflow.data.dao.ExpenseDao  List !com.focusflow.data.dao.ExpenseDao  
LocalDateTime !com.focusflow.data.dao.ExpenseDao  Long !com.focusflow.data.dao.ExpenseDao  Query !com.focusflow.data.dao.ExpenseDao  String !com.focusflow.data.dao.ExpenseDao  Update !com.focusflow.data.dao.ExpenseDao  
deleteExpense !com.focusflow.data.dao.ExpenseDao  deleteExpenseById !com.focusflow.data.dao.ExpenseDao  
updateExpense !com.focusflow.data.dao.ExpenseDao  Delete &com.focusflow.data.dao.FocusSessionDao  Double &com.focusflow.data.dao.FocusSessionDao  Flow &com.focusflow.data.dao.FocusSessionDao  FocusSession &com.focusflow.data.dao.FocusSessionDao  Insert &com.focusflow.data.dao.FocusSessionDao  Int &com.focusflow.data.dao.FocusSessionDao  List &com.focusflow.data.dao.FocusSessionDao  
LocalDateTime &com.focusflow.data.dao.FocusSessionDao  Long &com.focusflow.data.dao.FocusSessionDao  Query &com.focusflow.data.dao.FocusSessionDao  String &com.focusflow.data.dao.FocusSessionDao  
TaskTypeCount &com.focusflow.data.dao.FocusSessionDao  Update &com.focusflow.data.dao.FocusSessionDao  Delete "com.focusflow.data.dao.HabitLogDao  Flow "com.focusflow.data.dao.HabitLogDao  HabitLog "com.focusflow.data.dao.HabitLogDao  Insert "com.focusflow.data.dao.HabitLogDao  Int "com.focusflow.data.dao.HabitLogDao  List "com.focusflow.data.dao.HabitLogDao  	LocalDate "com.focusflow.data.dao.HabitLogDao  Long "com.focusflow.data.dao.HabitLogDao  OnConflictStrategy "com.focusflow.data.dao.HabitLogDao  Query "com.focusflow.data.dao.HabitLogDao  String "com.focusflow.data.dao.HabitLogDao  Update "com.focusflow.data.dao.HabitLogDao  Int +com.focusflow.data.dao.PatternCategoryCount  String +com.focusflow.data.dao.PatternCategoryCount  Int 'com.focusflow.data.dao.PatternTypeCount  String 'com.focusflow.data.dao.PatternTypeCount  Boolean )com.focusflow.data.dao.SpendingPatternDao  Delete )com.focusflow.data.dao.SpendingPatternDao  Double )com.focusflow.data.dao.SpendingPatternDao  Flow )com.focusflow.data.dao.SpendingPatternDao  Insert )com.focusflow.data.dao.SpendingPatternDao  Int )com.focusflow.data.dao.SpendingPatternDao  List )com.focusflow.data.dao.SpendingPatternDao  
LocalDateTime )com.focusflow.data.dao.SpendingPatternDao  Long )com.focusflow.data.dao.SpendingPatternDao  PatternCategoryCount )com.focusflow.data.dao.SpendingPatternDao  PatternTypeCount )com.focusflow.data.dao.SpendingPatternDao  Query )com.focusflow.data.dao.SpendingPatternDao  SpendingPattern )com.focusflow.data.dao.SpendingPatternDao  String )com.focusflow.data.dao.SpendingPatternDao  Update )com.focusflow.data.dao.SpendingPatternDao  Delete ,com.focusflow.data.dao.SpendingReflectionDao  Double ,com.focusflow.data.dao.SpendingReflectionDao  Flow ,com.focusflow.data.dao.SpendingReflectionDao  Insert ,com.focusflow.data.dao.SpendingReflectionDao  Int ,com.focusflow.data.dao.SpendingReflectionDao  List ,com.focusflow.data.dao.SpendingReflectionDao  
LocalDateTime ,com.focusflow.data.dao.SpendingReflectionDao  Long ,com.focusflow.data.dao.SpendingReflectionDao  Query ,com.focusflow.data.dao.SpendingReflectionDao  SpendingReflection ,com.focusflow.data.dao.SpendingReflectionDao  String ,com.focusflow.data.dao.SpendingReflectionDao  Update ,com.focusflow.data.dao.SpendingReflectionDao  deleteReflection ,com.focusflow.data.dao.SpendingReflectionDao  deleteReflectionsOlderThan ,com.focusflow.data.dao.SpendingReflectionDao  updateReflection ,com.focusflow.data.dao.SpendingReflectionDao  Delete com.focusflow.data.dao.TaskDao  Flow com.focusflow.data.dao.TaskDao  Insert com.focusflow.data.dao.TaskDao  Int com.focusflow.data.dao.TaskDao  List com.focusflow.data.dao.TaskDao  
LocalDateTime com.focusflow.data.dao.TaskDao  Long com.focusflow.data.dao.TaskDao  Query com.focusflow.data.dao.TaskDao  String com.focusflow.data.dao.TaskDao  Task com.focusflow.data.dao.TaskDao  Update com.focusflow.data.dao.TaskDao  Int $com.focusflow.data.dao.TaskTypeCount  String $com.focusflow.data.dao.TaskTypeCount  Boolean )com.focusflow.data.dao.UserPreferencesDao  Float )com.focusflow.data.dao.UserPreferencesDao  Flow )com.focusflow.data.dao.UserPreferencesDao  Insert )com.focusflow.data.dao.UserPreferencesDao  OnConflictStrategy )com.focusflow.data.dao.UserPreferencesDao  Query )com.focusflow.data.dao.UserPreferencesDao  String )com.focusflow.data.dao.UserPreferencesDao  Update )com.focusflow.data.dao.UserPreferencesDao  UserPreferences )com.focusflow.data.dao.UserPreferencesDao  insertUserPreferences )com.focusflow.data.dao.UserPreferencesDao  updateAnimationsEnabled )com.focusflow.data.dao.UserPreferencesDao  updateBudgetPeriod )com.focusflow.data.dao.UserPreferencesDao  updateDarkModeEnabled )com.focusflow.data.dao.UserPreferencesDao  updateFontScale )com.focusflow.data.dao.UserPreferencesDao  updateFontSize )com.focusflow.data.dao.UserPreferencesDao  updateHighContrastMode )com.focusflow.data.dao.UserPreferencesDao  updateNotificationsEnabled )com.focusflow.data.dao.UserPreferencesDao  updateThemePreference )com.focusflow.data.dao.UserPreferencesDao  updateUserPreferences )com.focusflow.data.dao.UserPreferencesDao  updateVoiceInputEnabled )com.focusflow.data.dao.UserPreferencesDao  Double #com.focusflow.data.dao.UserStatsDao  Flow #com.focusflow.data.dao.UserStatsDao  Insert #com.focusflow.data.dao.UserStatsDao  Int #com.focusflow.data.dao.UserStatsDao  OnConflictStrategy #com.focusflow.data.dao.UserStatsDao  Query #com.focusflow.data.dao.UserStatsDao  Update #com.focusflow.data.dao.UserStatsDao  	UserStats #com.focusflow.data.dao.UserStatsDao  Flow $com.focusflow.data.dao.VirtualPetDao  Insert $com.focusflow.data.dao.VirtualPetDao  Int $com.focusflow.data.dao.VirtualPetDao  OnConflictStrategy $com.focusflow.data.dao.VirtualPetDao  Query $com.focusflow.data.dao.VirtualPetDao  Update $com.focusflow.data.dao.VirtualPetDao  
VirtualPet $com.focusflow.data.dao.VirtualPetDao  kotlinx $com.focusflow.data.dao.VirtualPetDao  CommandTypeUsage &com.focusflow.data.dao.VoiceCommandDao  Delete &com.focusflow.data.dao.VoiceCommandDao  Double &com.focusflow.data.dao.VoiceCommandDao  Flow &com.focusflow.data.dao.VoiceCommandDao  Insert &com.focusflow.data.dao.VoiceCommandDao  Int &com.focusflow.data.dao.VoiceCommandDao  List &com.focusflow.data.dao.VoiceCommandDao  
LocalDateTime &com.focusflow.data.dao.VoiceCommandDao  Long &com.focusflow.data.dao.VoiceCommandDao  Query &com.focusflow.data.dao.VoiceCommandDao  String &com.focusflow.data.dao.VoiceCommandDao  Update &com.focusflow.data.dao.VoiceCommandDao  VoiceCommand &com.focusflow.data.dao.VoiceCommandDao  Boolean &com.focusflow.data.dao.WishlistItemDao  Delete &com.focusflow.data.dao.WishlistItemDao  Double &com.focusflow.data.dao.WishlistItemDao  Flow &com.focusflow.data.dao.WishlistItemDao  Insert &com.focusflow.data.dao.WishlistItemDao  Int &com.focusflow.data.dao.WishlistItemDao  List &com.focusflow.data.dao.WishlistItemDao  
LocalDateTime &com.focusflow.data.dao.WishlistItemDao  Long &com.focusflow.data.dao.WishlistItemDao  Query &com.focusflow.data.dao.WishlistItemDao  String &com.focusflow.data.dao.WishlistItemDao  Update &com.focusflow.data.dao.WishlistItemDao  WishlistItem &com.focusflow.data.dao.WishlistItemDao  deletePurchasedItemsOlderThan &com.focusflow.data.dao.WishlistItemDao  deleteWishlistItem &com.focusflow.data.dao.WishlistItemDao  markAsPurchased &com.focusflow.data.dao.WishlistItemDao  removeDelay &com.focusflow.data.dao.WishlistItemDao  updateReflection &com.focusflow.data.dao.WishlistItemDao  updateWishlistItem &com.focusflow.data.dao.WishlistItemDao  
AIInteraction com.focusflow.data.database  AIInteractionDao com.focusflow.data.database  AccountabilityContact com.focusflow.data.database  AccountabilityContactDao com.focusflow.data.database  Achievement com.focusflow.data.database  AchievementDao com.focusflow.data.database  AlternativeProduct com.focusflow.data.database  AlternativeProductDao com.focusflow.data.database  BudgetAnalytics com.focusflow.data.database  BudgetAnalyticsDao com.focusflow.data.database  BudgetCategory com.focusflow.data.database  BudgetCategoryDao com.focusflow.data.database  BudgetRecommendation com.focusflow.data.database  BudgetRecommendationDao com.focusflow.data.database  
Converters com.focusflow.data.database  
CreditCard com.focusflow.data.database  
CreditCardDao com.focusflow.data.database  DashboardWidget com.focusflow.data.database  DashboardWidgetDao com.focusflow.data.database  Expense com.focusflow.data.database  
ExpenseDao com.focusflow.data.database  FocusFlowDatabase com.focusflow.data.database  FocusSession com.focusflow.data.database  FocusSessionDao com.focusflow.data.database  HabitLog com.focusflow.data.database  HabitLogDao com.focusflow.data.database  SpendingPattern com.focusflow.data.database  SpendingPatternDao com.focusflow.data.database  SpendingReflection com.focusflow.data.database  SpendingReflectionDao com.focusflow.data.database  String com.focusflow.data.database  Task com.focusflow.data.database  TaskDao com.focusflow.data.database  UserPreferences com.focusflow.data.database  UserPreferencesDao com.focusflow.data.database  	UserStats com.focusflow.data.database  UserStatsDao com.focusflow.data.database  
VirtualPet com.focusflow.data.database  
VirtualPetDao com.focusflow.data.database  VoiceCommand com.focusflow.data.database  VoiceCommandDao com.focusflow.data.database  Volatile com.focusflow.data.database  WishlistItem com.focusflow.data.database  WishlistItemDao com.focusflow.data.database  	LocalDate &com.focusflow.data.database.Converters  
LocalDateTime &com.focusflow.data.database.Converters  String &com.focusflow.data.database.Converters  
TypeConverter &com.focusflow.data.database.Converters  AIInteractionDao -com.focusflow.data.database.FocusFlowDatabase  AccountabilityContactDao -com.focusflow.data.database.FocusFlowDatabase  AchievementDao -com.focusflow.data.database.FocusFlowDatabase  AlternativeProductDao -com.focusflow.data.database.FocusFlowDatabase  BudgetAnalyticsDao -com.focusflow.data.database.FocusFlowDatabase  BudgetCategoryDao -com.focusflow.data.database.FocusFlowDatabase  BudgetRecommendationDao -com.focusflow.data.database.FocusFlowDatabase  Context -com.focusflow.data.database.FocusFlowDatabase  
CreditCardDao -com.focusflow.data.database.FocusFlowDatabase  DashboardWidgetDao -com.focusflow.data.database.FocusFlowDatabase  
ExpenseDao -com.focusflow.data.database.FocusFlowDatabase  FocusFlowDatabase -com.focusflow.data.database.FocusFlowDatabase  FocusSessionDao -com.focusflow.data.database.FocusFlowDatabase  HabitLogDao -com.focusflow.data.database.FocusFlowDatabase  SpendingPatternDao -com.focusflow.data.database.FocusFlowDatabase  SpendingReflectionDao -com.focusflow.data.database.FocusFlowDatabase  TaskDao -com.focusflow.data.database.FocusFlowDatabase  UserPreferencesDao -com.focusflow.data.database.FocusFlowDatabase  UserStatsDao -com.focusflow.data.database.FocusFlowDatabase  
VirtualPetDao -com.focusflow.data.database.FocusFlowDatabase  VoiceCommandDao -com.focusflow.data.database.FocusFlowDatabase  Volatile -com.focusflow.data.database.FocusFlowDatabase  WishlistItemDao -com.focusflow.data.database.FocusFlowDatabase  AIInteractionDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  AccountabilityContactDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  AchievementDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  AlternativeProductDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  BudgetAnalyticsDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  BudgetCategoryDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  BudgetRecommendationDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  Context 7com.focusflow.data.database.FocusFlowDatabase.Companion  
CreditCardDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  DashboardWidgetDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  
ExpenseDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  FocusFlowDatabase 7com.focusflow.data.database.FocusFlowDatabase.Companion  FocusSessionDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  HabitLogDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  SpendingPatternDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  SpendingReflectionDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  TaskDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  UserPreferencesDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  UserStatsDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  
VirtualPetDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  VoiceCommandDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  Volatile 7com.focusflow.data.database.FocusFlowDatabase.Companion  WishlistItemDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  
AIInteraction com.focusflow.data.model  AIInteractionDao com.focusflow.data.model  AccountabilityContact com.focusflow.data.model  AccountabilityContactDao com.focusflow.data.model  Achievement com.focusflow.data.model  AchievementDao com.focusflow.data.model  AlternativeProduct com.focusflow.data.model  AlternativeProductDao com.focusflow.data.model  Boolean com.focusflow.data.model  BudgetAnalytics com.focusflow.data.model  BudgetAnalyticsDao com.focusflow.data.model  BudgetCategory com.focusflow.data.model  BudgetCategoryDao com.focusflow.data.model  BudgetRecommendation com.focusflow.data.model  BudgetRecommendationDao com.focusflow.data.model  
Converters com.focusflow.data.model  CoroutineScope com.focusflow.data.model  CoroutineWorker com.focusflow.data.model  
CreditCard com.focusflow.data.model  
CreditCardDao com.focusflow.data.model  DashboardWidget com.focusflow.data.model  DashboardWidgetDao com.focusflow.data.model  Dispatchers com.focusflow.data.model  Double com.focusflow.data.model  Expense com.focusflow.data.model  
ExpenseDao com.focusflow.data.model  Float com.focusflow.data.model  FocusSession com.focusflow.data.model  FocusSessionDao com.focusflow.data.model  HabitLog com.focusflow.data.model  HabitLogDao com.focusflow.data.model  Income com.focusflow.data.model  Int com.focusflow.data.model  List com.focusflow.data.model  	LocalDate com.focusflow.data.model  Long com.focusflow.data.model  SpendingPattern com.focusflow.data.model  SpendingPatternDao com.focusflow.data.model  SpendingReflection com.focusflow.data.model  SpendingReflectionDao com.focusflow.data.model  String com.focusflow.data.model  StringListConverter com.focusflow.data.model  
SupervisorJob com.focusflow.data.model  Task com.focusflow.data.model  TaskDao com.focusflow.data.model  UserPreferences com.focusflow.data.model  UserPreferencesDao com.focusflow.data.model  	UserStats com.focusflow.data.model  UserStatsDao com.focusflow.data.model  
VirtualPet com.focusflow.data.model  
VirtualPetDao com.focusflow.data.model  VoiceCommand com.focusflow.data.model  VoiceCommandDao com.focusflow.data.model  Volatile com.focusflow.data.model  WishlistItem com.focusflow.data.model  WishlistItemDao com.focusflow.data.model  WorkerParameters com.focusflow.data.model  kotlinx com.focusflow.data.model  mutableMapOf com.focusflow.data.model  
LocalDateTime &com.focusflow.data.model.AIInteraction  Long &com.focusflow.data.model.AIInteraction  
PrimaryKey &com.focusflow.data.model.AIInteraction  String &com.focusflow.data.model.AIInteraction  Boolean .com.focusflow.data.model.AccountabilityContact  Double .com.focusflow.data.model.AccountabilityContact  Int .com.focusflow.data.model.AccountabilityContact  
LocalDateTime .com.focusflow.data.model.AccountabilityContact  Long .com.focusflow.data.model.AccountabilityContact  
PrimaryKey .com.focusflow.data.model.AccountabilityContact  String .com.focusflow.data.model.AccountabilityContact  Boolean $com.focusflow.data.model.Achievement  Int $com.focusflow.data.model.Achievement  
LocalDateTime $com.focusflow.data.model.Achievement  Long $com.focusflow.data.model.Achievement  
PrimaryKey $com.focusflow.data.model.Achievement  String $com.focusflow.data.model.Achievement  Boolean +com.focusflow.data.model.AlternativeProduct  Double +com.focusflow.data.model.AlternativeProduct  Int +com.focusflow.data.model.AlternativeProduct  
LocalDateTime +com.focusflow.data.model.AlternativeProduct  Long +com.focusflow.data.model.AlternativeProduct  
PrimaryKey +com.focusflow.data.model.AlternativeProduct  String +com.focusflow.data.model.AlternativeProduct  Boolean (com.focusflow.data.model.BudgetAnalytics  Double (com.focusflow.data.model.BudgetAnalytics  Int (com.focusflow.data.model.BudgetAnalytics  
LocalDateTime (com.focusflow.data.model.BudgetAnalytics  Long (com.focusflow.data.model.BudgetAnalytics  
PrimaryKey (com.focusflow.data.model.BudgetAnalytics  String (com.focusflow.data.model.BudgetAnalytics  Boolean 'com.focusflow.data.model.BudgetCategory  Double 'com.focusflow.data.model.BudgetCategory  Int 'com.focusflow.data.model.BudgetCategory  Long 'com.focusflow.data.model.BudgetCategory  
PrimaryKey 'com.focusflow.data.model.BudgetCategory  String 'com.focusflow.data.model.BudgetCategory  kotlinx 'com.focusflow.data.model.BudgetCategory  Boolean -com.focusflow.data.model.BudgetRecommendation  Double -com.focusflow.data.model.BudgetRecommendation  Int -com.focusflow.data.model.BudgetRecommendation  
LocalDateTime -com.focusflow.data.model.BudgetRecommendation  Long -com.focusflow.data.model.BudgetRecommendation  
PrimaryKey -com.focusflow.data.model.BudgetRecommendation  String -com.focusflow.data.model.BudgetRecommendation  Boolean #com.focusflow.data.model.CreditCard  Double #com.focusflow.data.model.CreditCard  	LocalDate #com.focusflow.data.model.CreditCard  Long #com.focusflow.data.model.CreditCard  
PrimaryKey #com.focusflow.data.model.CreditCard  String #com.focusflow.data.model.CreditCard  Boolean (com.focusflow.data.model.DashboardWidget  Int (com.focusflow.data.model.DashboardWidget  
LocalDateTime (com.focusflow.data.model.DashboardWidget  Long (com.focusflow.data.model.DashboardWidget  
PrimaryKey (com.focusflow.data.model.DashboardWidget  String (com.focusflow.data.model.DashboardWidget  Boolean  com.focusflow.data.model.Expense  Double  com.focusflow.data.model.Expense  
LocalDateTime  com.focusflow.data.model.Expense  Long  com.focusflow.data.model.Expense  
PrimaryKey  com.focusflow.data.model.Expense  String  com.focusflow.data.model.Expense  Boolean %com.focusflow.data.model.FocusSession  Int %com.focusflow.data.model.FocusSession  
LocalDateTime %com.focusflow.data.model.FocusSession  Long %com.focusflow.data.model.FocusSession  
PrimaryKey %com.focusflow.data.model.FocusSession  String %com.focusflow.data.model.FocusSession  	LocalDate !com.focusflow.data.model.HabitLog  Long !com.focusflow.data.model.HabitLog  
PrimaryKey !com.focusflow.data.model.HabitLog  String !com.focusflow.data.model.HabitLog  Boolean com.focusflow.data.model.Income  Double com.focusflow.data.model.Income  	LocalDate com.focusflow.data.model.Income  Long com.focusflow.data.model.Income  
PrimaryKey com.focusflow.data.model.Income  String com.focusflow.data.model.Income  Boolean (com.focusflow.data.model.SpendingPattern  Double (com.focusflow.data.model.SpendingPattern  Int (com.focusflow.data.model.SpendingPattern  
LocalDateTime (com.focusflow.data.model.SpendingPattern  Long (com.focusflow.data.model.SpendingPattern  
PrimaryKey (com.focusflow.data.model.SpendingPattern  String (com.focusflow.data.model.SpendingPattern  Boolean +com.focusflow.data.model.SpendingReflection  Double +com.focusflow.data.model.SpendingReflection  Int +com.focusflow.data.model.SpendingReflection  
LocalDateTime +com.focusflow.data.model.SpendingReflection  Long +com.focusflow.data.model.SpendingReflection  
PrimaryKey +com.focusflow.data.model.SpendingReflection  String +com.focusflow.data.model.SpendingReflection  List ,com.focusflow.data.model.StringListConverter  String ,com.focusflow.data.model.StringListConverter  
TypeConverter ,com.focusflow.data.model.StringListConverter  Boolean com.focusflow.data.model.Task  Int com.focusflow.data.model.Task  
LocalDateTime com.focusflow.data.model.Task  Long com.focusflow.data.model.Task  
PrimaryKey com.focusflow.data.model.Task  String com.focusflow.data.model.Task  Boolean (com.focusflow.data.model.UserPreferences  Double (com.focusflow.data.model.UserPreferences  Float (com.focusflow.data.model.UserPreferences  Long (com.focusflow.data.model.UserPreferences  
PrimaryKey (com.focusflow.data.model.UserPreferences  String (com.focusflow.data.model.UserPreferences  Double "com.focusflow.data.model.UserStats  Int "com.focusflow.data.model.UserStats  
LocalDateTime "com.focusflow.data.model.UserStats  Long "com.focusflow.data.model.UserStats  
PrimaryKey "com.focusflow.data.model.UserStats  Int #com.focusflow.data.model.VirtualPet  List #com.focusflow.data.model.VirtualPet  
LocalDateTime #com.focusflow.data.model.VirtualPet  Long #com.focusflow.data.model.VirtualPet  
PrimaryKey #com.focusflow.data.model.VirtualPet  String #com.focusflow.data.model.VirtualPet  Boolean %com.focusflow.data.model.VoiceCommand  Double %com.focusflow.data.model.VoiceCommand  Int %com.focusflow.data.model.VoiceCommand  
LocalDateTime %com.focusflow.data.model.VoiceCommand  Long %com.focusflow.data.model.VoiceCommand  
PrimaryKey %com.focusflow.data.model.VoiceCommand  String %com.focusflow.data.model.VoiceCommand  Boolean %com.focusflow.data.model.WishlistItem  Double %com.focusflow.data.model.WishlistItem  Int %com.focusflow.data.model.WishlistItem  
LocalDateTime %com.focusflow.data.model.WishlistItem  Long %com.focusflow.data.model.WishlistItem  
PrimaryKey %com.focusflow.data.model.WishlistItem  String %com.focusflow.data.model.WishlistItem  Boolean com.focusflow.data.repository  BudgetAnalyticsRepository com.focusflow.data.repository  BudgetCategoryRepository com.focusflow.data.repository  BudgetRecommendationRepository com.focusflow.data.repository  CreditCardRepository com.focusflow.data.repository  Double com.focusflow.data.repository  ExpenseRepository com.focusflow.data.repository  Float com.focusflow.data.repository  Int com.focusflow.data.repository  List com.focusflow.data.repository  
LocalDateTime com.focusflow.data.repository  Long com.focusflow.data.repository  NotificationRepository com.focusflow.data.repository  Pair com.focusflow.data.repository  SpendingReflectionRepository com.focusflow.data.repository  String com.focusflow.data.repository  UserPreferencesRepository com.focusflow.data.repository  WishlistRepository com.focusflow.data.repository  BudgetAnalytics 7com.focusflow.data.repository.BudgetAnalyticsRepository  BudgetAnalyticsDao 7com.focusflow.data.repository.BudgetAnalyticsRepository  CategoryVarianceAverage 7com.focusflow.data.repository.BudgetAnalyticsRepository  Double 7com.focusflow.data.repository.BudgetAnalyticsRepository  Flow 7com.focusflow.data.repository.BudgetAnalyticsRepository  Inject 7com.focusflow.data.repository.BudgetAnalyticsRepository  Int 7com.focusflow.data.repository.BudgetAnalyticsRepository  List 7com.focusflow.data.repository.BudgetAnalyticsRepository  
LocalDateTime 7com.focusflow.data.repository.BudgetAnalyticsRepository  Long 7com.focusflow.data.repository.BudgetAnalyticsRepository  String 7com.focusflow.data.repository.BudgetAnalyticsRepository  budgetAnalyticsDao 7com.focusflow.data.repository.BudgetAnalyticsRepository  BudgetCategory 6com.focusflow.data.repository.BudgetCategoryRepository  BudgetCategoryDao 6com.focusflow.data.repository.BudgetCategoryRepository  Double 6com.focusflow.data.repository.BudgetCategoryRepository  Flow 6com.focusflow.data.repository.BudgetCategoryRepository  Inject 6com.focusflow.data.repository.BudgetCategoryRepository  List 6com.focusflow.data.repository.BudgetCategoryRepository  Long 6com.focusflow.data.repository.BudgetCategoryRepository  String 6com.focusflow.data.repository.BudgetCategoryRepository  budgetCategoryDao 6com.focusflow.data.repository.BudgetCategoryRepository  BudgetRecommendation <com.focusflow.data.repository.BudgetRecommendationRepository  BudgetRecommendationDao <com.focusflow.data.repository.BudgetRecommendationRepository  Double <com.focusflow.data.repository.BudgetRecommendationRepository  Flow <com.focusflow.data.repository.BudgetRecommendationRepository  Inject <com.focusflow.data.repository.BudgetRecommendationRepository  Int <com.focusflow.data.repository.BudgetRecommendationRepository  List <com.focusflow.data.repository.BudgetRecommendationRepository  
LocalDateTime <com.focusflow.data.repository.BudgetRecommendationRepository  Long <com.focusflow.data.repository.BudgetRecommendationRepository  String <com.focusflow.data.repository.BudgetRecommendationRepository  budgetRecommendationDao <com.focusflow.data.repository.BudgetRecommendationRepository  
CreditCard 2com.focusflow.data.repository.CreditCardRepository  
CreditCardDao 2com.focusflow.data.repository.CreditCardRepository  Double 2com.focusflow.data.repository.CreditCardRepository  Flow 2com.focusflow.data.repository.CreditCardRepository  Inject 2com.focusflow.data.repository.CreditCardRepository  List 2com.focusflow.data.repository.CreditCardRepository  	LocalDate 2com.focusflow.data.repository.CreditCardRepository  Long 2com.focusflow.data.repository.CreditCardRepository  
creditCardDao 2com.focusflow.data.repository.CreditCardRepository  getAllActiveCreditCards 2com.focusflow.data.repository.CreditCardRepository  Double /com.focusflow.data.repository.ExpenseRepository  Expense /com.focusflow.data.repository.ExpenseRepository  
ExpenseDao /com.focusflow.data.repository.ExpenseRepository  Flow /com.focusflow.data.repository.ExpenseRepository  Inject /com.focusflow.data.repository.ExpenseRepository  List /com.focusflow.data.repository.ExpenseRepository  
LocalDateTime /com.focusflow.data.repository.ExpenseRepository  Long /com.focusflow.data.repository.ExpenseRepository  String /com.focusflow.data.repository.ExpenseRepository  
expenseDao /com.focusflow.data.repository.ExpenseRepository  getAllCategories /com.focusflow.data.repository.ExpenseRepository  getAllExpenses /com.focusflow.data.repository.ExpenseRepository  ApplicationContext 4com.focusflow.data.repository.NotificationRepository  Boolean 4com.focusflow.data.repository.NotificationRepository  Context 4com.focusflow.data.repository.NotificationRepository  Double 4com.focusflow.data.repository.NotificationRepository  FocusFlowNotificationManager 4com.focusflow.data.repository.NotificationRepository  Inject 4com.focusflow.data.repository.NotificationRepository  Int 4com.focusflow.data.repository.NotificationRepository  Long 4com.focusflow.data.repository.NotificationRepository  Pair 4com.focusflow.data.repository.NotificationRepository  String 4com.focusflow.data.repository.NotificationRepository  UserPreferencesRepository 4com.focusflow.data.repository.NotificationRepository  Double :com.focusflow.data.repository.SpendingReflectionRepository  Flow :com.focusflow.data.repository.SpendingReflectionRepository  Inject :com.focusflow.data.repository.SpendingReflectionRepository  Int :com.focusflow.data.repository.SpendingReflectionRepository  List :com.focusflow.data.repository.SpendingReflectionRepository  
LocalDateTime :com.focusflow.data.repository.SpendingReflectionRepository  Long :com.focusflow.data.repository.SpendingReflectionRepository  SpendingReflection :com.focusflow.data.repository.SpendingReflectionRepository  SpendingReflectionDao :com.focusflow.data.repository.SpendingReflectionRepository  String :com.focusflow.data.repository.SpendingReflectionRepository  spendingReflectionDao :com.focusflow.data.repository.SpendingReflectionRepository  Boolean 7com.focusflow.data.repository.UserPreferencesRepository  Float 7com.focusflow.data.repository.UserPreferencesRepository  Flow 7com.focusflow.data.repository.UserPreferencesRepository  Inject 7com.focusflow.data.repository.UserPreferencesRepository  String 7com.focusflow.data.repository.UserPreferencesRepository  UserPreferences 7com.focusflow.data.repository.UserPreferencesRepository  UserPreferencesDao 7com.focusflow.data.repository.UserPreferencesRepository  userPreferencesDao 7com.focusflow.data.repository.UserPreferencesRepository  Boolean 0com.focusflow.data.repository.WishlistRepository  Double 0com.focusflow.data.repository.WishlistRepository  Flow 0com.focusflow.data.repository.WishlistRepository  Inject 0com.focusflow.data.repository.WishlistRepository  Int 0com.focusflow.data.repository.WishlistRepository  List 0com.focusflow.data.repository.WishlistRepository  
LocalDateTime 0com.focusflow.data.repository.WishlistRepository  Long 0com.focusflow.data.repository.WishlistRepository  String 0com.focusflow.data.repository.WishlistRepository  WishlistItem 0com.focusflow.data.repository.WishlistRepository  WishlistItemDao 0com.focusflow.data.repository.WishlistRepository  getActiveDelayItems 0com.focusflow.data.repository.WishlistRepository  getAllActiveWishlistItems 0com.focusflow.data.repository.WishlistRepository  wishlistItemDao 0com.focusflow.data.repository.WishlistRepository  AIInteractionDao com.focusflow.di  AccountabilityContactDao com.focusflow.di  AchievementDao com.focusflow.di  AlternativeProductDao com.focusflow.di  BudgetAnalyticsDao com.focusflow.di  BudgetCategoryDao com.focusflow.di  BudgetRecommendationDao com.focusflow.di  
CreditCardDao com.focusflow.di  DashboardWidgetDao com.focusflow.di  DatabaseModule com.focusflow.di  
ExpenseDao com.focusflow.di  FocusSessionDao com.focusflow.di  HabitLogDao com.focusflow.di  SingletonComponent com.focusflow.di  SpendingPatternDao com.focusflow.di  SpendingReflectionDao com.focusflow.di  TaskDao com.focusflow.di  UserPreferencesDao com.focusflow.di  UserStatsDao com.focusflow.di  
VirtualPetDao com.focusflow.di  VoiceCommandDao com.focusflow.di  WishlistItemDao com.focusflow.di  AIInteractionDao com.focusflow.di.DatabaseModule  AccountabilityContactDao com.focusflow.di.DatabaseModule  AchievementDao com.focusflow.di.DatabaseModule  AlternativeProductDao com.focusflow.di.DatabaseModule  ApplicationContext com.focusflow.di.DatabaseModule  BudgetAnalyticsDao com.focusflow.di.DatabaseModule  BudgetCategoryDao com.focusflow.di.DatabaseModule  BudgetRecommendationDao com.focusflow.di.DatabaseModule  Context com.focusflow.di.DatabaseModule  
CreditCardDao com.focusflow.di.DatabaseModule  DashboardWidgetDao com.focusflow.di.DatabaseModule  
ExpenseDao com.focusflow.di.DatabaseModule  FocusFlowDatabase com.focusflow.di.DatabaseModule  FocusSessionDao com.focusflow.di.DatabaseModule  HabitLogDao com.focusflow.di.DatabaseModule  Provides com.focusflow.di.DatabaseModule  	Singleton com.focusflow.di.DatabaseModule  SpendingPatternDao com.focusflow.di.DatabaseModule  SpendingReflectionDao com.focusflow.di.DatabaseModule  TaskDao com.focusflow.di.DatabaseModule  UserPreferencesDao com.focusflow.di.DatabaseModule  UserStatsDao com.focusflow.di.DatabaseModule  
VirtualPetDao com.focusflow.di.DatabaseModule  VoiceCommandDao com.focusflow.di.DatabaseModule  WishlistItemDao com.focusflow.di.DatabaseModule  CheckCircle com.focusflow.navigation  Favorite com.focusflow.navigation  Home com.focusflow.navigation  List com.focusflow.navigation  Person com.focusflow.navigation  Screen com.focusflow.navigation  Settings com.focusflow.navigation  ShoppingCart com.focusflow.navigation  Star com.focusflow.navigation  String com.focusflow.navigation  
TrendingUp com.focusflow.navigation  bottomNavItems com.focusflow.navigation  listOf com.focusflow.navigation  AICoach com.focusflow.navigation.Screen  CheckCircle com.focusflow.navigation.Screen  	Dashboard com.focusflow.navigation.Screen  Debt com.focusflow.navigation.Screen  Expenses com.focusflow.navigation.Screen  Favorite com.focusflow.navigation.Screen  Habits com.focusflow.navigation.Screen  Home com.focusflow.navigation.Screen  Icons com.focusflow.navigation.Screen  ImageVector com.focusflow.navigation.Screen  List com.focusflow.navigation.Screen  Person com.focusflow.navigation.Screen  Screen com.focusflow.navigation.Screen  Settings com.focusflow.navigation.Screen  ShoppingCart com.focusflow.navigation.Screen  Star com.focusflow.navigation.Screen  String com.focusflow.navigation.Screen  Tasks com.focusflow.navigation.Screen  
TrendingUp com.focusflow.navigation.Screen  Icons 'com.focusflow.navigation.Screen.AICoach  Person 'com.focusflow.navigation.Screen.AICoach  Icons &com.focusflow.navigation.Screen.Budget  List &com.focusflow.navigation.Screen.Budget  Home )com.focusflow.navigation.Screen.Dashboard  Icons )com.focusflow.navigation.Screen.Dashboard  Icons $com.focusflow.navigation.Screen.Debt  Star $com.focusflow.navigation.Screen.Debt  Icons (com.focusflow.navigation.Screen.Expenses  ShoppingCart (com.focusflow.navigation.Screen.Expenses  Favorite &com.focusflow.navigation.Screen.Habits  Icons &com.focusflow.navigation.Screen.Habits  Icons -com.focusflow.navigation.Screen.PayoffPlanner  
TrendingUp -com.focusflow.navigation.Screen.PayoffPlanner  Icons (com.focusflow.navigation.Screen.Settings  Settings (com.focusflow.navigation.Screen.Settings  CheckCircle %com.focusflow.navigation.Screen.Tasks  Icons %com.focusflow.navigation.Screen.Tasks  
AlarmReceiver com.focusflow.receiver  String com.focusflow.receiver  Context $com.focusflow.receiver.AlarmReceiver  Intent $com.focusflow.receiver.AlarmReceiver  NotificationCompat $com.focusflow.receiver.AlarmReceiver  String $com.focusflow.receiver.AlarmReceiver  AdvancedAnalyticsService com.focusflow.service  AnalyticsPrecomputeWorker com.focusflow.service  Any com.focusflow.service  Boolean com.focusflow.service  BudgetAnalytics com.focusflow.service  BudgetCategory com.focusflow.service  BudgetRecommendationService com.focusflow.service  	ByteArray com.focusflow.service  CacheCleanupWorker com.focusflow.service  Context com.focusflow.service  CoroutineScope com.focusflow.service  CoroutineWorker com.focusflow.service  
CreditCard com.focusflow.service  DelayPeriodOption com.focusflow.service  DelayRecommendation com.focusflow.service  DelayStatistics com.focusflow.service  Dispatchers com.focusflow.service  Double com.focusflow.service  Expense com.focusflow.service  Float com.focusflow.service  FocusFlowNotificationManager com.focusflow.service  FocusSessionStatistics com.focusflow.service  FocusTimerService com.focusflow.service  GamificationService com.focusflow.service  Int com.focusflow.service  Job com.focusflow.service  List com.focusflow.service  	LocalDate com.focusflow.service  
LocalDateTime com.focusflow.service  Long com.focusflow.service  Map com.focusflow.service  Month com.focusflow.service  MutableLiveData com.focusflow.service  MutableStateFlow com.focusflow.service  NotificationManagerCompat com.focusflow.service  NotificationService com.focusflow.service  Pair com.focusflow.service  PerformanceOptimizationService com.focusflow.service  PurchaseDelayService com.focusflow.service  RecommendationResult com.focusflow.service  SpeechRecognizer com.focusflow.service  SpendingAnalysisData com.focusflow.service  SpendingPattern com.focusflow.service  	StateFlow com.focusflow.service  String com.focusflow.service  
SupervisorJob com.focusflow.service  TimerPreset com.focusflow.service  
TimerState com.focusflow.service  VoiceInputService com.focusflow.service  WorkerParameters com.focusflow.service  _error com.focusflow.service  _isListening com.focusflow.service  _recognizedText com.focusflow.service  asStateFlow com.focusflow.service  
isNullOrEmpty com.focusflow.service  mutableMapOf com.focusflow.service  processVoiceCommand com.focusflow.service  BehaviorInsight .com.focusflow.service.AdvancedAnalyticsService  Boolean .com.focusflow.service.AdvancedAnalyticsService  BudgetCategory .com.focusflow.service.AdvancedAnalyticsService  
CreditCard .com.focusflow.service.AdvancedAnalyticsService  Double .com.focusflow.service.AdvancedAnalyticsService  Expense .com.focusflow.service.AdvancedAnalyticsService  FinancialHealthScore .com.focusflow.service.AdvancedAnalyticsService  FocusFlowDatabase .com.focusflow.service.AdvancedAnalyticsService  Inject .com.focusflow.service.AdvancedAnalyticsService  List .com.focusflow.service.AdvancedAnalyticsService  	LocalDate .com.focusflow.service.AdvancedAnalyticsService  Map .com.focusflow.service.AdvancedAnalyticsService  PerformanceOptimizationService .com.focusflow.service.AdvancedAnalyticsService  SpendingForecast .com.focusflow.service.AdvancedAnalyticsService  String .com.focusflow.service.AdvancedAnalyticsService  Boolean >com.focusflow.service.AdvancedAnalyticsService.BehaviorInsight  Double >com.focusflow.service.AdvancedAnalyticsService.BehaviorInsight  List >com.focusflow.service.AdvancedAnalyticsService.BehaviorInsight  String >com.focusflow.service.AdvancedAnalyticsService.BehaviorInsight  Double Ccom.focusflow.service.AdvancedAnalyticsService.FinancialHealthScore  List Ccom.focusflow.service.AdvancedAnalyticsService.FinancialHealthScore  Map Ccom.focusflow.service.AdvancedAnalyticsService.FinancialHealthScore  String Ccom.focusflow.service.AdvancedAnalyticsService.FinancialHealthScore  Double ?com.focusflow.service.AdvancedAnalyticsService.SpendingForecast  List ?com.focusflow.service.AdvancedAnalyticsService.SpendingForecast  Map ?com.focusflow.service.AdvancedAnalyticsService.SpendingForecast  String ?com.focusflow.service.AdvancedAnalyticsService.SpendingForecast  Context /com.focusflow.service.AnalyticsPrecomputeWorker  Result /com.focusflow.service.AnalyticsPrecomputeWorker  WorkerParameters /com.focusflow.service.AnalyticsPrecomputeWorker  BudgetCategoryRepository 1com.focusflow.service.BudgetRecommendationService  BudgetRecommendationRepository 1com.focusflow.service.BudgetRecommendationService  Double 1com.focusflow.service.BudgetRecommendationService  ExpenseRepository 1com.focusflow.service.BudgetRecommendationService  Inject 1com.focusflow.service.BudgetRecommendationService  List 1com.focusflow.service.BudgetRecommendationService  Month 1com.focusflow.service.BudgetRecommendationService  Pair 1com.focusflow.service.BudgetRecommendationService  RecommendationResult 1com.focusflow.service.BudgetRecommendationService  SpendingAnalysisData 1com.focusflow.service.BudgetRecommendationService  String 1com.focusflow.service.BudgetRecommendationService  Context (com.focusflow.service.CacheCleanupWorker  Result (com.focusflow.service.CacheCleanupWorker  WorkerParameters (com.focusflow.service.CacheCleanupWorker  Int 'com.focusflow.service.DelayPeriodOption  String 'com.focusflow.service.DelayPeriodOption  Double )com.focusflow.service.DelayRecommendation  Int )com.focusflow.service.DelayRecommendation  String )com.focusflow.service.DelayRecommendation  Double %com.focusflow.service.DelayStatistics  Int %com.focusflow.service.DelayStatistics  String %com.focusflow.service.DelayStatistics  AlarmManager 2com.focusflow.service.FocusFlowNotificationManager  ApplicationContext 2com.focusflow.service.FocusFlowNotificationManager  Context 2com.focusflow.service.FocusFlowNotificationManager  Inject 2com.focusflow.service.FocusFlowNotificationManager  Int 2com.focusflow.service.FocusFlowNotificationManager  List 2com.focusflow.service.FocusFlowNotificationManager  Long 2com.focusflow.service.FocusFlowNotificationManager  NotificationManagerCompat 2com.focusflow.service.FocusFlowNotificationManager  String 2com.focusflow.service.FocusFlowNotificationManager  context 2com.focusflow.service.FocusFlowNotificationManager  AlarmManager <com.focusflow.service.FocusFlowNotificationManager.Companion  ApplicationContext <com.focusflow.service.FocusFlowNotificationManager.Companion  Context <com.focusflow.service.FocusFlowNotificationManager.Companion  Inject <com.focusflow.service.FocusFlowNotificationManager.Companion  Int <com.focusflow.service.FocusFlowNotificationManager.Companion  List <com.focusflow.service.FocusFlowNotificationManager.Companion  Long <com.focusflow.service.FocusFlowNotificationManager.Companion  NotificationManagerCompat <com.focusflow.service.FocusFlowNotificationManager.Companion  String <com.focusflow.service.FocusFlowNotificationManager.Companion  Double ,com.focusflow.service.FocusSessionStatistics  Int ,com.focusflow.service.FocusSessionStatistics  Long ,com.focusflow.service.FocusSessionStatistics  Boolean 'com.focusflow.service.FocusTimerService  FocusSession 'com.focusflow.service.FocusTimerService  FocusSessionDao 'com.focusflow.service.FocusTimerService  FocusSessionStatistics 'com.focusflow.service.FocusTimerService  GamificationService 'com.focusflow.service.FocusTimerService  Inject 'com.focusflow.service.FocusTimerService  Int 'com.focusflow.service.FocusTimerService  Job 'com.focusflow.service.FocusTimerService  List 'com.focusflow.service.FocusTimerService  
LocalDateTime 'com.focusflow.service.FocusTimerService  Long 'com.focusflow.service.FocusTimerService  MutableStateFlow 'com.focusflow.service.FocusTimerService  NotificationService 'com.focusflow.service.FocusTimerService  	StateFlow 'com.focusflow.service.FocusTimerService  String 'com.focusflow.service.FocusTimerService  TimerPreset 'com.focusflow.service.FocusTimerService  
TimerState 'com.focusflow.service.FocusTimerService  _currentSession 'com.focusflow.service.FocusTimerService  _isBreakTime 'com.focusflow.service.FocusTimerService  _remainingTime 'com.focusflow.service.FocusTimerService  _timerState 'com.focusflow.service.FocusTimerService  asStateFlow 'com.focusflow.service.FocusTimerService  getASStateFlow 'com.focusflow.service.FocusTimerService  getAsStateFlow 'com.focusflow.service.FocusTimerService  Boolean 1com.focusflow.service.FocusTimerService.Companion  FocusSession 1com.focusflow.service.FocusTimerService.Companion  FocusSessionDao 1com.focusflow.service.FocusTimerService.Companion  FocusSessionStatistics 1com.focusflow.service.FocusTimerService.Companion  GamificationService 1com.focusflow.service.FocusTimerService.Companion  Inject 1com.focusflow.service.FocusTimerService.Companion  Int 1com.focusflow.service.FocusTimerService.Companion  Job 1com.focusflow.service.FocusTimerService.Companion  List 1com.focusflow.service.FocusTimerService.Companion  
LocalDateTime 1com.focusflow.service.FocusTimerService.Companion  Long 1com.focusflow.service.FocusTimerService.Companion  MutableStateFlow 1com.focusflow.service.FocusTimerService.Companion  NotificationService 1com.focusflow.service.FocusTimerService.Companion  	StateFlow 1com.focusflow.service.FocusTimerService.Companion  String 1com.focusflow.service.FocusTimerService.Companion  TimerPreset 1com.focusflow.service.FocusTimerService.Companion  
TimerState 1com.focusflow.service.FocusTimerService.Companion  asStateFlow 1com.focusflow.service.FocusTimerService.Companion  getASStateFlow 1com.focusflow.service.FocusTimerService.Companion  getAsStateFlow 1com.focusflow.service.FocusTimerService.Companion  AchievementDao )com.focusflow.service.GamificationService  Double )com.focusflow.service.GamificationService  Inject )com.focusflow.service.GamificationService  Int )com.focusflow.service.GamificationService  String )com.focusflow.service.GamificationService  UserStatsDao )com.focusflow.service.GamificationService  
VirtualPetDao )com.focusflow.service.GamificationService  ApplicationContext )com.focusflow.service.NotificationService  Context )com.focusflow.service.NotificationService  Double )com.focusflow.service.NotificationService  Inject )com.focusflow.service.NotificationService  Long )com.focusflow.service.NotificationService  String )com.focusflow.service.NotificationService  WishlistItem )com.focusflow.service.NotificationService  ApplicationContext 3com.focusflow.service.NotificationService.Companion  Context 3com.focusflow.service.NotificationService.Companion  Double 3com.focusflow.service.NotificationService.Companion  Inject 3com.focusflow.service.NotificationService.Companion  Long 3com.focusflow.service.NotificationService.Companion  String 3com.focusflow.service.NotificationService.Companion  WishlistItem 3com.focusflow.service.NotificationService.Companion  Any 4com.focusflow.service.PerformanceOptimizationService  ApplicationContext 4com.focusflow.service.PerformanceOptimizationService  Boolean 4com.focusflow.service.PerformanceOptimizationService  BudgetAnalytics 4com.focusflow.service.PerformanceOptimizationService  
CacheEntry 4com.focusflow.service.PerformanceOptimizationService  Context 4com.focusflow.service.PerformanceOptimizationService  CoroutineScope 4com.focusflow.service.PerformanceOptimizationService  Dispatchers 4com.focusflow.service.PerformanceOptimizationService  Double 4com.focusflow.service.PerformanceOptimizationService  Flow 4com.focusflow.service.PerformanceOptimizationService  FocusFlowDatabase 4com.focusflow.service.PerformanceOptimizationService  Inject 4com.focusflow.service.PerformanceOptimizationService  Int 4com.focusflow.service.PerformanceOptimizationService  List 4com.focusflow.service.PerformanceOptimizationService  Long 4com.focusflow.service.PerformanceOptimizationService  SpendingPattern 4com.focusflow.service.PerformanceOptimizationService  String 4com.focusflow.service.PerformanceOptimizationService  
SupervisorJob 4com.focusflow.service.PerformanceOptimizationService  getMUTABLEMapOf 4com.focusflow.service.PerformanceOptimizationService  getMutableMapOf 4com.focusflow.service.PerformanceOptimizationService  mutableMapOf 4com.focusflow.service.PerformanceOptimizationService  Boolean ?com.focusflow.service.PerformanceOptimizationService.CacheEntry  Long ?com.focusflow.service.PerformanceOptimizationService.CacheEntry  Boolean *com.focusflow.service.PurchaseDelayService  DelayPeriodOption *com.focusflow.service.PurchaseDelayService  DelayRecommendation *com.focusflow.service.PurchaseDelayService  DelayStatistics *com.focusflow.service.PurchaseDelayService  Double *com.focusflow.service.PurchaseDelayService  Inject *com.focusflow.service.PurchaseDelayService  Int *com.focusflow.service.PurchaseDelayService  List *com.focusflow.service.PurchaseDelayService  Long *com.focusflow.service.PurchaseDelayService  NotificationService *com.focusflow.service.PurchaseDelayService  String *com.focusflow.service.PurchaseDelayService  WishlistItem *com.focusflow.service.PurchaseDelayService  WishlistRepository *com.focusflow.service.PurchaseDelayService  getDelayPeriodOptions *com.focusflow.service.PurchaseDelayService  Boolean 4com.focusflow.service.PurchaseDelayService.Companion  DelayPeriodOption 4com.focusflow.service.PurchaseDelayService.Companion  DelayRecommendation 4com.focusflow.service.PurchaseDelayService.Companion  DelayStatistics 4com.focusflow.service.PurchaseDelayService.Companion  Double 4com.focusflow.service.PurchaseDelayService.Companion  Inject 4com.focusflow.service.PurchaseDelayService.Companion  Int 4com.focusflow.service.PurchaseDelayService.Companion  List 4com.focusflow.service.PurchaseDelayService.Companion  Long 4com.focusflow.service.PurchaseDelayService.Companion  NotificationService 4com.focusflow.service.PurchaseDelayService.Companion  String 4com.focusflow.service.PurchaseDelayService.Companion  WishlistItem 4com.focusflow.service.PurchaseDelayService.Companion  WishlistRepository 4com.focusflow.service.PurchaseDelayService.Companion  Boolean *com.focusflow.service.RecommendationResult  Double *com.focusflow.service.RecommendationResult  String *com.focusflow.service.RecommendationResult  Double *com.focusflow.service.SpendingAnalysisData  Int *com.focusflow.service.SpendingAnalysisData  Int !com.focusflow.service.TimerPreset  String !com.focusflow.service.TimerPreset  STOPPED  com.focusflow.service.TimerState  Any 'com.focusflow.service.VoiceInputService  ApplicationContext 'com.focusflow.service.VoiceInputService  Boolean 'com.focusflow.service.VoiceInputService  Bundle 'com.focusflow.service.VoiceInputService  	ByteArray 'com.focusflow.service.VoiceInputService  Context 'com.focusflow.service.VoiceInputService  CoroutineScope 'com.focusflow.service.VoiceInputService  Dispatchers 'com.focusflow.service.VoiceInputService  Double 'com.focusflow.service.VoiceInputService  Float 'com.focusflow.service.VoiceInputService  Inject 'com.focusflow.service.VoiceInputService  Int 'com.focusflow.service.VoiceInputService  LiveData 'com.focusflow.service.VoiceInputService  Map 'com.focusflow.service.VoiceInputService  MutableLiveData 'com.focusflow.service.VoiceInputService  RecognitionListener 'com.focusflow.service.VoiceInputService  SpeechRecognizer 'com.focusflow.service.VoiceInputService  String 'com.focusflow.service.VoiceInputService  VoiceCommandDao 'com.focusflow.service.VoiceInputService  VoiceCommandResult 'com.focusflow.service.VoiceInputService  _error 'com.focusflow.service.VoiceInputService  _isListening 'com.focusflow.service.VoiceInputService  _recognizedText 'com.focusflow.service.VoiceInputService  _voiceCommandResult 'com.focusflow.service.VoiceInputService  getISNullOrEmpty 'com.focusflow.service.VoiceInputService  getIsNullOrEmpty 'com.focusflow.service.VoiceInputService  
isNullOrEmpty 'com.focusflow.service.VoiceInputService  processVoiceCommand 'com.focusflow.service.VoiceInputService  Any :com.focusflow.service.VoiceInputService.VoiceCommandResult  Boolean :com.focusflow.service.VoiceInputService.VoiceCommandResult  Double :com.focusflow.service.VoiceInputService.VoiceCommandResult  Map :com.focusflow.service.VoiceInputService.VoiceCommandResult  String :com.focusflow.service.VoiceInputService.VoiceCommandResult  getISNullOrEmpty Ncom.focusflow.service.VoiceInputService.recognitionListener.<no name provided>  getIsNullOrEmpty Ncom.focusflow.service.VoiceInputService.recognitionListener.<no name provided>  getPROCESSVoiceCommand Ncom.focusflow.service.VoiceInputService.recognitionListener.<no name provided>  getProcessVoiceCommand Ncom.focusflow.service.VoiceInputService.recognitionListener.<no name provided>  	get_error Ncom.focusflow.service.VoiceInputService.recognitionListener.<no name provided>  get_isListening Ncom.focusflow.service.VoiceInputService.recognitionListener.<no name provided>  get_recognizedText Ncom.focusflow.service.VoiceInputService.recognitionListener.<no name provided>  
isNullOrEmpty Ncom.focusflow.service.VoiceInputService.recognitionListener.<no name provided>  AccessibilityBadge com.focusflow.ui.components  Boolean com.focusflow.ui.components  BottomNavigationBar com.focusflow.ui.components  
BudgetInsight com.focusflow.ui.components  BudgetPeriodDialog com.focusflow.ui.components  BudgetPeriodSwitcher com.focusflow.ui.components  BudgetRecommendationCard com.focusflow.ui.components  BudgetVarianceAlert com.focusflow.ui.components  BudgetVarianceBar com.focusflow.ui.components  BudgetVarianceChart com.focusflow.ui.components  BudgetVarianceData com.focusflow.ui.components  BudgetWarningCard com.focusflow.ui.components  CategoryLegendItem com.focusflow.ui.components  CategorySpending com.focusflow.ui.components  CategorySpendingPieChart com.focusflow.ui.components  
CircularTimer com.focusflow.ui.components  ClearDataDialog com.focusflow.ui.components  ColumnScope com.focusflow.ui.components  
Composable com.focusflow.ui.components  ConfidenceIndicator com.focusflow.ui.components  CooldownPeriodCard com.focusflow.ui.components  DecisionOption com.focusflow.ui.components  DecisionOptionCard com.focusflow.ui.components  DelayOptionItem com.focusflow.ui.components  DelayPeriodSelector com.focusflow.ui.components  Double com.focusflow.ui.components  ExportDataDialog com.focusflow.ui.components  Float com.focusflow.ui.components  FocusSessionSetup com.focusflow.ui.components  FocusTimerWidget com.focusflow.ui.components  FontSizeSelectionDialog com.focusflow.ui.components  FontSizeSettingsCard com.focusflow.ui.components  FontSizeSlider com.focusflow.ui.components  GoalSelectionDialog com.focusflow.ui.components  ImpulseControlDialog com.focusflow.ui.components  ImpulseControlQuestions com.focusflow.ui.components  InsightItem com.focusflow.ui.components  Int com.focusflow.ui.components  List com.focusflow.ui.components  ListeningIndicator com.focusflow.ui.components  Long com.focusflow.ui.components  MindfulnessBreathingExercise com.focusflow.ui.components  MultipleChoiceDecision com.focusflow.ui.components  NotificationTimeDialog com.focusflow.ui.components  PeriodButton com.focusflow.ui.components  ProgressiveDisclosureCard com.focusflow.ui.components  QuickAction com.focusflow.ui.components  QuickActionButton com.focusflow.ui.components  QuickActionGrid com.focusflow.ui.components  RecognizedTextDisplay com.focusflow.ui.components  RecommendationBadge com.focusflow.ui.components  RecommendedBadge com.focusflow.ui.components  SimpleYesNoDecision com.focusflow.ui.components  SmartBudgetInsights com.focusflow.ui.components  SpendingChartLegend com.focusflow.ui.components  SpendingDataPoint com.focusflow.ui.components  SpendingHeatmap com.focusflow.ui.components  SpendingTrendChart com.focusflow.ui.components  SpendingWatchlistCard com.focusflow.ui.components  String com.focusflow.ui.components  ThemeOption com.focusflow.ui.components  ThemeOptionItem com.focusflow.ui.components  ThemeOptionsList com.focusflow.ui.components  ThemeSelectionDialog com.focusflow.ui.components  ThemeSettingsCard com.focusflow.ui.components  TimerPresetItem com.focusflow.ui.components  TimerPresetSelector com.focusflow.ui.components  Unit com.focusflow.ui.components  VoiceButtonSize com.focusflow.ui.components  VoiceCommandResultDialog com.focusflow.ui.components  VoiceCommandSuggestion com.focusflow.ui.components  VoiceCommandSuggestionItem com.focusflow.ui.components  VoiceCommandSuggestions com.focusflow.ui.components  VoiceInputButton com.focusflow.ui.components  VoiceInputCard com.focusflow.ui.components  VoiceWaveAnimation com.focusflow.ui.components  androidx com.focusflow.ui.components  drawHeatmap com.focusflow.ui.components  drawPieChart com.focusflow.ui.components  drawSpendingChart com.focusflow.ui.components  getCategoryColor com.focusflow.ui.components  getDelayDescription com.focusflow.ui.components  getHeatmapColor com.focusflow.ui.components  Color )com.focusflow.ui.components.BudgetInsight  Int )com.focusflow.ui.components.BudgetInsight  String )com.focusflow.ui.components.BudgetInsight  androidx )com.focusflow.ui.components.BudgetInsight  Double .com.focusflow.ui.components.BudgetVarianceData  String .com.focusflow.ui.components.BudgetVarianceData  Double ,com.focusflow.ui.components.CategorySpending  String ,com.focusflow.ui.components.CategorySpending  Int *com.focusflow.ui.components.DecisionOption  List *com.focusflow.ui.components.DecisionOption  String *com.focusflow.ui.components.DecisionOption  Color 'com.focusflow.ui.components.QuickAction  String 'com.focusflow.ui.components.QuickAction  androidx 'com.focusflow.ui.components.QuickAction  Double -com.focusflow.ui.components.SpendingDataPoint  String -com.focusflow.ui.components.SpendingDataPoint  Boolean 'com.focusflow.ui.components.ThemeOption  ImageVector 'com.focusflow.ui.components.ThemeOption  String 'com.focusflow.ui.components.ThemeOption  	ThemeMode 'com.focusflow.ui.components.ThemeOption  ImageVector 2com.focusflow.ui.components.VoiceCommandSuggestion  String 2com.focusflow.ui.components.VoiceCommandSuggestion  ADHDFriendlyStep com.focusflow.ui.onboarding  Boolean com.focusflow.ui.onboarding  BudgetSetupStep com.focusflow.ui.onboarding  CompleteStep com.focusflow.ui.onboarding  
Composable com.focusflow.ui.onboarding  
DebtSetupStep com.focusflow.ui.onboarding  FinancialGoalsStep com.focusflow.ui.onboarding  IncomeSetupStep com.focusflow.ui.onboarding  Int com.focusflow.ui.onboarding  List com.focusflow.ui.onboarding  NotificationSetupStep com.focusflow.ui.onboarding  OnboardingActivity com.focusflow.ui.onboarding  OnboardingProgressIndicator com.focusflow.ui.onboarding  OnboardingScreen com.focusflow.ui.onboarding  OnboardingStepLayout com.focusflow.ui.onboarding  PersonalGoalsStep com.focusflow.ui.onboarding  String com.focusflow.ui.onboarding  Unit com.focusflow.ui.onboarding  WelcomeStep com.focusflow.ui.onboarding  Bundle .com.focusflow.ui.onboarding.OnboardingActivity  
Composable .com.focusflow.ui.onboarding.OnboardingActivity  
AICoachScreen com.focusflow.ui.screens  AchievementBadge com.focusflow.ui.screens  AddBudgetCategoryDialog com.focusflow.ui.screens  AddCreditCardDialog com.focusflow.ui.screens  AddExpenseDialog com.focusflow.ui.screens  Boolean com.focusflow.ui.screens  BudgetAmountItem com.focusflow.ui.screens  BudgetCategoryItem com.focusflow.ui.screens  BudgetOverviewCard com.focusflow.ui.screens  BudgetScreen com.focusflow.ui.screens  ColumnScope com.focusflow.ui.screens  
Composable com.focusflow.ui.screens  CreditCardItem com.focusflow.ui.screens  CreditCardSummaryCard com.focusflow.ui.screens  DashboardScreen com.focusflow.ui.screens  DebtOverviewCard com.focusflow.ui.screens  
DebtScreen com.focusflow.ui.screens  Double com.focusflow.ui.screens  EmptyBudgetState com.focusflow.ui.screens  EmptyStateCard com.focusflow.ui.screens  EmptyStateMessage com.focusflow.ui.screens  ExpenseItem com.focusflow.ui.screens  ExpensesScreen com.focusflow.ui.screens  
FilterChip com.focusflow.ui.screens  HabitStreakCard com.focusflow.ui.screens  HabitStreakItem com.focusflow.ui.screens  HabitsScreen com.focusflow.ui.screens  Int com.focusflow.ui.screens  List com.focusflow.ui.screens  	LocalDate com.focusflow.ui.screens  
MessageBubble com.focusflow.ui.screens  MessageInputField com.focusflow.ui.screens  MotivationalQuoteCard com.focusflow.ui.screens  Pair com.focusflow.ui.screens  
PaymentDialog com.focusflow.ui.screens  PayoffGoalType com.focusflow.ui.screens  PayoffPlannerDialog com.focusflow.ui.screens  PayoffPlannerScreen com.focusflow.ui.screens  PayoffResultsSection com.focusflow.ui.screens  PayoffStepItem com.focusflow.ui.screens  PayoffSummaryCard com.focusflow.ui.screens  ProgressAchievementsCard com.focusflow.ui.screens  QuickCategoryOverview com.focusflow.ui.screens  QuickSetupDialog com.focusflow.ui.screens  SafeToSpendWidget com.focusflow.ui.screens  SettingsItem com.focusflow.ui.screens  SettingsScreen com.focusflow.ui.screens  SettingsSection com.focusflow.ui.screens  SettingsToggleItem com.focusflow.ui.screens  SpendingSummaryCard com.focusflow.ui.screens  StrategyComparisonCard com.focusflow.ui.screens  StrategySelectionCard com.focusflow.ui.screens  String com.focusflow.ui.screens  SuggestedPromptCard com.focusflow.ui.screens  SuggestedPromptsSection com.focusflow.ui.screens  SummaryItem com.focusflow.ui.screens  TaskItem com.focusflow.ui.screens  TasksScreen com.focusflow.ui.screens  TodaysTasksCard com.focusflow.ui.screens  TypingIndicator com.focusflow.ui.screens  Unit com.focusflow.ui.screens  VirtualPetWidget com.focusflow.ui.screens  com com.focusflow.ui.screens  
formatDate com.focusflow.ui.screens  Boolean com.focusflow.ui.theme  DarkColorPalette com.focusflow.ui.theme  FocusFlowTheme com.focusflow.ui.theme  HighContrastDarkPalette com.focusflow.ui.theme  HighContrastLightPalette com.focusflow.ui.theme  LightColorPalette com.focusflow.ui.theme  	Purple200 com.focusflow.ui.theme  	Purple500 com.focusflow.ui.theme  	Purple700 com.focusflow.ui.theme  Shapes com.focusflow.ui.theme  Teal200 com.focusflow.ui.theme  	ThemeMode com.focusflow.ui.theme  
Typography com.focusflow.ui.theme  Unit com.focusflow.ui.theme  AICoachUiState com.focusflow.ui.viewmodel  AICoachViewModel com.focusflow.ui.viewmodel  Boolean com.focusflow.ui.viewmodel  
BudgetUiState com.focusflow.ui.viewmodel  BudgetViewModel com.focusflow.ui.viewmodel  ChatMessage com.focusflow.ui.viewmodel  DashboardUiState com.focusflow.ui.viewmodel  DashboardViewModel com.focusflow.ui.viewmodel  DebtUiState com.focusflow.ui.viewmodel  
DebtViewModel com.focusflow.ui.viewmodel  DefaultBudgetCategories com.focusflow.ui.viewmodel  Double com.focusflow.ui.viewmodel  EnhancedBudgetUiState com.focusflow.ui.viewmodel  EnhancedBudgetViewModel com.focusflow.ui.viewmodel  ExpenseCategories com.focusflow.ui.viewmodel  ExpenseUiState com.focusflow.ui.viewmodel  ExpenseViewModel com.focusflow.ui.viewmodel  Flow com.focusflow.ui.viewmodel  ImpulseControlUiState com.focusflow.ui.viewmodel  ImpulseControlViewModel com.focusflow.ui.viewmodel  Int com.focusflow.ui.viewmodel  List com.focusflow.ui.viewmodel  	LocalDate com.focusflow.ui.viewmodel  
LocalDateTime com.focusflow.ui.viewmodel  Long com.focusflow.ui.viewmodel  MainUiState com.focusflow.ui.viewmodel  
MainViewModel com.focusflow.ui.viewmodel  MutableStateFlow com.focusflow.ui.viewmodel  OnboardingStep com.focusflow.ui.viewmodel  OnboardingUiState com.focusflow.ui.viewmodel  OnboardingViewModel com.focusflow.ui.viewmodel  Pair com.focusflow.ui.viewmodel  PayoffCalculation com.focusflow.ui.viewmodel  
PayoffPlan com.focusflow.ui.viewmodel  
PayoffStep com.focusflow.ui.viewmodel  PayoffStrategy com.focusflow.ui.viewmodel  SettingsUiState com.focusflow.ui.viewmodel  SettingsViewModel com.focusflow.ui.viewmodel  SharingStarted com.focusflow.ui.viewmodel  	StateFlow com.focusflow.ui.viewmodel  String com.focusflow.ui.viewmodel  Triple com.focusflow.ui.viewmodel  asStateFlow com.focusflow.ui.viewmodel  com com.focusflow.ui.viewmodel  	emptyList com.focusflow.ui.viewmodel  listOf com.focusflow.ui.viewmodel  stateIn com.focusflow.ui.viewmodel  to com.focusflow.ui.viewmodel  viewModelScope com.focusflow.ui.viewmodel  Boolean )com.focusflow.ui.viewmodel.AICoachUiState  ChatMessage )com.focusflow.ui.viewmodel.AICoachUiState  List )com.focusflow.ui.viewmodel.AICoachUiState  String )com.focusflow.ui.viewmodel.AICoachUiState  AICoachUiState +com.focusflow.ui.viewmodel.AICoachViewModel  CreditCardRepository +com.focusflow.ui.viewmodel.AICoachViewModel  ExpenseRepository +com.focusflow.ui.viewmodel.AICoachViewModel  Inject +com.focusflow.ui.viewmodel.AICoachViewModel  MutableStateFlow +com.focusflow.ui.viewmodel.AICoachViewModel  	StateFlow +com.focusflow.ui.viewmodel.AICoachViewModel  String +com.focusflow.ui.viewmodel.AICoachViewModel  UserPreferencesRepository +com.focusflow.ui.viewmodel.AICoachViewModel  _uiState +com.focusflow.ui.viewmodel.AICoachViewModel  asStateFlow +com.focusflow.ui.viewmodel.AICoachViewModel  getASStateFlow +com.focusflow.ui.viewmodel.AICoachViewModel  getAsStateFlow +com.focusflow.ui.viewmodel.AICoachViewModel  Boolean (com.focusflow.ui.viewmodel.BudgetUiState  BudgetCategory (com.focusflow.ui.viewmodel.BudgetUiState  Double (com.focusflow.ui.viewmodel.BudgetUiState  List (com.focusflow.ui.viewmodel.BudgetUiState  String (com.focusflow.ui.viewmodel.BudgetUiState  BudgetCategory *com.focusflow.ui.viewmodel.BudgetViewModel  BudgetCategoryRepository *com.focusflow.ui.viewmodel.BudgetViewModel  
BudgetUiState *com.focusflow.ui.viewmodel.BudgetViewModel  Double *com.focusflow.ui.viewmodel.BudgetViewModel  ExpenseRepository *com.focusflow.ui.viewmodel.BudgetViewModel  Inject *com.focusflow.ui.viewmodel.BudgetViewModel  Int *com.focusflow.ui.viewmodel.BudgetViewModel  
LocalDateTime *com.focusflow.ui.viewmodel.BudgetViewModel  MutableStateFlow *com.focusflow.ui.viewmodel.BudgetViewModel  	StateFlow *com.focusflow.ui.viewmodel.BudgetViewModel  String *com.focusflow.ui.viewmodel.BudgetViewModel  Triple *com.focusflow.ui.viewmodel.BudgetViewModel  UserPreferencesRepository *com.focusflow.ui.viewmodel.BudgetViewModel  _uiState *com.focusflow.ui.viewmodel.BudgetViewModel  asStateFlow *com.focusflow.ui.viewmodel.BudgetViewModel  getASStateFlow *com.focusflow.ui.viewmodel.BudgetViewModel  getAsStateFlow *com.focusflow.ui.viewmodel.BudgetViewModel  Boolean &com.focusflow.ui.viewmodel.ChatMessage  Long &com.focusflow.ui.viewmodel.ChatMessage  String &com.focusflow.ui.viewmodel.ChatMessage  Boolean +com.focusflow.ui.viewmodel.DashboardUiState  Double +com.focusflow.ui.viewmodel.DashboardUiState  String +com.focusflow.ui.viewmodel.DashboardUiState  CreditCardRepository -com.focusflow.ui.viewmodel.DashboardViewModel  DashboardUiState -com.focusflow.ui.viewmodel.DashboardViewModel  ExpenseRepository -com.focusflow.ui.viewmodel.DashboardViewModel  Inject -com.focusflow.ui.viewmodel.DashboardViewModel  
LocalDateTime -com.focusflow.ui.viewmodel.DashboardViewModel  MutableStateFlow -com.focusflow.ui.viewmodel.DashboardViewModel  Pair -com.focusflow.ui.viewmodel.DashboardViewModel  	StateFlow -com.focusflow.ui.viewmodel.DashboardViewModel  String -com.focusflow.ui.viewmodel.DashboardViewModel  UserPreferencesRepository -com.focusflow.ui.viewmodel.DashboardViewModel  _uiState -com.focusflow.ui.viewmodel.DashboardViewModel  asStateFlow -com.focusflow.ui.viewmodel.DashboardViewModel  getASStateFlow -com.focusflow.ui.viewmodel.DashboardViewModel  getAsStateFlow -com.focusflow.ui.viewmodel.DashboardViewModel  Boolean &com.focusflow.ui.viewmodel.DebtUiState  Double &com.focusflow.ui.viewmodel.DebtUiState  String &com.focusflow.ui.viewmodel.DebtUiState  
CreditCard (com.focusflow.ui.viewmodel.DebtViewModel  CreditCardRepository (com.focusflow.ui.viewmodel.DebtViewModel  DebtUiState (com.focusflow.ui.viewmodel.DebtViewModel  Double (com.focusflow.ui.viewmodel.DebtViewModel  Inject (com.focusflow.ui.viewmodel.DebtViewModel  List (com.focusflow.ui.viewmodel.DebtViewModel  	LocalDate (com.focusflow.ui.viewmodel.DebtViewModel  Long (com.focusflow.ui.viewmodel.DebtViewModel  MutableStateFlow (com.focusflow.ui.viewmodel.DebtViewModel  PayoffCalculation (com.focusflow.ui.viewmodel.DebtViewModel  
PayoffPlan (com.focusflow.ui.viewmodel.DebtViewModel  PayoffStrategy (com.focusflow.ui.viewmodel.DebtViewModel  	StateFlow (com.focusflow.ui.viewmodel.DebtViewModel  String (com.focusflow.ui.viewmodel.DebtViewModel  _payoffPlan (com.focusflow.ui.viewmodel.DebtViewModel  _uiState (com.focusflow.ui.viewmodel.DebtViewModel  asStateFlow (com.focusflow.ui.viewmodel.DebtViewModel  creditCardRepository (com.focusflow.ui.viewmodel.DebtViewModel  	emptyList (com.focusflow.ui.viewmodel.DebtViewModel  getASStateFlow (com.focusflow.ui.viewmodel.DebtViewModel  getAsStateFlow (com.focusflow.ui.viewmodel.DebtViewModel  getEMPTYList (com.focusflow.ui.viewmodel.DebtViewModel  getEmptyList (com.focusflow.ui.viewmodel.DebtViewModel  	getLISTOf 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  	getListOf 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  getTO 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  getTo 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  listOf 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  to 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  Boolean 0com.focusflow.ui.viewmodel.EnhancedBudgetUiState  BudgetCategory 0com.focusflow.ui.viewmodel.EnhancedBudgetUiState  
BudgetInsight 0com.focusflow.ui.viewmodel.EnhancedBudgetUiState  BudgetRecommendation 0com.focusflow.ui.viewmodel.EnhancedBudgetUiState  Double 0com.focusflow.ui.viewmodel.EnhancedBudgetUiState  List 0com.focusflow.ui.viewmodel.EnhancedBudgetUiState  String 0com.focusflow.ui.viewmodel.EnhancedBudgetUiState  BudgetCategory 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  BudgetCategoryRepository 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  BudgetRecommendationRepository 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  BudgetRecommendationService 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  Double 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  EnhancedBudgetUiState 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  ExpenseRepository 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  Inject 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  Int 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  
LocalDateTime 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  Long 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  MutableStateFlow 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  	StateFlow 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  String 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  Triple 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  UserPreferencesRepository 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  _uiState 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  asStateFlow 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  getASStateFlow 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  getAsStateFlow 2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel  	getLISTOf ,com.focusflow.ui.viewmodel.ExpenseCategories  	getListOf ,com.focusflow.ui.viewmodel.ExpenseCategories  listOf ,com.focusflow.ui.viewmodel.ExpenseCategories  Boolean )com.focusflow.ui.viewmodel.ExpenseUiState  Double )com.focusflow.ui.viewmodel.ExpenseUiState  Expense )com.focusflow.ui.viewmodel.ExpenseUiState  List )com.focusflow.ui.viewmodel.ExpenseUiState  String )com.focusflow.ui.viewmodel.ExpenseUiState  Double +com.focusflow.ui.viewmodel.ExpenseViewModel  Expense +com.focusflow.ui.viewmodel.ExpenseViewModel  ExpenseRepository +com.focusflow.ui.viewmodel.ExpenseViewModel  ExpenseUiState +com.focusflow.ui.viewmodel.ExpenseViewModel  Flow +com.focusflow.ui.viewmodel.ExpenseViewModel  Inject +com.focusflow.ui.viewmodel.ExpenseViewModel  List +com.focusflow.ui.viewmodel.ExpenseViewModel  
LocalDateTime +com.focusflow.ui.viewmodel.ExpenseViewModel  MutableStateFlow +com.focusflow.ui.viewmodel.ExpenseViewModel  Pair +com.focusflow.ui.viewmodel.ExpenseViewModel  	StateFlow +com.focusflow.ui.viewmodel.ExpenseViewModel  String +com.focusflow.ui.viewmodel.ExpenseViewModel  UserPreferencesRepository +com.focusflow.ui.viewmodel.ExpenseViewModel  _uiState +com.focusflow.ui.viewmodel.ExpenseViewModel  asStateFlow +com.focusflow.ui.viewmodel.ExpenseViewModel  com +com.focusflow.ui.viewmodel.ExpenseViewModel  expenseRepository +com.focusflow.ui.viewmodel.ExpenseViewModel  getASStateFlow +com.focusflow.ui.viewmodel.ExpenseViewModel  getAsStateFlow +com.focusflow.ui.viewmodel.ExpenseViewModel  Boolean 0com.focusflow.ui.viewmodel.ImpulseControlUiState  Int 0com.focusflow.ui.viewmodel.ImpulseControlUiState  Long 0com.focusflow.ui.viewmodel.ImpulseControlUiState  String 0com.focusflow.ui.viewmodel.ImpulseControlUiState  com 0com.focusflow.ui.viewmodel.ImpulseControlUiState  Boolean 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  Double 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  ImpulseControlUiState 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  Inject 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  Int 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  Long 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  MutableStateFlow 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  PurchaseDelayService 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  SharingStarted 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  	StateFlow 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  String 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  WishlistItem 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  WishlistRepository 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  _uiState 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  asStateFlow 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  	emptyList 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  getASStateFlow 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  getAsStateFlow 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  getEMPTYList 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  getEmptyList 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  
getSTATEIn 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  
getStateIn 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  getVIEWModelScope 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  getViewModelScope 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  purchaseDelayService 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  stateIn 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  viewModelScope 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  wishlistRepository 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  Boolean &com.focusflow.ui.viewmodel.MainUiState  String &com.focusflow.ui.viewmodel.MainUiState  	ThemeMode &com.focusflow.ui.viewmodel.MainUiState  Inject (com.focusflow.ui.viewmodel.MainViewModel  MainUiState (com.focusflow.ui.viewmodel.MainViewModel  MutableStateFlow (com.focusflow.ui.viewmodel.MainViewModel  NotificationRepository (com.focusflow.ui.viewmodel.MainViewModel  	StateFlow (com.focusflow.ui.viewmodel.MainViewModel  String (com.focusflow.ui.viewmodel.MainViewModel  	ThemeMode (com.focusflow.ui.viewmodel.MainViewModel  UserPreferencesRepository (com.focusflow.ui.viewmodel.MainViewModel  _uiState (com.focusflow.ui.viewmodel.MainViewModel  asStateFlow (com.focusflow.ui.viewmodel.MainViewModel  com (com.focusflow.ui.viewmodel.MainViewModel  getASStateFlow (com.focusflow.ui.viewmodel.MainViewModel  getAsStateFlow (com.focusflow.ui.viewmodel.MainViewModel  Boolean ,com.focusflow.ui.viewmodel.OnboardingUiState  List ,com.focusflow.ui.viewmodel.OnboardingUiState  OnboardingStep ,com.focusflow.ui.viewmodel.OnboardingUiState  String ,com.focusflow.ui.viewmodel.OnboardingUiState  Boolean .com.focusflow.ui.viewmodel.OnboardingViewModel  Double .com.focusflow.ui.viewmodel.OnboardingViewModel  Inject .com.focusflow.ui.viewmodel.OnboardingViewModel  List .com.focusflow.ui.viewmodel.OnboardingViewModel  MutableStateFlow .com.focusflow.ui.viewmodel.OnboardingViewModel  OnboardingUiState .com.focusflow.ui.viewmodel.OnboardingViewModel  	StateFlow .com.focusflow.ui.viewmodel.OnboardingViewModel  String .com.focusflow.ui.viewmodel.OnboardingViewModel  UserPreferencesRepository .com.focusflow.ui.viewmodel.OnboardingViewModel  _uiState .com.focusflow.ui.viewmodel.OnboardingViewModel  asStateFlow .com.focusflow.ui.viewmodel.OnboardingViewModel  getASStateFlow .com.focusflow.ui.viewmodel.OnboardingViewModel  getAsStateFlow .com.focusflow.ui.viewmodel.OnboardingViewModel  Double ,com.focusflow.ui.viewmodel.PayoffCalculation  Int ,com.focusflow.ui.viewmodel.PayoffCalculation  Double %com.focusflow.ui.viewmodel.PayoffPlan  Int %com.focusflow.ui.viewmodel.PayoffPlan  List %com.focusflow.ui.viewmodel.PayoffPlan  
PayoffStep %com.focusflow.ui.viewmodel.PayoffPlan  Boolean %com.focusflow.ui.viewmodel.PayoffStep  Double %com.focusflow.ui.viewmodel.PayoffStep  Int %com.focusflow.ui.viewmodel.PayoffStep  String %com.focusflow.ui.viewmodel.PayoffStep  Boolean *com.focusflow.ui.viewmodel.SettingsUiState  String *com.focusflow.ui.viewmodel.SettingsUiState  UserPreferences *com.focusflow.ui.viewmodel.SettingsUiState  Boolean ,com.focusflow.ui.viewmodel.SettingsViewModel  Inject ,com.focusflow.ui.viewmodel.SettingsViewModel  MutableStateFlow ,com.focusflow.ui.viewmodel.SettingsViewModel  NotificationRepository ,com.focusflow.ui.viewmodel.SettingsViewModel  SettingsUiState ,com.focusflow.ui.viewmodel.SettingsViewModel  	StateFlow ,com.focusflow.ui.viewmodel.SettingsViewModel  String ,com.focusflow.ui.viewmodel.SettingsViewModel  UserPreferencesRepository ,com.focusflow.ui.viewmodel.SettingsViewModel  _uiState ,com.focusflow.ui.viewmodel.SettingsViewModel  asStateFlow ,com.focusflow.ui.viewmodel.SettingsViewModel  getASStateFlow ,com.focusflow.ui.viewmodel.SettingsViewModel  getAsStateFlow ,com.focusflow.ui.viewmodel.SettingsViewModel  AccessibilityUtils com.focusflow.utils  Boolean com.focusflow.utils  DataIntegrityUtils com.focusflow.utils  Double com.focusflow.utils  ErrorHandler com.focusflow.utils  
ErrorSnackbar com.focusflow.utils  Int com.focusflow.utils  List com.focusflow.utils  Long com.focusflow.utils  PerformanceMonitor com.focusflow.utils  
SecurityUtils com.focusflow.utils  String com.focusflow.utils  	Throwable com.focusflow.utils  Unit com.focusflow.utils  ValidationUtils com.focusflow.utils  androidx com.focusflow.utils  com com.focusflow.utils  mutableMapOf com.focusflow.utils  Boolean &com.focusflow.utils.AccessibilityUtils  Double &com.focusflow.utils.AccessibilityUtils  Int &com.focusflow.utils.AccessibilityUtils  List &com.focusflow.utils.AccessibilityUtils  Long &com.focusflow.utils.AccessibilityUtils  String &com.focusflow.utils.AccessibilityUtils  androidx &com.focusflow.utils.AccessibilityUtils  List &com.focusflow.utils.DataIntegrityUtils  String &com.focusflow.utils.DataIntegrityUtils  com &com.focusflow.utils.DataIntegrityUtils  SnackbarDuration  com.focusflow.utils.ErrorHandler  SnackbarHostState  com.focusflow.utils.ErrorHandler  String  com.focusflow.utils.ErrorHandler  	Throwable  com.focusflow.utils.ErrorHandler  Long &com.focusflow.utils.PerformanceMonitor  String &com.focusflow.utils.PerformanceMonitor  getMUTABLEMapOf &com.focusflow.utils.PerformanceMonitor  getMutableMapOf &com.focusflow.utils.PerformanceMonitor  mutableMapOf &com.focusflow.utils.PerformanceMonitor  Boolean !com.focusflow.utils.SecurityUtils  String !com.focusflow.utils.SecurityUtils  Boolean #com.focusflow.utils.ValidationUtils  Double #com.focusflow.utils.ValidationUtils  String #com.focusflow.utils.ValidationUtils  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  AICoachUiState 	java.lang  
AIInteraction 	java.lang  AccountabilityContact 	java.lang  Achievement 	java.lang  AlternativeProduct 	java.lang  BudgetAnalytics 	java.lang  BudgetCategory 	java.lang  BudgetRecommendation 	java.lang  
BudgetUiState 	java.lang  Context 	java.lang  
Converters 	java.lang  CoroutineScope 	java.lang  
CreditCard 	java.lang  DashboardUiState 	java.lang  DashboardWidget 	java.lang  DebtUiState 	java.lang  Dispatchers 	java.lang  EnhancedBudgetUiState 	java.lang  Expense 	java.lang  ExpenseUiState 	java.lang  FocusSession 	java.lang  HabitLog 	java.lang  ImpulseControlUiState 	java.lang  MainUiState 	java.lang  MutableLiveData 	java.lang  MutableStateFlow 	java.lang  NotificationManagerCompat 	java.lang  OnConflictStrategy 	java.lang  OnboardingUiState 	java.lang  
PayoffPlan 	java.lang  Screen 	java.lang  SettingsUiState 	java.lang  SharingStarted 	java.lang  SingletonComponent 	java.lang  SpeechRecognizer 	java.lang  SpendingPattern 	java.lang  SpendingReflection 	java.lang  StringListConverter 	java.lang  
SupervisorJob 	java.lang  Task 	java.lang  
TimerState 	java.lang  
TrendingUp 	java.lang  UserPreferences 	java.lang  	UserStats 	java.lang  
VirtualPet 	java.lang  VoiceCommand 	java.lang  WishlistItem 	java.lang  _error 	java.lang  _isListening 	java.lang  _recognizedText 	java.lang  androidx 	java.lang  asStateFlow 	java.lang  com 	java.lang  	emptyList 	java.lang  
isNullOrEmpty 	java.lang  kotlinx 	java.lang  listOf 	java.lang  mutableMapOf 	java.lang  processVoiceCommand 	java.lang  stateIn 	java.lang  to 	java.lang  SimpleDateFormat 	java.text  	ArrayList 	java.util  
Composable 	java.util  Context 	java.util  CoroutineScope 	java.util  Dispatchers 	java.util  MutableLiveData 	java.util  NotificationManagerCompat 	java.util  SpeechRecognizer 	java.util  _error 	java.util  _isListening 	java.util  _recognizedText 	java.util  
isNullOrEmpty 	java.util  processVoiceCommand 	java.util  get java.util.AbstractCollection  
isNullOrEmpty java.util.AbstractCollection  get java.util.AbstractList  
isNullOrEmpty java.util.AbstractList  get java.util.ArrayList  getISNullOrEmpty java.util.ArrayList  getIsNullOrEmpty java.util.ArrayList  
isNullOrEmpty java.util.ArrayList  TimeUnit java.util.concurrent  Inject javax.inject  	Singleton javax.inject  AICoachUiState kotlin  
AIInteraction kotlin  AccountabilityContact kotlin  Achievement kotlin  AlternativeProduct kotlin  Any kotlin  Array kotlin  Boolean kotlin  BudgetAnalytics kotlin  BudgetCategory kotlin  BudgetRecommendation kotlin  
BudgetUiState kotlin  	ByteArray kotlin  Context kotlin  
Converters kotlin  CoroutineScope kotlin  
CreditCard kotlin  DashboardUiState kotlin  DashboardWidget kotlin  DebtUiState kotlin  Dispatchers kotlin  Double kotlin  EnhancedBudgetUiState kotlin  Expense kotlin  ExpenseUiState kotlin  Float kotlin  
FloatArray kotlin  FocusSession kotlin  HabitLog kotlin  ImpulseControlUiState kotlin  Int kotlin  Long kotlin  MainUiState kotlin  MutableLiveData kotlin  MutableStateFlow kotlin  Nothing kotlin  NotificationManagerCompat kotlin  OnConflictStrategy kotlin  OnboardingUiState kotlin  Pair kotlin  
PayoffPlan kotlin  Screen kotlin  SettingsUiState kotlin  SharingStarted kotlin  SingletonComponent kotlin  SpeechRecognizer kotlin  SpendingPattern kotlin  SpendingReflection kotlin  String kotlin  StringListConverter kotlin  
SupervisorJob kotlin  Task kotlin  	Throwable kotlin  
TimerState kotlin  
TrendingUp kotlin  Triple kotlin  Unit kotlin  UserPreferences kotlin  	UserStats kotlin  
VirtualPet kotlin  VoiceCommand kotlin  Volatile kotlin  WishlistItem kotlin  _error kotlin  _isListening kotlin  _recognizedText kotlin  androidx kotlin  arrayOf kotlin  asStateFlow kotlin  com kotlin  	emptyList kotlin  
isNullOrEmpty kotlin  kotlinx kotlin  listOf kotlin  mutableMapOf kotlin  processVoiceCommand kotlin  stateIn kotlin  to kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getDP 
kotlin.Int  getDp 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  getTO 
kotlin.String  getTo 
kotlin.String  AICoachUiState kotlin.annotation  
AIInteraction kotlin.annotation  AccountabilityContact kotlin.annotation  Achievement kotlin.annotation  AlternativeProduct kotlin.annotation  BudgetAnalytics kotlin.annotation  BudgetCategory kotlin.annotation  BudgetRecommendation kotlin.annotation  
BudgetUiState kotlin.annotation  Context kotlin.annotation  
Converters kotlin.annotation  CoroutineScope kotlin.annotation  
CreditCard kotlin.annotation  DashboardUiState kotlin.annotation  DashboardWidget kotlin.annotation  DebtUiState kotlin.annotation  Dispatchers kotlin.annotation  EnhancedBudgetUiState kotlin.annotation  Expense kotlin.annotation  ExpenseUiState kotlin.annotation  FocusSession kotlin.annotation  HabitLog kotlin.annotation  ImpulseControlUiState kotlin.annotation  MainUiState kotlin.annotation  MutableLiveData kotlin.annotation  MutableStateFlow kotlin.annotation  NotificationManagerCompat kotlin.annotation  OnConflictStrategy kotlin.annotation  OnboardingUiState kotlin.annotation  Pair kotlin.annotation  
PayoffPlan kotlin.annotation  Screen kotlin.annotation  SettingsUiState kotlin.annotation  SharingStarted kotlin.annotation  SingletonComponent kotlin.annotation  SpeechRecognizer kotlin.annotation  SpendingPattern kotlin.annotation  SpendingReflection kotlin.annotation  StringListConverter kotlin.annotation  
SupervisorJob kotlin.annotation  Task kotlin.annotation  
TimerState kotlin.annotation  
TrendingUp kotlin.annotation  Triple kotlin.annotation  UserPreferences kotlin.annotation  	UserStats kotlin.annotation  
VirtualPet kotlin.annotation  VoiceCommand kotlin.annotation  Volatile kotlin.annotation  WishlistItem kotlin.annotation  _error kotlin.annotation  _isListening kotlin.annotation  _recognizedText kotlin.annotation  androidx kotlin.annotation  asStateFlow kotlin.annotation  com kotlin.annotation  	emptyList kotlin.annotation  
isNullOrEmpty kotlin.annotation  kotlinx kotlin.annotation  listOf kotlin.annotation  mutableMapOf kotlin.annotation  processVoiceCommand kotlin.annotation  stateIn kotlin.annotation  to kotlin.annotation  AICoachUiState kotlin.collections  
AIInteraction kotlin.collections  AccountabilityContact kotlin.collections  Achievement kotlin.collections  AlternativeProduct kotlin.collections  BudgetAnalytics kotlin.collections  BudgetCategory kotlin.collections  BudgetRecommendation kotlin.collections  
BudgetUiState kotlin.collections  Context kotlin.collections  
Converters kotlin.collections  CoroutineScope kotlin.collections  
CreditCard kotlin.collections  DashboardUiState kotlin.collections  DashboardWidget kotlin.collections  DebtUiState kotlin.collections  Dispatchers kotlin.collections  EnhancedBudgetUiState kotlin.collections  Expense kotlin.collections  ExpenseUiState kotlin.collections  FocusSession kotlin.collections  HabitLog kotlin.collections  ImpulseControlUiState kotlin.collections  List kotlin.collections  MainUiState kotlin.collections  Map kotlin.collections  MutableLiveData kotlin.collections  
MutableMap kotlin.collections  MutableStateFlow kotlin.collections  NotificationManagerCompat kotlin.collections  OnConflictStrategy kotlin.collections  OnboardingUiState kotlin.collections  Pair kotlin.collections  
PayoffPlan kotlin.collections  Screen kotlin.collections  SettingsUiState kotlin.collections  SharingStarted kotlin.collections  SingletonComponent kotlin.collections  SpeechRecognizer kotlin.collections  SpendingPattern kotlin.collections  SpendingReflection kotlin.collections  StringListConverter kotlin.collections  
SupervisorJob kotlin.collections  Task kotlin.collections  
TimerState kotlin.collections  
TrendingUp kotlin.collections  Triple kotlin.collections  UserPreferences kotlin.collections  	UserStats kotlin.collections  
VirtualPet kotlin.collections  VoiceCommand kotlin.collections  Volatile kotlin.collections  WishlistItem kotlin.collections  _error kotlin.collections  _isListening kotlin.collections  _recognizedText kotlin.collections  androidx kotlin.collections  asStateFlow kotlin.collections  com kotlin.collections  	emptyList kotlin.collections  
isNullOrEmpty kotlin.collections  kotlinx kotlin.collections  listOf kotlin.collections  mutableMapOf kotlin.collections  processVoiceCommand kotlin.collections  stateIn kotlin.collections  to kotlin.collections  AICoachUiState kotlin.comparisons  
AIInteraction kotlin.comparisons  AccountabilityContact kotlin.comparisons  Achievement kotlin.comparisons  AlternativeProduct kotlin.comparisons  BudgetAnalytics kotlin.comparisons  BudgetCategory kotlin.comparisons  BudgetRecommendation kotlin.comparisons  
BudgetUiState kotlin.comparisons  Context kotlin.comparisons  
Converters kotlin.comparisons  CoroutineScope kotlin.comparisons  
CreditCard kotlin.comparisons  DashboardUiState kotlin.comparisons  DashboardWidget kotlin.comparisons  DebtUiState kotlin.comparisons  Dispatchers kotlin.comparisons  EnhancedBudgetUiState kotlin.comparisons  Expense kotlin.comparisons  ExpenseUiState kotlin.comparisons  FocusSession kotlin.comparisons  HabitLog kotlin.comparisons  ImpulseControlUiState kotlin.comparisons  MainUiState kotlin.comparisons  MutableLiveData kotlin.comparisons  MutableStateFlow kotlin.comparisons  NotificationManagerCompat kotlin.comparisons  OnConflictStrategy kotlin.comparisons  OnboardingUiState kotlin.comparisons  Pair kotlin.comparisons  
PayoffPlan kotlin.comparisons  Screen kotlin.comparisons  SettingsUiState kotlin.comparisons  SharingStarted kotlin.comparisons  SingletonComponent kotlin.comparisons  SpeechRecognizer kotlin.comparisons  SpendingPattern kotlin.comparisons  SpendingReflection kotlin.comparisons  StringListConverter kotlin.comparisons  
SupervisorJob kotlin.comparisons  Task kotlin.comparisons  
TimerState kotlin.comparisons  
TrendingUp kotlin.comparisons  Triple kotlin.comparisons  UserPreferences kotlin.comparisons  	UserStats kotlin.comparisons  
VirtualPet kotlin.comparisons  VoiceCommand kotlin.comparisons  Volatile kotlin.comparisons  WishlistItem kotlin.comparisons  _error kotlin.comparisons  _isListening kotlin.comparisons  _recognizedText kotlin.comparisons  androidx kotlin.comparisons  asStateFlow kotlin.comparisons  com kotlin.comparisons  	emptyList kotlin.comparisons  
isNullOrEmpty kotlin.comparisons  kotlinx kotlin.comparisons  listOf kotlin.comparisons  mutableMapOf kotlin.comparisons  processVoiceCommand kotlin.comparisons  stateIn kotlin.comparisons  to kotlin.comparisons  CoroutineContext kotlin.coroutines  plus 1kotlin.coroutines.AbstractCoroutineContextElement  AICoachUiState 	kotlin.io  
AIInteraction 	kotlin.io  AccountabilityContact 	kotlin.io  Achievement 	kotlin.io  AlternativeProduct 	kotlin.io  BudgetAnalytics 	kotlin.io  BudgetCategory 	kotlin.io  BudgetRecommendation 	kotlin.io  
BudgetUiState 	kotlin.io  Context 	kotlin.io  
Converters 	kotlin.io  CoroutineScope 	kotlin.io  
CreditCard 	kotlin.io  DashboardUiState 	kotlin.io  DashboardWidget 	kotlin.io  DebtUiState 	kotlin.io  Dispatchers 	kotlin.io  EnhancedBudgetUiState 	kotlin.io  Expense 	kotlin.io  ExpenseUiState 	kotlin.io  FocusSession 	kotlin.io  HabitLog 	kotlin.io  ImpulseControlUiState 	kotlin.io  MainUiState 	kotlin.io  MutableLiveData 	kotlin.io  MutableStateFlow 	kotlin.io  NotificationManagerCompat 	kotlin.io  OnConflictStrategy 	kotlin.io  OnboardingUiState 	kotlin.io  Pair 	kotlin.io  
PayoffPlan 	kotlin.io  Screen 	kotlin.io  SettingsUiState 	kotlin.io  SharingStarted 	kotlin.io  SingletonComponent 	kotlin.io  SpeechRecognizer 	kotlin.io  SpendingPattern 	kotlin.io  SpendingReflection 	kotlin.io  StringListConverter 	kotlin.io  
SupervisorJob 	kotlin.io  Task 	kotlin.io  
TimerState 	kotlin.io  
TrendingUp 	kotlin.io  Triple 	kotlin.io  UserPreferences 	kotlin.io  	UserStats 	kotlin.io  
VirtualPet 	kotlin.io  VoiceCommand 	kotlin.io  Volatile 	kotlin.io  WishlistItem 	kotlin.io  _error 	kotlin.io  _isListening 	kotlin.io  _recognizedText 	kotlin.io  androidx 	kotlin.io  asStateFlow 	kotlin.io  com 	kotlin.io  	emptyList 	kotlin.io  
isNullOrEmpty 	kotlin.io  kotlinx 	kotlin.io  listOf 	kotlin.io  mutableMapOf 	kotlin.io  processVoiceCommand 	kotlin.io  stateIn 	kotlin.io  to 	kotlin.io  AICoachUiState 
kotlin.jvm  
AIInteraction 
kotlin.jvm  AccountabilityContact 
kotlin.jvm  Achievement 
kotlin.jvm  AlternativeProduct 
kotlin.jvm  BudgetAnalytics 
kotlin.jvm  BudgetCategory 
kotlin.jvm  BudgetRecommendation 
kotlin.jvm  
BudgetUiState 
kotlin.jvm  Context 
kotlin.jvm  
Converters 
kotlin.jvm  CoroutineScope 
kotlin.jvm  
CreditCard 
kotlin.jvm  DashboardUiState 
kotlin.jvm  DashboardWidget 
kotlin.jvm  DebtUiState 
kotlin.jvm  Dispatchers 
kotlin.jvm  EnhancedBudgetUiState 
kotlin.jvm  Expense 
kotlin.jvm  ExpenseUiState 
kotlin.jvm  FocusSession 
kotlin.jvm  HabitLog 
kotlin.jvm  ImpulseControlUiState 
kotlin.jvm  MainUiState 
kotlin.jvm  MutableLiveData 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  NotificationManagerCompat 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  OnboardingUiState 
kotlin.jvm  Pair 
kotlin.jvm  
PayoffPlan 
kotlin.jvm  Screen 
kotlin.jvm  SettingsUiState 
kotlin.jvm  SharingStarted 
kotlin.jvm  SingletonComponent 
kotlin.jvm  SpeechRecognizer 
kotlin.jvm  SpendingPattern 
kotlin.jvm  SpendingReflection 
kotlin.jvm  StringListConverter 
kotlin.jvm  
SupervisorJob 
kotlin.jvm  Task 
kotlin.jvm  
TimerState 
kotlin.jvm  
TrendingUp 
kotlin.jvm  Triple 
kotlin.jvm  UserPreferences 
kotlin.jvm  	UserStats 
kotlin.jvm  
VirtualPet 
kotlin.jvm  VoiceCommand 
kotlin.jvm  Volatile 
kotlin.jvm  WishlistItem 
kotlin.jvm  _error 
kotlin.jvm  _isListening 
kotlin.jvm  _recognizedText 
kotlin.jvm  androidx 
kotlin.jvm  asStateFlow 
kotlin.jvm  com 
kotlin.jvm  	emptyList 
kotlin.jvm  
isNullOrEmpty 
kotlin.jvm  kotlinx 
kotlin.jvm  listOf 
kotlin.jvm  mutableMapOf 
kotlin.jvm  processVoiceCommand 
kotlin.jvm  stateIn 
kotlin.jvm  to 
kotlin.jvm  BudgetCategory kotlin.math  
CreditCard kotlin.math  Expense kotlin.math  	LocalDate kotlin.math  Month kotlin.math  Pair kotlin.math  abs kotlin.math  ceil kotlin.math  cos kotlin.math  max kotlin.math  min kotlin.math  sin kotlin.math  AICoachUiState 
kotlin.ranges  
AIInteraction 
kotlin.ranges  AccountabilityContact 
kotlin.ranges  Achievement 
kotlin.ranges  AlternativeProduct 
kotlin.ranges  BudgetAnalytics 
kotlin.ranges  BudgetCategory 
kotlin.ranges  BudgetRecommendation 
kotlin.ranges  
BudgetUiState 
kotlin.ranges  Context 
kotlin.ranges  
Converters 
kotlin.ranges  CoroutineScope 
kotlin.ranges  
CreditCard 
kotlin.ranges  DashboardUiState 
kotlin.ranges  DashboardWidget 
kotlin.ranges  DebtUiState 
kotlin.ranges  Dispatchers 
kotlin.ranges  EnhancedBudgetUiState 
kotlin.ranges  Expense 
kotlin.ranges  ExpenseUiState 
kotlin.ranges  FocusSession 
kotlin.ranges  HabitLog 
kotlin.ranges  ImpulseControlUiState 
kotlin.ranges  MainUiState 
kotlin.ranges  MutableLiveData 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  NotificationManagerCompat 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  OnboardingUiState 
kotlin.ranges  Pair 
kotlin.ranges  
PayoffPlan 
kotlin.ranges  Screen 
kotlin.ranges  SettingsUiState 
kotlin.ranges  SharingStarted 
kotlin.ranges  SingletonComponent 
kotlin.ranges  SpeechRecognizer 
kotlin.ranges  SpendingPattern 
kotlin.ranges  SpendingReflection 
kotlin.ranges  StringListConverter 
kotlin.ranges  
SupervisorJob 
kotlin.ranges  Task 
kotlin.ranges  
TimerState 
kotlin.ranges  
TrendingUp 
kotlin.ranges  Triple 
kotlin.ranges  UserPreferences 
kotlin.ranges  	UserStats 
kotlin.ranges  
VirtualPet 
kotlin.ranges  VoiceCommand 
kotlin.ranges  Volatile 
kotlin.ranges  WishlistItem 
kotlin.ranges  _error 
kotlin.ranges  _isListening 
kotlin.ranges  _recognizedText 
kotlin.ranges  androidx 
kotlin.ranges  asStateFlow 
kotlin.ranges  com 
kotlin.ranges  	emptyList 
kotlin.ranges  
isNullOrEmpty 
kotlin.ranges  kotlinx 
kotlin.ranges  listOf 
kotlin.ranges  mutableMapOf 
kotlin.ranges  processVoiceCommand 
kotlin.ranges  stateIn 
kotlin.ranges  to 
kotlin.ranges  KClass kotlin.reflect  AICoachUiState kotlin.sequences  
AIInteraction kotlin.sequences  AccountabilityContact kotlin.sequences  Achievement kotlin.sequences  AlternativeProduct kotlin.sequences  BudgetAnalytics kotlin.sequences  BudgetCategory kotlin.sequences  BudgetRecommendation kotlin.sequences  
BudgetUiState kotlin.sequences  Context kotlin.sequences  
Converters kotlin.sequences  CoroutineScope kotlin.sequences  
CreditCard kotlin.sequences  DashboardUiState kotlin.sequences  DashboardWidget kotlin.sequences  DebtUiState kotlin.sequences  Dispatchers kotlin.sequences  EnhancedBudgetUiState kotlin.sequences  Expense kotlin.sequences  ExpenseUiState kotlin.sequences  FocusSession kotlin.sequences  HabitLog kotlin.sequences  ImpulseControlUiState kotlin.sequences  MainUiState kotlin.sequences  MutableLiveData kotlin.sequences  MutableStateFlow kotlin.sequences  NotificationManagerCompat kotlin.sequences  OnConflictStrategy kotlin.sequences  OnboardingUiState kotlin.sequences  Pair kotlin.sequences  
PayoffPlan kotlin.sequences  Screen kotlin.sequences  SettingsUiState kotlin.sequences  SharingStarted kotlin.sequences  SingletonComponent kotlin.sequences  SpeechRecognizer kotlin.sequences  SpendingPattern kotlin.sequences  SpendingReflection kotlin.sequences  StringListConverter kotlin.sequences  
SupervisorJob kotlin.sequences  Task kotlin.sequences  
TimerState kotlin.sequences  
TrendingUp kotlin.sequences  Triple kotlin.sequences  UserPreferences kotlin.sequences  	UserStats kotlin.sequences  
VirtualPet kotlin.sequences  VoiceCommand kotlin.sequences  Volatile kotlin.sequences  WishlistItem kotlin.sequences  _error kotlin.sequences  _isListening kotlin.sequences  _recognizedText kotlin.sequences  androidx kotlin.sequences  asStateFlow kotlin.sequences  com kotlin.sequences  	emptyList kotlin.sequences  
isNullOrEmpty kotlin.sequences  kotlinx kotlin.sequences  listOf kotlin.sequences  mutableMapOf kotlin.sequences  processVoiceCommand kotlin.sequences  stateIn kotlin.sequences  to kotlin.sequences  AICoachUiState kotlin.text  
AIInteraction kotlin.text  AccountabilityContact kotlin.text  Achievement kotlin.text  AlternativeProduct kotlin.text  BudgetAnalytics kotlin.text  BudgetCategory kotlin.text  BudgetRecommendation kotlin.text  
BudgetUiState kotlin.text  Context kotlin.text  
Converters kotlin.text  CoroutineScope kotlin.text  
CreditCard kotlin.text  DashboardUiState kotlin.text  DashboardWidget kotlin.text  DebtUiState kotlin.text  Dispatchers kotlin.text  EnhancedBudgetUiState kotlin.text  Expense kotlin.text  ExpenseUiState kotlin.text  FocusSession kotlin.text  HabitLog kotlin.text  ImpulseControlUiState kotlin.text  MainUiState kotlin.text  MutableLiveData kotlin.text  MutableStateFlow kotlin.text  NotificationManagerCompat kotlin.text  OnConflictStrategy kotlin.text  OnboardingUiState kotlin.text  Pair kotlin.text  
PayoffPlan kotlin.text  Screen kotlin.text  SettingsUiState kotlin.text  SharingStarted kotlin.text  SingletonComponent kotlin.text  SpeechRecognizer kotlin.text  SpendingPattern kotlin.text  SpendingReflection kotlin.text  StringListConverter kotlin.text  
SupervisorJob kotlin.text  Task kotlin.text  
TimerState kotlin.text  
TrendingUp kotlin.text  Triple kotlin.text  UserPreferences kotlin.text  	UserStats kotlin.text  
VirtualPet kotlin.text  VoiceCommand kotlin.text  Volatile kotlin.text  WishlistItem kotlin.text  _error kotlin.text  _isListening kotlin.text  _recognizedText kotlin.text  androidx kotlin.text  asStateFlow kotlin.text  com kotlin.text  	emptyList kotlin.text  
isNullOrEmpty kotlin.text  kotlinx kotlin.text  listOf kotlin.text  mutableMapOf kotlin.text  processVoiceCommand kotlin.text  stateIn kotlin.text  to kotlin.text  BudgetAnalytics kotlinx.coroutines  CompletableJob kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  CoroutineWorker kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  
LocalDateTime kotlinx.coroutines  MutableStateFlow kotlinx.coroutines  SpendingPattern kotlinx.coroutines  	StateFlow kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  
TimerState kotlinx.coroutines  WorkerParameters kotlinx.coroutines  asStateFlow kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  mutableMapOf kotlinx.coroutines  withContext kotlinx.coroutines  plus &kotlinx.coroutines.CoroutineDispatcher  IO kotlinx.coroutines.Dispatchers  AICoachUiState kotlinx.coroutines.flow  
BudgetUiState kotlinx.coroutines.flow  DashboardUiState kotlinx.coroutines.flow  DebtUiState kotlinx.coroutines.flow  EnhancedBudgetUiState kotlinx.coroutines.flow  ExpenseUiState kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  ImpulseControlUiState kotlinx.coroutines.flow  Job kotlinx.coroutines.flow  	LocalDate kotlinx.coroutines.flow  
LocalDateTime kotlinx.coroutines.flow  MainUiState kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  OnboardingUiState kotlinx.coroutines.flow  Pair kotlinx.coroutines.flow  
PayoffPlan kotlinx.coroutines.flow  SettingsUiState kotlinx.coroutines.flow  SharingStarted kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  
TimerState kotlinx.coroutines.flow  Triple kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  com kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  first kotlinx.coroutines.flow  flow kotlinx.coroutines.flow  listOf kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  to kotlinx.coroutines.flow  viewModelScope kotlinx.coroutines.flow  
getSTATEIn kotlinx.coroutines.flow.Flow  
getStateIn kotlinx.coroutines.flow.Flow  stateIn kotlinx.coroutines.flow.Flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  WhileSubscribed &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed 0kotlinx.coroutines.flow.SharingStarted.Companion  BudgetCategory kotlinx.datetime  
BudgetUiState kotlinx.datetime  Clock kotlinx.datetime  
Composable kotlinx.datetime  
CreditCard kotlinx.datetime  DashboardUiState kotlinx.datetime  DebtUiState kotlinx.datetime  EnhancedBudgetUiState kotlinx.datetime  Expense kotlinx.datetime  ExpenseUiState kotlinx.datetime  Flow kotlinx.datetime  ImpulseControlUiState kotlinx.datetime  Job kotlinx.datetime  	LocalDate kotlinx.datetime  
LocalDateTime kotlinx.datetime  Month kotlinx.datetime  MutableStateFlow kotlinx.datetime  Pair kotlinx.datetime  
PayoffPlan kotlinx.datetime  SharingStarted kotlinx.datetime  	StateFlow kotlinx.datetime  TimeZone kotlinx.datetime  
TimerState kotlinx.datetime  Triple kotlinx.datetime  asStateFlow kotlinx.datetime  com kotlinx.datetime  	emptyList kotlinx.datetime  listOf kotlinx.datetime  stateIn kotlinx.datetime  to kotlinx.datetime  toLocalDateTime kotlinx.datetime  viewModelScope kotlinx.datetime  Timeline ,androidx.compose.material.icons.Icons.Filled  Timeline &androidx.compose.material.icons.filled  Timeline com.focusflow.navigation  Timeline com.focusflow.navigation.Screen  Timeline -com.focusflow.navigation.Screen.PayoffPlanner  Timeline 	java.lang  Timeline kotlin  Timeline kotlin.annotation  Timeline kotlin.collections  Timeline kotlin.comparisons  Timeline 	kotlin.io  Timeline 
kotlin.jvm  Timeline 
kotlin.ranges  Timeline kotlin.sequences  Timeline kotlin.text  Schedule ,androidx.compose.material.icons.Icons.Filled  Schedule &androidx.compose.material.icons.filled  Schedule com.focusflow.navigation  Schedule com.focusflow.navigation.Screen  Schedule -com.focusflow.navigation.Screen.PayoffPlanner  Schedule 	java.lang  Schedule kotlin  Schedule kotlin.annotation  Schedule kotlin.collections  Schedule kotlin.comparisons  Schedule 	kotlin.io  Schedule 
kotlin.jvm  Schedule 
kotlin.ranges  Schedule kotlin.sequences  Schedule kotlin.text  List -com.focusflow.navigation.Screen.PayoffPlanner  PayoffMetricCard com.focusflow.ui.screens  PayoffTimelineCard com.focusflow.ui.screens  StrategyComparisonItem com.focusflow.ui.screens  EnhancedStrategySelectionCard com.focusflow.ui.screens  EnhancedStrategyOption com.focusflow.ui.screens  GoalSelectionCard com.focusflow.ui.screens  MonthlyScheduleCard com.focusflow.ui.screens  TimelineMilestone com.focusflow.ui.screens  EnhancedPayoffStepItem com.focusflow.ui.screens  EnhancedStrategyComparisonCard com.focusflow.ui.screens  EnhancedPayoffSummaryCard com.focusflow.ui.screens  EnhancedPayoffResultsSection com.focusflow.ui.screens  GoalTypeOption com.focusflow.ui.screens  androidx androidx.compose.animation.core  androidx com.focusflow.ui.screens  calculateExtraPayment com.focusflow.ui.screens  formatPayoffDate com.focusflow.ui.screens  formatTimelineDate com.focusflow.ui.screens  getGoalAchievementMessage com.focusflow.ui.screens  androidx kotlinx.datetime  TransferFundsDialog com.focusflow.ui.components  EmptyBudgetCategoriesCard com.focusflow.ui.screens  EnvelopeBudgetCard com.focusflow.ui.components  IncomeInputDialog com.focusflow.ui.components  ViewMode com.focusflow.ui.screens  EnvelopeGrid com.focusflow.ui.components  ZeroBudgetUiState com.focusflow.ui.viewmodel  ZeroBudgetOverviewCard com.focusflow.ui.components  ZeroBudgetViewModel com.focusflow.ui.viewmodel  QuickActionsCard com.focusflow.ui.screens  EnhancedBudgetScreen com.focusflow.ui.screens  EnhancedBudgetCategoryCard com.focusflow.ui.components  IncomeSetupCard com.focusflow.ui.components  TextOverflow androidx.compose.ui.text.style  ZeroBudgetUiState androidx.lifecycle.ViewModel  BudgetAmountColumn com.focusflow.ui.components  Pair com.focusflow.ui.components  Boolean ,com.focusflow.ui.viewmodel.ZeroBudgetUiState  BudgetCategory ,com.focusflow.ui.viewmodel.ZeroBudgetUiState  Double ,com.focusflow.ui.viewmodel.ZeroBudgetUiState  List ,com.focusflow.ui.viewmodel.ZeroBudgetUiState  String ,com.focusflow.ui.viewmodel.ZeroBudgetUiState  BudgetCategory .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  BudgetCategoryRepository .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  Double .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  ExpenseRepository .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  Inject .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  Int .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  
LocalDateTime .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  Long .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  MutableStateFlow .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  	StateFlow .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  String .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  Triple .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  UserPreferencesRepository .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  ZeroBudgetUiState .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  _uiState .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  asStateFlow .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  getASStateFlow .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  getAsStateFlow .com.focusflow.ui.viewmodel.ZeroBudgetViewModel  ZeroBudgetUiState 	java.lang  ZeroBudgetUiState kotlin  ZeroBudgetUiState kotlin.annotation  ZeroBudgetUiState kotlin.collections  ZeroBudgetUiState kotlin.comparisons  ZeroBudgetUiState 	kotlin.io  ZeroBudgetUiState 
kotlin.jvm  ZeroBudgetUiState 
kotlin.ranges  ZeroBudgetUiState kotlin.sequences  ZeroBudgetUiState kotlin.text  ZeroBudgetUiState kotlinx.coroutines.flow  ZeroBudgetUiState kotlinx.datetime  	Migration androidx.room.RoomDatabase  SupportSQLiteDatabase androidx.room.RoomDatabase  	Migration androidx.room.migration  SupportSQLiteDatabase !androidx.room.migration.Migration  SupportSQLiteDatabase androidx.sqlite.db  execSQL (androidx.sqlite.db.SupportSQLiteDatabase  	Migration -com.focusflow.data.database.FocusFlowDatabase  SupportSQLiteDatabase -com.focusflow.data.database.FocusFlowDatabase  	Migration 7com.focusflow.data.database.FocusFlowDatabase.Companion  SupportSQLiteDatabase 7com.focusflow.data.database.FocusFlowDatabase.Companion  ImpulseControlSettingsCard com.focusflow.ui.components  BudgetImpactCard com.focusflow.ui.components  BudgetImpactPreview com.focusflow.ui.viewmodel  EnhancedAddExpenseDialog com.focusflow.ui.components  TotalBudgetImpact com.focusflow.ui.viewmodel  BudgetImpactPreview androidx.lifecycle.ViewModel  TotalBudgetImpact androidx.lifecycle.ViewModel  Int (com.focusflow.data.model.UserPreferences  CoolingOffPeriodDialog com.focusflow.ui.components  SettingItem com.focusflow.ui.components  SettingToggleItem com.focusflow.ui.components  SpendingThresholdDialog com.focusflow.ui.components  com com.focusflow.ui.components  Boolean .com.focusflow.ui.viewmodel.BudgetImpactPreview  BudgetCategory .com.focusflow.ui.viewmodel.BudgetImpactPreview  Double .com.focusflow.ui.viewmodel.BudgetImpactPreview  List .com.focusflow.ui.viewmodel.BudgetImpactPreview  TotalBudgetImpact .com.focusflow.ui.viewmodel.BudgetImpactPreview  Double 0com.focusflow.ui.viewmodel.ImpulseControlUiState  BudgetCategory 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  BudgetCategoryRepository 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  BudgetImpactPreview 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  List 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  TotalBudgetImpact 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  UserPreferencesRepository 2com.focusflow.ui.viewmodel.ImpulseControlViewModel  Double ,com.focusflow.ui.viewmodel.TotalBudgetImpact  PaymentSchedule com.focusflow.data.model  PayoffComparison com.focusflow.ui.viewmodel  PaymentScheduleDao com.focusflow.data.dao  
PayoffPlanDao com.focusflow.data.dao  PayoffPlannerUiState com.focusflow.ui.viewmodel  CurrentPlanCard com.focusflow.ui.components  PayoffComparison com.focusflow.data.model  BudgetIntegrationCard com.focusflow.ui.components  PayoffMilestoneDao com.focusflow.data.dao  PayoffPlannerViewModel com.focusflow.ui.viewmodel  
PayoffPlan com.focusflow.data.model  EnhancedSummaryMetric com.focusflow.ui.screens  PayoffMilestone com.focusflow.data.model  StrategyComparisonColumn com.focusflow.ui.screens  ADHDFriendlyCard com.focusflow.ui.components  PayoffPlanRepository com.focusflow.data.repository  TimelineMilestoneItem com.focusflow.ui.screens  StrategyOptionCard com.focusflow.ui.components  CreatePlanDialog com.focusflow.ui.components  StrategyMetric com.focusflow.ui.components  ColumnScope androidx.compose.animation.core  PaymentSchedule androidx.lifecycle.ViewModel  PayoffComparison androidx.lifecycle.ViewModel  PayoffMilestone androidx.lifecycle.ViewModel  PayoffPlanRepository androidx.lifecycle.ViewModel  PayoffPlannerUiState androidx.lifecycle.ViewModel  PaymentScheduleDao androidx.room.RoomDatabase  PayoffMilestoneDao androidx.room.RoomDatabase  
PayoffPlanDao androidx.room.RoomDatabase  PaymentSchedule com.focusflow.data.dao  PayoffMilestone com.focusflow.data.dao  
PayoffPlan com.focusflow.data.dao  Delete )com.focusflow.data.dao.PaymentScheduleDao  Double )com.focusflow.data.dao.PaymentScheduleDao  Flow )com.focusflow.data.dao.PaymentScheduleDao  Insert )com.focusflow.data.dao.PaymentScheduleDao  List )com.focusflow.data.dao.PaymentScheduleDao  	LocalDate )com.focusflow.data.dao.PaymentScheduleDao  Long )com.focusflow.data.dao.PaymentScheduleDao  PaymentSchedule )com.focusflow.data.dao.PaymentScheduleDao  Query )com.focusflow.data.dao.PaymentScheduleDao  Update )com.focusflow.data.dao.PaymentScheduleDao  deletePaymentSchedulesByPlan )com.focusflow.data.dao.PaymentScheduleDao  insertPaymentSchedules )com.focusflow.data.dao.PaymentScheduleDao  Delete )com.focusflow.data.dao.PayoffMilestoneDao  Flow )com.focusflow.data.dao.PayoffMilestoneDao  Insert )com.focusflow.data.dao.PayoffMilestoneDao  Int )com.focusflow.data.dao.PayoffMilestoneDao  List )com.focusflow.data.dao.PayoffMilestoneDao  	LocalDate )com.focusflow.data.dao.PayoffMilestoneDao  Long )com.focusflow.data.dao.PayoffMilestoneDao  PayoffMilestone )com.focusflow.data.dao.PayoffMilestoneDao  Query )com.focusflow.data.dao.PayoffMilestoneDao  Update )com.focusflow.data.dao.PayoffMilestoneDao  deleteMilestonesByPlan )com.focusflow.data.dao.PayoffMilestoneDao  insertMilestones )com.focusflow.data.dao.PayoffMilestoneDao  markMilestoneCompleted )com.focusflow.data.dao.PayoffMilestoneDao  Delete $com.focusflow.data.dao.PayoffPlanDao  Flow $com.focusflow.data.dao.PayoffPlanDao  Insert $com.focusflow.data.dao.PayoffPlanDao  Int $com.focusflow.data.dao.PayoffPlanDao  List $com.focusflow.data.dao.PayoffPlanDao  Long $com.focusflow.data.dao.PayoffPlanDao  
PayoffPlan $com.focusflow.data.dao.PayoffPlanDao  Query $com.focusflow.data.dao.PayoffPlanDao  String $com.focusflow.data.dao.PayoffPlanDao  Update $com.focusflow.data.dao.PayoffPlanDao  deletePayoffPlan $com.focusflow.data.dao.PayoffPlanDao  updatePayoffPlan $com.focusflow.data.dao.PayoffPlanDao  PaymentSchedule com.focusflow.data.database  PaymentScheduleDao com.focusflow.data.database  PayoffMilestone com.focusflow.data.database  PayoffMilestoneDao com.focusflow.data.database  
PayoffPlan com.focusflow.data.database  
PayoffPlanDao com.focusflow.data.database  PaymentScheduleDao -com.focusflow.data.database.FocusFlowDatabase  PayoffMilestoneDao -com.focusflow.data.database.FocusFlowDatabase  
PayoffPlanDao -com.focusflow.data.database.FocusFlowDatabase  PaymentScheduleDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  PayoffMilestoneDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  
PayoffPlanDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  MutableStateFlow com.focusflow.data.model  PaymentScheduleDao com.focusflow.data.model  PayoffMilestoneDao com.focusflow.data.model  
PayoffPlanDao com.focusflow.data.model  PayoffPlannerUiState com.focusflow.data.model  	StateFlow com.focusflow.data.model  asStateFlow com.focusflow.data.model  Boolean (com.focusflow.data.model.PaymentSchedule  Double (com.focusflow.data.model.PaymentSchedule  Int (com.focusflow.data.model.PaymentSchedule  	LocalDate (com.focusflow.data.model.PaymentSchedule  Long (com.focusflow.data.model.PaymentSchedule  
PrimaryKey (com.focusflow.data.model.PaymentSchedule  Double )com.focusflow.data.model.PayoffComparison  Int )com.focusflow.data.model.PayoffComparison  	LocalDate )com.focusflow.data.model.PayoffComparison  PayoffStrategy )com.focusflow.data.model.PayoffComparison  Boolean (com.focusflow.data.model.PayoffMilestone  Double (com.focusflow.data.model.PayoffMilestone  	LocalDate (com.focusflow.data.model.PayoffMilestone  Long (com.focusflow.data.model.PayoffMilestone  
PrimaryKey (com.focusflow.data.model.PayoffMilestone  String (com.focusflow.data.model.PayoffMilestone  Boolean #com.focusflow.data.model.PayoffPlan  Double #com.focusflow.data.model.PayoffPlan  Int #com.focusflow.data.model.PayoffPlan  	LocalDate #com.focusflow.data.model.PayoffPlan  
LocalDateTime #com.focusflow.data.model.PayoffPlan  Long #com.focusflow.data.model.PayoffPlan  
PrimaryKey #com.focusflow.data.model.PayoffPlan  String #com.focusflow.data.model.PayoffPlan  Flow 2com.focusflow.data.repository.PayoffPlanRepository  Inject 2com.focusflow.data.repository.PayoffPlanRepository  List 2com.focusflow.data.repository.PayoffPlanRepository  	LocalDate 2com.focusflow.data.repository.PayoffPlanRepository  Long 2com.focusflow.data.repository.PayoffPlanRepository  PaymentSchedule 2com.focusflow.data.repository.PayoffPlanRepository  PaymentScheduleDao 2com.focusflow.data.repository.PayoffPlanRepository  PayoffMilestone 2com.focusflow.data.repository.PayoffPlanRepository  PayoffMilestoneDao 2com.focusflow.data.repository.PayoffPlanRepository  
PayoffPlan 2com.focusflow.data.repository.PayoffPlanRepository  
PayoffPlanDao 2com.focusflow.data.repository.PayoffPlanRepository  String 2com.focusflow.data.repository.PayoffPlanRepository  getCurrentActivePayoffPlanFlow 2com.focusflow.data.repository.PayoffPlanRepository  paymentScheduleDao 2com.focusflow.data.repository.PayoffPlanRepository  payoffMilestoneDao 2com.focusflow.data.repository.PayoffPlanRepository  
payoffPlanDao 2com.focusflow.data.repository.PayoffPlanRepository  PaymentScheduleDao com.focusflow.di  PayoffMilestoneDao com.focusflow.di  
PayoffPlanDao com.focusflow.di  PaymentScheduleDao com.focusflow.di.DatabaseModule  PayoffMilestoneDao com.focusflow.di.DatabaseModule  
PayoffPlanDao com.focusflow.di.DatabaseModule  Boolean *com.focusflow.ui.screens.TimelineMilestone  Int *com.focusflow.ui.screens.TimelineMilestone  String *com.focusflow.ui.screens.TimelineMilestone  
CreditCard com.focusflow.ui.viewmodel  PaymentSchedule com.focusflow.ui.viewmodel  PayoffMilestone com.focusflow.ui.viewmodel  Double +com.focusflow.ui.viewmodel.PayoffComparison  Int +com.focusflow.ui.viewmodel.PayoffComparison  	LocalDate +com.focusflow.ui.viewmodel.PayoffComparison  PayoffStrategy +com.focusflow.ui.viewmodel.PayoffComparison  Boolean /com.focusflow.ui.viewmodel.PayoffPlannerUiState  Double /com.focusflow.ui.viewmodel.PayoffPlannerUiState  PayoffComparison /com.focusflow.ui.viewmodel.PayoffPlannerUiState  PayoffStrategy /com.focusflow.ui.viewmodel.PayoffPlannerUiState  String /com.focusflow.ui.viewmodel.PayoffPlannerUiState  BudgetCategoryRepository 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  
CreditCard 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  CreditCardRepository 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  Double 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  Inject 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  Int 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  List 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  MutableStateFlow 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  PaymentSchedule 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  PayoffComparison 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  PayoffMilestone 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  PayoffPlanRepository 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  PayoffPlannerUiState 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  PayoffStrategy 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  	StateFlow 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  String 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  UserPreferencesRepository 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  _uiState 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  asStateFlow 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  creditCardRepository 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  getASStateFlow 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  getAsStateFlow 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  payoffPlanRepository 1com.focusflow.ui.viewmodel.PayoffPlannerViewModel  PaymentSchedule 	java.lang  PayoffMilestone 	java.lang  PayoffPlannerUiState 	java.lang  NumberFormat 	java.text  ColumnScope 	java.util  androidx 	java.util  com 	java.util  PaymentSchedule kotlin  PayoffMilestone kotlin  PayoffPlannerUiState kotlin  PaymentSchedule kotlin.annotation  PayoffMilestone kotlin.annotation  PayoffPlannerUiState kotlin.annotation  PaymentSchedule kotlin.collections  PayoffMilestone kotlin.collections  PayoffPlannerUiState kotlin.collections  PaymentSchedule kotlin.comparisons  PayoffMilestone kotlin.comparisons  PayoffPlannerUiState kotlin.comparisons  PaymentSchedule 	kotlin.io  PayoffMilestone 	kotlin.io  PayoffPlannerUiState 	kotlin.io  PaymentSchedule 
kotlin.jvm  PayoffMilestone 
kotlin.jvm  PayoffPlannerUiState 
kotlin.jvm  PaymentSchedule 
kotlin.ranges  PayoffMilestone 
kotlin.ranges  PayoffPlannerUiState 
kotlin.ranges  PaymentSchedule kotlin.sequences  PayoffMilestone kotlin.sequences  PayoffPlannerUiState kotlin.sequences  PaymentSchedule kotlin.text  PayoffMilestone kotlin.text  PayoffPlannerUiState kotlin.text  
CreditCard kotlinx.coroutines.flow  PaymentSchedule kotlinx.coroutines.flow  PayoffMilestone kotlinx.coroutines.flow  PayoffPlannerUiState kotlinx.coroutines.flow  PaymentSchedule kotlinx.datetime  PayoffMilestone kotlinx.datetime  PayoffPlannerUiState kotlinx.datetime  DebtMetricColumn com.focusflow.ui.screens  ExtraPaymentInputCard com.focusflow.ui.screens  
DebtViewModel androidx.compose.animation  
PayoffPlan androidx.compose.animation  
PayoffStep androidx.compose.animation  
DebtViewModel androidx.compose.animation.core  
PayoffPlan androidx.compose.animation.core  
PayoffStep androidx.compose.animation.core  
DebtViewModel "androidx.compose.foundation.layout  
PayoffPlan "androidx.compose.foundation.layout  
PayoffStep "androidx.compose.foundation.layout  
DebtViewModel androidx.compose.material  
PayoffPlan androidx.compose.material  
PayoffStep androidx.compose.material  
DebtViewModel &androidx.compose.material.icons.filled  
PayoffPlan &androidx.compose.material.icons.filled  
PayoffStep &androidx.compose.material.icons.filled  
DebtViewModel androidx.compose.runtime  
PayoffPlan androidx.compose.runtime  
PayoffStep androidx.compose.runtime  
DebtViewModel com.focusflow.ui.components  
PayoffPlan com.focusflow.ui.components  
PayoffStep com.focusflow.ui.components  
DebtViewModel com.focusflow.ui.screens  
PayoffPlan com.focusflow.ui.screens  
PayoffStep com.focusflow.ui.screens  
DebtViewModel 	java.lang  
PayoffStep 	java.lang  
DebtViewModel 	java.util  
PayoffPlan 	java.util  
PayoffStep 	java.util  
DebtViewModel kotlin  
PayoffStep kotlin  
DebtViewModel kotlin.annotation  
PayoffStep kotlin.annotation  
DebtViewModel kotlin.collections  
PayoffStep kotlin.collections  
DebtViewModel kotlin.comparisons  
PayoffStep kotlin.comparisons  
DebtViewModel 	kotlin.io  
PayoffStep 	kotlin.io  
DebtViewModel 
kotlin.jvm  
PayoffStep 
kotlin.jvm  
DebtViewModel 
kotlin.ranges  
PayoffStep 
kotlin.ranges  
DebtViewModel kotlin.sequences  
PayoffStep kotlin.sequences  
DebtViewModel kotlin.text  
PayoffStep kotlin.text  
DebtViewModel kotlinx.datetime  
PayoffStep kotlinx.datetime  PayoffComparison kotlinx.coroutines.flow  PayoffComparison kotlinx.datetime                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       