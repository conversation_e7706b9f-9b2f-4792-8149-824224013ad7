package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.AccountabilityContact;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDateTime;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AccountabilityContactDao_Impl implements AccountabilityContactDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<AccountabilityContact> __insertionAdapterOfAccountabilityContact;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<AccountabilityContact> __deletionAdapterOfAccountabilityContact;

  private final EntityDeletionOrUpdateAdapter<AccountabilityContact> __updateAdapterOfAccountabilityContact;

  private final SharedSQLiteStatement __preparedStmtOfRecordInteraction;

  private final SharedSQLiteStatement __preparedStmtOfRecordSuccessfulIntervention;

  private final SharedSQLiteStatement __preparedStmtOfUpdateTrustLevel;

  private final SharedSQLiteStatement __preparedStmtOfUpdateResponseRate;

  private final SharedSQLiteStatement __preparedStmtOfDeactivateContact;

  public AccountabilityContactDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfAccountabilityContact = new EntityInsertionAdapter<AccountabilityContact>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `accountability_contacts` (`id`,`name`,`relationship`,`contactMethod`,`contactInfo`,`isActive`,`permissionLevel`,`sharingPreferences`,`addedDate`,`lastContactDate`,`totalInteractions`,`successfulInterventions`,`trustLevel`,`responseRate`,`averageResponseTime`,`preferredContactTime`,`timeZone`,`notificationFrequency`,`supportType`,`specializations`,`notes`,`emergencyContact`,`canReceiveSpendingAlerts`,`canReceiveBudgetUpdates`,`canReceiveGoalProgress`,`canReceiveEmergencyAlerts`,`mutualAccountability`,`partnerUserId`,`consentGiven`,`consentDate`,`lastConsentUpdate`,`privacyLevel`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AccountabilityContact entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        if (entity.getRelationship() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getRelationship());
        }
        if (entity.getContactMethod() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getContactMethod());
        }
        if (entity.getContactInfo() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getContactInfo());
        }
        final int _tmp = entity.isActive() ? 1 : 0;
        statement.bindLong(6, _tmp);
        if (entity.getPermissionLevel() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getPermissionLevel());
        }
        if (entity.getSharingPreferences() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getSharingPreferences());
        }
        final String _tmp_1 = __converters.fromLocalDateTime(entity.getAddedDate());
        if (_tmp_1 == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp_1);
        }
        final String _tmp_2 = __converters.fromLocalDateTime(entity.getLastContactDate());
        if (_tmp_2 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_2);
        }
        statement.bindLong(11, entity.getTotalInteractions());
        statement.bindLong(12, entity.getSuccessfulInterventions());
        statement.bindLong(13, entity.getTrustLevel());
        statement.bindDouble(14, entity.getResponseRate());
        if (entity.getAverageResponseTime() == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, entity.getAverageResponseTime());
        }
        if (entity.getPreferredContactTime() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getPreferredContactTime());
        }
        if (entity.getTimeZone() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getTimeZone());
        }
        if (entity.getNotificationFrequency() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getNotificationFrequency());
        }
        if (entity.getSupportType() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getSupportType());
        }
        if (entity.getSpecializations() == null) {
          statement.bindNull(20);
        } else {
          statement.bindString(20, entity.getSpecializations());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getNotes());
        }
        final int _tmp_3 = entity.getEmergencyContact() ? 1 : 0;
        statement.bindLong(22, _tmp_3);
        final int _tmp_4 = entity.getCanReceiveSpendingAlerts() ? 1 : 0;
        statement.bindLong(23, _tmp_4);
        final int _tmp_5 = entity.getCanReceiveBudgetUpdates() ? 1 : 0;
        statement.bindLong(24, _tmp_5);
        final int _tmp_6 = entity.getCanReceiveGoalProgress() ? 1 : 0;
        statement.bindLong(25, _tmp_6);
        final int _tmp_7 = entity.getCanReceiveEmergencyAlerts() ? 1 : 0;
        statement.bindLong(26, _tmp_7);
        final int _tmp_8 = entity.getMutualAccountability() ? 1 : 0;
        statement.bindLong(27, _tmp_8);
        if (entity.getPartnerUserId() == null) {
          statement.bindNull(28);
        } else {
          statement.bindString(28, entity.getPartnerUserId());
        }
        final int _tmp_9 = entity.getConsentGiven() ? 1 : 0;
        statement.bindLong(29, _tmp_9);
        final String _tmp_10 = __converters.fromLocalDateTime(entity.getConsentDate());
        if (_tmp_10 == null) {
          statement.bindNull(30);
        } else {
          statement.bindString(30, _tmp_10);
        }
        final String _tmp_11 = __converters.fromLocalDateTime(entity.getLastConsentUpdate());
        if (_tmp_11 == null) {
          statement.bindNull(31);
        } else {
          statement.bindString(31, _tmp_11);
        }
        if (entity.getPrivacyLevel() == null) {
          statement.bindNull(32);
        } else {
          statement.bindString(32, entity.getPrivacyLevel());
        }
      }
    };
    this.__deletionAdapterOfAccountabilityContact = new EntityDeletionOrUpdateAdapter<AccountabilityContact>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `accountability_contacts` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AccountabilityContact entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfAccountabilityContact = new EntityDeletionOrUpdateAdapter<AccountabilityContact>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `accountability_contacts` SET `id` = ?,`name` = ?,`relationship` = ?,`contactMethod` = ?,`contactInfo` = ?,`isActive` = ?,`permissionLevel` = ?,`sharingPreferences` = ?,`addedDate` = ?,`lastContactDate` = ?,`totalInteractions` = ?,`successfulInterventions` = ?,`trustLevel` = ?,`responseRate` = ?,`averageResponseTime` = ?,`preferredContactTime` = ?,`timeZone` = ?,`notificationFrequency` = ?,`supportType` = ?,`specializations` = ?,`notes` = ?,`emergencyContact` = ?,`canReceiveSpendingAlerts` = ?,`canReceiveBudgetUpdates` = ?,`canReceiveGoalProgress` = ?,`canReceiveEmergencyAlerts` = ?,`mutualAccountability` = ?,`partnerUserId` = ?,`consentGiven` = ?,`consentDate` = ?,`lastConsentUpdate` = ?,`privacyLevel` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AccountabilityContact entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        if (entity.getRelationship() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getRelationship());
        }
        if (entity.getContactMethod() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getContactMethod());
        }
        if (entity.getContactInfo() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getContactInfo());
        }
        final int _tmp = entity.isActive() ? 1 : 0;
        statement.bindLong(6, _tmp);
        if (entity.getPermissionLevel() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getPermissionLevel());
        }
        if (entity.getSharingPreferences() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getSharingPreferences());
        }
        final String _tmp_1 = __converters.fromLocalDateTime(entity.getAddedDate());
        if (_tmp_1 == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp_1);
        }
        final String _tmp_2 = __converters.fromLocalDateTime(entity.getLastContactDate());
        if (_tmp_2 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_2);
        }
        statement.bindLong(11, entity.getTotalInteractions());
        statement.bindLong(12, entity.getSuccessfulInterventions());
        statement.bindLong(13, entity.getTrustLevel());
        statement.bindDouble(14, entity.getResponseRate());
        if (entity.getAverageResponseTime() == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, entity.getAverageResponseTime());
        }
        if (entity.getPreferredContactTime() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getPreferredContactTime());
        }
        if (entity.getTimeZone() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getTimeZone());
        }
        if (entity.getNotificationFrequency() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getNotificationFrequency());
        }
        if (entity.getSupportType() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getSupportType());
        }
        if (entity.getSpecializations() == null) {
          statement.bindNull(20);
        } else {
          statement.bindString(20, entity.getSpecializations());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getNotes());
        }
        final int _tmp_3 = entity.getEmergencyContact() ? 1 : 0;
        statement.bindLong(22, _tmp_3);
        final int _tmp_4 = entity.getCanReceiveSpendingAlerts() ? 1 : 0;
        statement.bindLong(23, _tmp_4);
        final int _tmp_5 = entity.getCanReceiveBudgetUpdates() ? 1 : 0;
        statement.bindLong(24, _tmp_5);
        final int _tmp_6 = entity.getCanReceiveGoalProgress() ? 1 : 0;
        statement.bindLong(25, _tmp_6);
        final int _tmp_7 = entity.getCanReceiveEmergencyAlerts() ? 1 : 0;
        statement.bindLong(26, _tmp_7);
        final int _tmp_8 = entity.getMutualAccountability() ? 1 : 0;
        statement.bindLong(27, _tmp_8);
        if (entity.getPartnerUserId() == null) {
          statement.bindNull(28);
        } else {
          statement.bindString(28, entity.getPartnerUserId());
        }
        final int _tmp_9 = entity.getConsentGiven() ? 1 : 0;
        statement.bindLong(29, _tmp_9);
        final String _tmp_10 = __converters.fromLocalDateTime(entity.getConsentDate());
        if (_tmp_10 == null) {
          statement.bindNull(30);
        } else {
          statement.bindString(30, _tmp_10);
        }
        final String _tmp_11 = __converters.fromLocalDateTime(entity.getLastConsentUpdate());
        if (_tmp_11 == null) {
          statement.bindNull(31);
        } else {
          statement.bindString(31, _tmp_11);
        }
        if (entity.getPrivacyLevel() == null) {
          statement.bindNull(32);
        } else {
          statement.bindString(32, entity.getPrivacyLevel());
        }
        statement.bindLong(33, entity.getId());
      }
    };
    this.__preparedStmtOfRecordInteraction = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE accountability_contacts SET lastContactDate = ?, totalInteractions = totalInteractions + 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfRecordSuccessfulIntervention = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE accountability_contacts SET successfulInterventions = successfulInterventions + 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateTrustLevel = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE accountability_contacts SET trustLevel = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateResponseRate = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE accountability_contacts SET responseRate = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeactivateContact = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE accountability_contacts SET isActive = 0 WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertContact(final AccountabilityContact contact,
      final Continuation<? super Long> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfAccountabilityContact.insertAndReturnId(contact);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object deleteContact(final AccountabilityContact contact,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfAccountabilityContact.handle(contact);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object updateContact(final AccountabilityContact contact,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfAccountabilityContact.handle(contact);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object recordInteraction(final long id, final LocalDateTime contactDate,
      final Continuation<? super Unit> arg2) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfRecordInteraction.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(contactDate);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfRecordInteraction.release(_stmt);
        }
      }
    }, arg2);
  }

  @Override
  public Object recordSuccessfulIntervention(final long id, final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfRecordSuccessfulIntervention.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfRecordSuccessfulIntervention.release(_stmt);
        }
      }
    }, arg1);
  }

  @Override
  public Object updateTrustLevel(final long id, final int trustLevel,
      final Continuation<? super Unit> arg2) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateTrustLevel.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, trustLevel);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateTrustLevel.release(_stmt);
        }
      }
    }, arg2);
  }

  @Override
  public Object updateResponseRate(final long id, final double responseRate,
      final Continuation<? super Unit> arg2) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateResponseRate.acquire();
        int _argIndex = 1;
        _stmt.bindDouble(_argIndex, responseRate);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateResponseRate.release(_stmt);
        }
      }
    }, arg2);
  }

  @Override
  public Object deactivateContact(final long id, final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeactivateContact.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeactivateContact.release(_stmt);
        }
      }
    }, arg1);
  }

  @Override
  public Flow<List<AccountabilityContact>> getAllActiveContacts() {
    final String _sql = "SELECT `accountability_contacts`.`id` AS `id`, `accountability_contacts`.`name` AS `name`, `accountability_contacts`.`relationship` AS `relationship`, `accountability_contacts`.`contactMethod` AS `contactMethod`, `accountability_contacts`.`contactInfo` AS `contactInfo`, `accountability_contacts`.`isActive` AS `isActive`, `accountability_contacts`.`permissionLevel` AS `permissionLevel`, `accountability_contacts`.`sharingPreferences` AS `sharingPreferences`, `accountability_contacts`.`addedDate` AS `addedDate`, `accountability_contacts`.`lastContactDate` AS `lastContactDate`, `accountability_contacts`.`totalInteractions` AS `totalInteractions`, `accountability_contacts`.`successfulInterventions` AS `successfulInterventions`, `accountability_contacts`.`trustLevel` AS `trustLevel`, `accountability_contacts`.`responseRate` AS `responseRate`, `accountability_contacts`.`averageResponseTime` AS `averageResponseTime`, `accountability_contacts`.`preferredContactTime` AS `preferredContactTime`, `accountability_contacts`.`timeZone` AS `timeZone`, `accountability_contacts`.`notificationFrequency` AS `notificationFrequency`, `accountability_contacts`.`supportType` AS `supportType`, `accountability_contacts`.`specializations` AS `specializations`, `accountability_contacts`.`notes` AS `notes`, `accountability_contacts`.`emergencyContact` AS `emergencyContact`, `accountability_contacts`.`canReceiveSpendingAlerts` AS `canReceiveSpendingAlerts`, `accountability_contacts`.`canReceiveBudgetUpdates` AS `canReceiveBudgetUpdates`, `accountability_contacts`.`canReceiveGoalProgress` AS `canReceiveGoalProgress`, `accountability_contacts`.`canReceiveEmergencyAlerts` AS `canReceiveEmergencyAlerts`, `accountability_contacts`.`mutualAccountability` AS `mutualAccountability`, `accountability_contacts`.`partnerUserId` AS `partnerUserId`, `accountability_contacts`.`consentGiven` AS `consentGiven`, `accountability_contacts`.`consentDate` AS `consentDate`, `accountability_contacts`.`lastConsentUpdate` AS `lastConsentUpdate`, `accountability_contacts`.`privacyLevel` AS `privacyLevel` FROM accountability_contacts WHERE isActive = 1 ORDER BY trustLevel DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"accountability_contacts"}, new Callable<List<AccountabilityContact>>() {
      @Override
      @NonNull
      public List<AccountabilityContact> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfName = 1;
          final int _cursorIndexOfRelationship = 2;
          final int _cursorIndexOfContactMethod = 3;
          final int _cursorIndexOfContactInfo = 4;
          final int _cursorIndexOfIsActive = 5;
          final int _cursorIndexOfPermissionLevel = 6;
          final int _cursorIndexOfSharingPreferences = 7;
          final int _cursorIndexOfAddedDate = 8;
          final int _cursorIndexOfLastContactDate = 9;
          final int _cursorIndexOfTotalInteractions = 10;
          final int _cursorIndexOfSuccessfulInterventions = 11;
          final int _cursorIndexOfTrustLevel = 12;
          final int _cursorIndexOfResponseRate = 13;
          final int _cursorIndexOfAverageResponseTime = 14;
          final int _cursorIndexOfPreferredContactTime = 15;
          final int _cursorIndexOfTimeZone = 16;
          final int _cursorIndexOfNotificationFrequency = 17;
          final int _cursorIndexOfSupportType = 18;
          final int _cursorIndexOfSpecializations = 19;
          final int _cursorIndexOfNotes = 20;
          final int _cursorIndexOfEmergencyContact = 21;
          final int _cursorIndexOfCanReceiveSpendingAlerts = 22;
          final int _cursorIndexOfCanReceiveBudgetUpdates = 23;
          final int _cursorIndexOfCanReceiveGoalProgress = 24;
          final int _cursorIndexOfCanReceiveEmergencyAlerts = 25;
          final int _cursorIndexOfMutualAccountability = 26;
          final int _cursorIndexOfPartnerUserId = 27;
          final int _cursorIndexOfConsentGiven = 28;
          final int _cursorIndexOfConsentDate = 29;
          final int _cursorIndexOfLastConsentUpdate = 30;
          final int _cursorIndexOfPrivacyLevel = 31;
          final List<AccountabilityContact> _result = new ArrayList<AccountabilityContact>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AccountabilityContact _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpRelationship;
            if (_cursor.isNull(_cursorIndexOfRelationship)) {
              _tmpRelationship = null;
            } else {
              _tmpRelationship = _cursor.getString(_cursorIndexOfRelationship);
            }
            final String _tmpContactMethod;
            if (_cursor.isNull(_cursorIndexOfContactMethod)) {
              _tmpContactMethod = null;
            } else {
              _tmpContactMethod = _cursor.getString(_cursorIndexOfContactMethod);
            }
            final String _tmpContactInfo;
            if (_cursor.isNull(_cursorIndexOfContactInfo)) {
              _tmpContactInfo = null;
            } else {
              _tmpContactInfo = _cursor.getString(_cursorIndexOfContactInfo);
            }
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            final String _tmpPermissionLevel;
            if (_cursor.isNull(_cursorIndexOfPermissionLevel)) {
              _tmpPermissionLevel = null;
            } else {
              _tmpPermissionLevel = _cursor.getString(_cursorIndexOfPermissionLevel);
            }
            final String _tmpSharingPreferences;
            if (_cursor.isNull(_cursorIndexOfSharingPreferences)) {
              _tmpSharingPreferences = null;
            } else {
              _tmpSharingPreferences = _cursor.getString(_cursorIndexOfSharingPreferences);
            }
            final LocalDateTime _tmpAddedDate;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfAddedDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfAddedDate);
            }
            _tmpAddedDate = __converters.toLocalDateTime(_tmp_1);
            final LocalDateTime _tmpLastContactDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfLastContactDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfLastContactDate);
            }
            _tmpLastContactDate = __converters.toLocalDateTime(_tmp_2);
            final int _tmpTotalInteractions;
            _tmpTotalInteractions = _cursor.getInt(_cursorIndexOfTotalInteractions);
            final int _tmpSuccessfulInterventions;
            _tmpSuccessfulInterventions = _cursor.getInt(_cursorIndexOfSuccessfulInterventions);
            final int _tmpTrustLevel;
            _tmpTrustLevel = _cursor.getInt(_cursorIndexOfTrustLevel);
            final double _tmpResponseRate;
            _tmpResponseRate = _cursor.getDouble(_cursorIndexOfResponseRate);
            final Integer _tmpAverageResponseTime;
            if (_cursor.isNull(_cursorIndexOfAverageResponseTime)) {
              _tmpAverageResponseTime = null;
            } else {
              _tmpAverageResponseTime = _cursor.getInt(_cursorIndexOfAverageResponseTime);
            }
            final String _tmpPreferredContactTime;
            if (_cursor.isNull(_cursorIndexOfPreferredContactTime)) {
              _tmpPreferredContactTime = null;
            } else {
              _tmpPreferredContactTime = _cursor.getString(_cursorIndexOfPreferredContactTime);
            }
            final String _tmpTimeZone;
            if (_cursor.isNull(_cursorIndexOfTimeZone)) {
              _tmpTimeZone = null;
            } else {
              _tmpTimeZone = _cursor.getString(_cursorIndexOfTimeZone);
            }
            final String _tmpNotificationFrequency;
            if (_cursor.isNull(_cursorIndexOfNotificationFrequency)) {
              _tmpNotificationFrequency = null;
            } else {
              _tmpNotificationFrequency = _cursor.getString(_cursorIndexOfNotificationFrequency);
            }
            final String _tmpSupportType;
            if (_cursor.isNull(_cursorIndexOfSupportType)) {
              _tmpSupportType = null;
            } else {
              _tmpSupportType = _cursor.getString(_cursorIndexOfSupportType);
            }
            final String _tmpSpecializations;
            if (_cursor.isNull(_cursorIndexOfSpecializations)) {
              _tmpSpecializations = null;
            } else {
              _tmpSpecializations = _cursor.getString(_cursorIndexOfSpecializations);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final boolean _tmpEmergencyContact;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfEmergencyContact);
            _tmpEmergencyContact = _tmp_3 != 0;
            final boolean _tmpCanReceiveSpendingAlerts;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfCanReceiveSpendingAlerts);
            _tmpCanReceiveSpendingAlerts = _tmp_4 != 0;
            final boolean _tmpCanReceiveBudgetUpdates;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfCanReceiveBudgetUpdates);
            _tmpCanReceiveBudgetUpdates = _tmp_5 != 0;
            final boolean _tmpCanReceiveGoalProgress;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfCanReceiveGoalProgress);
            _tmpCanReceiveGoalProgress = _tmp_6 != 0;
            final boolean _tmpCanReceiveEmergencyAlerts;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfCanReceiveEmergencyAlerts);
            _tmpCanReceiveEmergencyAlerts = _tmp_7 != 0;
            final boolean _tmpMutualAccountability;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfMutualAccountability);
            _tmpMutualAccountability = _tmp_8 != 0;
            final String _tmpPartnerUserId;
            if (_cursor.isNull(_cursorIndexOfPartnerUserId)) {
              _tmpPartnerUserId = null;
            } else {
              _tmpPartnerUserId = _cursor.getString(_cursorIndexOfPartnerUserId);
            }
            final boolean _tmpConsentGiven;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfConsentGiven);
            _tmpConsentGiven = _tmp_9 != 0;
            final LocalDateTime _tmpConsentDate;
            final String _tmp_10;
            if (_cursor.isNull(_cursorIndexOfConsentDate)) {
              _tmp_10 = null;
            } else {
              _tmp_10 = _cursor.getString(_cursorIndexOfConsentDate);
            }
            _tmpConsentDate = __converters.toLocalDateTime(_tmp_10);
            final LocalDateTime _tmpLastConsentUpdate;
            final String _tmp_11;
            if (_cursor.isNull(_cursorIndexOfLastConsentUpdate)) {
              _tmp_11 = null;
            } else {
              _tmp_11 = _cursor.getString(_cursorIndexOfLastConsentUpdate);
            }
            _tmpLastConsentUpdate = __converters.toLocalDateTime(_tmp_11);
            final String _tmpPrivacyLevel;
            if (_cursor.isNull(_cursorIndexOfPrivacyLevel)) {
              _tmpPrivacyLevel = null;
            } else {
              _tmpPrivacyLevel = _cursor.getString(_cursorIndexOfPrivacyLevel);
            }
            _item = new AccountabilityContact(_tmpId,_tmpName,_tmpRelationship,_tmpContactMethod,_tmpContactInfo,_tmpIsActive,_tmpPermissionLevel,_tmpSharingPreferences,_tmpAddedDate,_tmpLastContactDate,_tmpTotalInteractions,_tmpSuccessfulInterventions,_tmpTrustLevel,_tmpResponseRate,_tmpAverageResponseTime,_tmpPreferredContactTime,_tmpTimeZone,_tmpNotificationFrequency,_tmpSupportType,_tmpSpecializations,_tmpNotes,_tmpEmergencyContact,_tmpCanReceiveSpendingAlerts,_tmpCanReceiveBudgetUpdates,_tmpCanReceiveGoalProgress,_tmpCanReceiveEmergencyAlerts,_tmpMutualAccountability,_tmpPartnerUserId,_tmpConsentGiven,_tmpConsentDate,_tmpLastConsentUpdate,_tmpPrivacyLevel);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<AccountabilityContact>> getContactsByRelationship(final String relationship) {
    final String _sql = "SELECT * FROM accountability_contacts WHERE relationship = ? AND isActive = 1 ORDER BY trustLevel DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (relationship == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, relationship);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"accountability_contacts"}, new Callable<List<AccountabilityContact>>() {
      @Override
      @NonNull
      public List<AccountabilityContact> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfRelationship = CursorUtil.getColumnIndexOrThrow(_cursor, "relationship");
          final int _cursorIndexOfContactMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "contactMethod");
          final int _cursorIndexOfContactInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "contactInfo");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfPermissionLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "permissionLevel");
          final int _cursorIndexOfSharingPreferences = CursorUtil.getColumnIndexOrThrow(_cursor, "sharingPreferences");
          final int _cursorIndexOfAddedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "addedDate");
          final int _cursorIndexOfLastContactDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastContactDate");
          final int _cursorIndexOfTotalInteractions = CursorUtil.getColumnIndexOrThrow(_cursor, "totalInteractions");
          final int _cursorIndexOfSuccessfulInterventions = CursorUtil.getColumnIndexOrThrow(_cursor, "successfulInterventions");
          final int _cursorIndexOfTrustLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "trustLevel");
          final int _cursorIndexOfResponseRate = CursorUtil.getColumnIndexOrThrow(_cursor, "responseRate");
          final int _cursorIndexOfAverageResponseTime = CursorUtil.getColumnIndexOrThrow(_cursor, "averageResponseTime");
          final int _cursorIndexOfPreferredContactTime = CursorUtil.getColumnIndexOrThrow(_cursor, "preferredContactTime");
          final int _cursorIndexOfTimeZone = CursorUtil.getColumnIndexOrThrow(_cursor, "timeZone");
          final int _cursorIndexOfNotificationFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "notificationFrequency");
          final int _cursorIndexOfSupportType = CursorUtil.getColumnIndexOrThrow(_cursor, "supportType");
          final int _cursorIndexOfSpecializations = CursorUtil.getColumnIndexOrThrow(_cursor, "specializations");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfEmergencyContact = CursorUtil.getColumnIndexOrThrow(_cursor, "emergencyContact");
          final int _cursorIndexOfCanReceiveSpendingAlerts = CursorUtil.getColumnIndexOrThrow(_cursor, "canReceiveSpendingAlerts");
          final int _cursorIndexOfCanReceiveBudgetUpdates = CursorUtil.getColumnIndexOrThrow(_cursor, "canReceiveBudgetUpdates");
          final int _cursorIndexOfCanReceiveGoalProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "canReceiveGoalProgress");
          final int _cursorIndexOfCanReceiveEmergencyAlerts = CursorUtil.getColumnIndexOrThrow(_cursor, "canReceiveEmergencyAlerts");
          final int _cursorIndexOfMutualAccountability = CursorUtil.getColumnIndexOrThrow(_cursor, "mutualAccountability");
          final int _cursorIndexOfPartnerUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "partnerUserId");
          final int _cursorIndexOfConsentGiven = CursorUtil.getColumnIndexOrThrow(_cursor, "consentGiven");
          final int _cursorIndexOfConsentDate = CursorUtil.getColumnIndexOrThrow(_cursor, "consentDate");
          final int _cursorIndexOfLastConsentUpdate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastConsentUpdate");
          final int _cursorIndexOfPrivacyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "privacyLevel");
          final List<AccountabilityContact> _result = new ArrayList<AccountabilityContact>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AccountabilityContact _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpRelationship;
            if (_cursor.isNull(_cursorIndexOfRelationship)) {
              _tmpRelationship = null;
            } else {
              _tmpRelationship = _cursor.getString(_cursorIndexOfRelationship);
            }
            final String _tmpContactMethod;
            if (_cursor.isNull(_cursorIndexOfContactMethod)) {
              _tmpContactMethod = null;
            } else {
              _tmpContactMethod = _cursor.getString(_cursorIndexOfContactMethod);
            }
            final String _tmpContactInfo;
            if (_cursor.isNull(_cursorIndexOfContactInfo)) {
              _tmpContactInfo = null;
            } else {
              _tmpContactInfo = _cursor.getString(_cursorIndexOfContactInfo);
            }
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            final String _tmpPermissionLevel;
            if (_cursor.isNull(_cursorIndexOfPermissionLevel)) {
              _tmpPermissionLevel = null;
            } else {
              _tmpPermissionLevel = _cursor.getString(_cursorIndexOfPermissionLevel);
            }
            final String _tmpSharingPreferences;
            if (_cursor.isNull(_cursorIndexOfSharingPreferences)) {
              _tmpSharingPreferences = null;
            } else {
              _tmpSharingPreferences = _cursor.getString(_cursorIndexOfSharingPreferences);
            }
            final LocalDateTime _tmpAddedDate;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfAddedDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfAddedDate);
            }
            _tmpAddedDate = __converters.toLocalDateTime(_tmp_1);
            final LocalDateTime _tmpLastContactDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfLastContactDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfLastContactDate);
            }
            _tmpLastContactDate = __converters.toLocalDateTime(_tmp_2);
            final int _tmpTotalInteractions;
            _tmpTotalInteractions = _cursor.getInt(_cursorIndexOfTotalInteractions);
            final int _tmpSuccessfulInterventions;
            _tmpSuccessfulInterventions = _cursor.getInt(_cursorIndexOfSuccessfulInterventions);
            final int _tmpTrustLevel;
            _tmpTrustLevel = _cursor.getInt(_cursorIndexOfTrustLevel);
            final double _tmpResponseRate;
            _tmpResponseRate = _cursor.getDouble(_cursorIndexOfResponseRate);
            final Integer _tmpAverageResponseTime;
            if (_cursor.isNull(_cursorIndexOfAverageResponseTime)) {
              _tmpAverageResponseTime = null;
            } else {
              _tmpAverageResponseTime = _cursor.getInt(_cursorIndexOfAverageResponseTime);
            }
            final String _tmpPreferredContactTime;
            if (_cursor.isNull(_cursorIndexOfPreferredContactTime)) {
              _tmpPreferredContactTime = null;
            } else {
              _tmpPreferredContactTime = _cursor.getString(_cursorIndexOfPreferredContactTime);
            }
            final String _tmpTimeZone;
            if (_cursor.isNull(_cursorIndexOfTimeZone)) {
              _tmpTimeZone = null;
            } else {
              _tmpTimeZone = _cursor.getString(_cursorIndexOfTimeZone);
            }
            final String _tmpNotificationFrequency;
            if (_cursor.isNull(_cursorIndexOfNotificationFrequency)) {
              _tmpNotificationFrequency = null;
            } else {
              _tmpNotificationFrequency = _cursor.getString(_cursorIndexOfNotificationFrequency);
            }
            final String _tmpSupportType;
            if (_cursor.isNull(_cursorIndexOfSupportType)) {
              _tmpSupportType = null;
            } else {
              _tmpSupportType = _cursor.getString(_cursorIndexOfSupportType);
            }
            final String _tmpSpecializations;
            if (_cursor.isNull(_cursorIndexOfSpecializations)) {
              _tmpSpecializations = null;
            } else {
              _tmpSpecializations = _cursor.getString(_cursorIndexOfSpecializations);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final boolean _tmpEmergencyContact;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfEmergencyContact);
            _tmpEmergencyContact = _tmp_3 != 0;
            final boolean _tmpCanReceiveSpendingAlerts;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfCanReceiveSpendingAlerts);
            _tmpCanReceiveSpendingAlerts = _tmp_4 != 0;
            final boolean _tmpCanReceiveBudgetUpdates;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfCanReceiveBudgetUpdates);
            _tmpCanReceiveBudgetUpdates = _tmp_5 != 0;
            final boolean _tmpCanReceiveGoalProgress;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfCanReceiveGoalProgress);
            _tmpCanReceiveGoalProgress = _tmp_6 != 0;
            final boolean _tmpCanReceiveEmergencyAlerts;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfCanReceiveEmergencyAlerts);
            _tmpCanReceiveEmergencyAlerts = _tmp_7 != 0;
            final boolean _tmpMutualAccountability;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfMutualAccountability);
            _tmpMutualAccountability = _tmp_8 != 0;
            final String _tmpPartnerUserId;
            if (_cursor.isNull(_cursorIndexOfPartnerUserId)) {
              _tmpPartnerUserId = null;
            } else {
              _tmpPartnerUserId = _cursor.getString(_cursorIndexOfPartnerUserId);
            }
            final boolean _tmpConsentGiven;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfConsentGiven);
            _tmpConsentGiven = _tmp_9 != 0;
            final LocalDateTime _tmpConsentDate;
            final String _tmp_10;
            if (_cursor.isNull(_cursorIndexOfConsentDate)) {
              _tmp_10 = null;
            } else {
              _tmp_10 = _cursor.getString(_cursorIndexOfConsentDate);
            }
            _tmpConsentDate = __converters.toLocalDateTime(_tmp_10);
            final LocalDateTime _tmpLastConsentUpdate;
            final String _tmp_11;
            if (_cursor.isNull(_cursorIndexOfLastConsentUpdate)) {
              _tmp_11 = null;
            } else {
              _tmp_11 = _cursor.getString(_cursorIndexOfLastConsentUpdate);
            }
            _tmpLastConsentUpdate = __converters.toLocalDateTime(_tmp_11);
            final String _tmpPrivacyLevel;
            if (_cursor.isNull(_cursorIndexOfPrivacyLevel)) {
              _tmpPrivacyLevel = null;
            } else {
              _tmpPrivacyLevel = _cursor.getString(_cursorIndexOfPrivacyLevel);
            }
            _item = new AccountabilityContact(_tmpId,_tmpName,_tmpRelationship,_tmpContactMethod,_tmpContactInfo,_tmpIsActive,_tmpPermissionLevel,_tmpSharingPreferences,_tmpAddedDate,_tmpLastContactDate,_tmpTotalInteractions,_tmpSuccessfulInterventions,_tmpTrustLevel,_tmpResponseRate,_tmpAverageResponseTime,_tmpPreferredContactTime,_tmpTimeZone,_tmpNotificationFrequency,_tmpSupportType,_tmpSpecializations,_tmpNotes,_tmpEmergencyContact,_tmpCanReceiveSpendingAlerts,_tmpCanReceiveBudgetUpdates,_tmpCanReceiveGoalProgress,_tmpCanReceiveEmergencyAlerts,_tmpMutualAccountability,_tmpPartnerUserId,_tmpConsentGiven,_tmpConsentDate,_tmpLastConsentUpdate,_tmpPrivacyLevel);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<AccountabilityContact>> getEmergencyContacts() {
    final String _sql = "SELECT `accountability_contacts`.`id` AS `id`, `accountability_contacts`.`name` AS `name`, `accountability_contacts`.`relationship` AS `relationship`, `accountability_contacts`.`contactMethod` AS `contactMethod`, `accountability_contacts`.`contactInfo` AS `contactInfo`, `accountability_contacts`.`isActive` AS `isActive`, `accountability_contacts`.`permissionLevel` AS `permissionLevel`, `accountability_contacts`.`sharingPreferences` AS `sharingPreferences`, `accountability_contacts`.`addedDate` AS `addedDate`, `accountability_contacts`.`lastContactDate` AS `lastContactDate`, `accountability_contacts`.`totalInteractions` AS `totalInteractions`, `accountability_contacts`.`successfulInterventions` AS `successfulInterventions`, `accountability_contacts`.`trustLevel` AS `trustLevel`, `accountability_contacts`.`responseRate` AS `responseRate`, `accountability_contacts`.`averageResponseTime` AS `averageResponseTime`, `accountability_contacts`.`preferredContactTime` AS `preferredContactTime`, `accountability_contacts`.`timeZone` AS `timeZone`, `accountability_contacts`.`notificationFrequency` AS `notificationFrequency`, `accountability_contacts`.`supportType` AS `supportType`, `accountability_contacts`.`specializations` AS `specializations`, `accountability_contacts`.`notes` AS `notes`, `accountability_contacts`.`emergencyContact` AS `emergencyContact`, `accountability_contacts`.`canReceiveSpendingAlerts` AS `canReceiveSpendingAlerts`, `accountability_contacts`.`canReceiveBudgetUpdates` AS `canReceiveBudgetUpdates`, `accountability_contacts`.`canReceiveGoalProgress` AS `canReceiveGoalProgress`, `accountability_contacts`.`canReceiveEmergencyAlerts` AS `canReceiveEmergencyAlerts`, `accountability_contacts`.`mutualAccountability` AS `mutualAccountability`, `accountability_contacts`.`partnerUserId` AS `partnerUserId`, `accountability_contacts`.`consentGiven` AS `consentGiven`, `accountability_contacts`.`consentDate` AS `consentDate`, `accountability_contacts`.`lastConsentUpdate` AS `lastConsentUpdate`, `accountability_contacts`.`privacyLevel` AS `privacyLevel` FROM accountability_contacts WHERE emergencyContact = 1 AND isActive = 1 ORDER BY trustLevel DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"accountability_contacts"}, new Callable<List<AccountabilityContact>>() {
      @Override
      @NonNull
      public List<AccountabilityContact> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfName = 1;
          final int _cursorIndexOfRelationship = 2;
          final int _cursorIndexOfContactMethod = 3;
          final int _cursorIndexOfContactInfo = 4;
          final int _cursorIndexOfIsActive = 5;
          final int _cursorIndexOfPermissionLevel = 6;
          final int _cursorIndexOfSharingPreferences = 7;
          final int _cursorIndexOfAddedDate = 8;
          final int _cursorIndexOfLastContactDate = 9;
          final int _cursorIndexOfTotalInteractions = 10;
          final int _cursorIndexOfSuccessfulInterventions = 11;
          final int _cursorIndexOfTrustLevel = 12;
          final int _cursorIndexOfResponseRate = 13;
          final int _cursorIndexOfAverageResponseTime = 14;
          final int _cursorIndexOfPreferredContactTime = 15;
          final int _cursorIndexOfTimeZone = 16;
          final int _cursorIndexOfNotificationFrequency = 17;
          final int _cursorIndexOfSupportType = 18;
          final int _cursorIndexOfSpecializations = 19;
          final int _cursorIndexOfNotes = 20;
          final int _cursorIndexOfEmergencyContact = 21;
          final int _cursorIndexOfCanReceiveSpendingAlerts = 22;
          final int _cursorIndexOfCanReceiveBudgetUpdates = 23;
          final int _cursorIndexOfCanReceiveGoalProgress = 24;
          final int _cursorIndexOfCanReceiveEmergencyAlerts = 25;
          final int _cursorIndexOfMutualAccountability = 26;
          final int _cursorIndexOfPartnerUserId = 27;
          final int _cursorIndexOfConsentGiven = 28;
          final int _cursorIndexOfConsentDate = 29;
          final int _cursorIndexOfLastConsentUpdate = 30;
          final int _cursorIndexOfPrivacyLevel = 31;
          final List<AccountabilityContact> _result = new ArrayList<AccountabilityContact>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AccountabilityContact _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpRelationship;
            if (_cursor.isNull(_cursorIndexOfRelationship)) {
              _tmpRelationship = null;
            } else {
              _tmpRelationship = _cursor.getString(_cursorIndexOfRelationship);
            }
            final String _tmpContactMethod;
            if (_cursor.isNull(_cursorIndexOfContactMethod)) {
              _tmpContactMethod = null;
            } else {
              _tmpContactMethod = _cursor.getString(_cursorIndexOfContactMethod);
            }
            final String _tmpContactInfo;
            if (_cursor.isNull(_cursorIndexOfContactInfo)) {
              _tmpContactInfo = null;
            } else {
              _tmpContactInfo = _cursor.getString(_cursorIndexOfContactInfo);
            }
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            final String _tmpPermissionLevel;
            if (_cursor.isNull(_cursorIndexOfPermissionLevel)) {
              _tmpPermissionLevel = null;
            } else {
              _tmpPermissionLevel = _cursor.getString(_cursorIndexOfPermissionLevel);
            }
            final String _tmpSharingPreferences;
            if (_cursor.isNull(_cursorIndexOfSharingPreferences)) {
              _tmpSharingPreferences = null;
            } else {
              _tmpSharingPreferences = _cursor.getString(_cursorIndexOfSharingPreferences);
            }
            final LocalDateTime _tmpAddedDate;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfAddedDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfAddedDate);
            }
            _tmpAddedDate = __converters.toLocalDateTime(_tmp_1);
            final LocalDateTime _tmpLastContactDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfLastContactDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfLastContactDate);
            }
            _tmpLastContactDate = __converters.toLocalDateTime(_tmp_2);
            final int _tmpTotalInteractions;
            _tmpTotalInteractions = _cursor.getInt(_cursorIndexOfTotalInteractions);
            final int _tmpSuccessfulInterventions;
            _tmpSuccessfulInterventions = _cursor.getInt(_cursorIndexOfSuccessfulInterventions);
            final int _tmpTrustLevel;
            _tmpTrustLevel = _cursor.getInt(_cursorIndexOfTrustLevel);
            final double _tmpResponseRate;
            _tmpResponseRate = _cursor.getDouble(_cursorIndexOfResponseRate);
            final Integer _tmpAverageResponseTime;
            if (_cursor.isNull(_cursorIndexOfAverageResponseTime)) {
              _tmpAverageResponseTime = null;
            } else {
              _tmpAverageResponseTime = _cursor.getInt(_cursorIndexOfAverageResponseTime);
            }
            final String _tmpPreferredContactTime;
            if (_cursor.isNull(_cursorIndexOfPreferredContactTime)) {
              _tmpPreferredContactTime = null;
            } else {
              _tmpPreferredContactTime = _cursor.getString(_cursorIndexOfPreferredContactTime);
            }
            final String _tmpTimeZone;
            if (_cursor.isNull(_cursorIndexOfTimeZone)) {
              _tmpTimeZone = null;
            } else {
              _tmpTimeZone = _cursor.getString(_cursorIndexOfTimeZone);
            }
            final String _tmpNotificationFrequency;
            if (_cursor.isNull(_cursorIndexOfNotificationFrequency)) {
              _tmpNotificationFrequency = null;
            } else {
              _tmpNotificationFrequency = _cursor.getString(_cursorIndexOfNotificationFrequency);
            }
            final String _tmpSupportType;
            if (_cursor.isNull(_cursorIndexOfSupportType)) {
              _tmpSupportType = null;
            } else {
              _tmpSupportType = _cursor.getString(_cursorIndexOfSupportType);
            }
            final String _tmpSpecializations;
            if (_cursor.isNull(_cursorIndexOfSpecializations)) {
              _tmpSpecializations = null;
            } else {
              _tmpSpecializations = _cursor.getString(_cursorIndexOfSpecializations);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final boolean _tmpEmergencyContact;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfEmergencyContact);
            _tmpEmergencyContact = _tmp_3 != 0;
            final boolean _tmpCanReceiveSpendingAlerts;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfCanReceiveSpendingAlerts);
            _tmpCanReceiveSpendingAlerts = _tmp_4 != 0;
            final boolean _tmpCanReceiveBudgetUpdates;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfCanReceiveBudgetUpdates);
            _tmpCanReceiveBudgetUpdates = _tmp_5 != 0;
            final boolean _tmpCanReceiveGoalProgress;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfCanReceiveGoalProgress);
            _tmpCanReceiveGoalProgress = _tmp_6 != 0;
            final boolean _tmpCanReceiveEmergencyAlerts;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfCanReceiveEmergencyAlerts);
            _tmpCanReceiveEmergencyAlerts = _tmp_7 != 0;
            final boolean _tmpMutualAccountability;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfMutualAccountability);
            _tmpMutualAccountability = _tmp_8 != 0;
            final String _tmpPartnerUserId;
            if (_cursor.isNull(_cursorIndexOfPartnerUserId)) {
              _tmpPartnerUserId = null;
            } else {
              _tmpPartnerUserId = _cursor.getString(_cursorIndexOfPartnerUserId);
            }
            final boolean _tmpConsentGiven;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfConsentGiven);
            _tmpConsentGiven = _tmp_9 != 0;
            final LocalDateTime _tmpConsentDate;
            final String _tmp_10;
            if (_cursor.isNull(_cursorIndexOfConsentDate)) {
              _tmp_10 = null;
            } else {
              _tmp_10 = _cursor.getString(_cursorIndexOfConsentDate);
            }
            _tmpConsentDate = __converters.toLocalDateTime(_tmp_10);
            final LocalDateTime _tmpLastConsentUpdate;
            final String _tmp_11;
            if (_cursor.isNull(_cursorIndexOfLastConsentUpdate)) {
              _tmp_11 = null;
            } else {
              _tmp_11 = _cursor.getString(_cursorIndexOfLastConsentUpdate);
            }
            _tmpLastConsentUpdate = __converters.toLocalDateTime(_tmp_11);
            final String _tmpPrivacyLevel;
            if (_cursor.isNull(_cursorIndexOfPrivacyLevel)) {
              _tmpPrivacyLevel = null;
            } else {
              _tmpPrivacyLevel = _cursor.getString(_cursorIndexOfPrivacyLevel);
            }
            _item = new AccountabilityContact(_tmpId,_tmpName,_tmpRelationship,_tmpContactMethod,_tmpContactInfo,_tmpIsActive,_tmpPermissionLevel,_tmpSharingPreferences,_tmpAddedDate,_tmpLastContactDate,_tmpTotalInteractions,_tmpSuccessfulInterventions,_tmpTrustLevel,_tmpResponseRate,_tmpAverageResponseTime,_tmpPreferredContactTime,_tmpTimeZone,_tmpNotificationFrequency,_tmpSupportType,_tmpSpecializations,_tmpNotes,_tmpEmergencyContact,_tmpCanReceiveSpendingAlerts,_tmpCanReceiveBudgetUpdates,_tmpCanReceiveGoalProgress,_tmpCanReceiveEmergencyAlerts,_tmpMutualAccountability,_tmpPartnerUserId,_tmpConsentGiven,_tmpConsentDate,_tmpLastConsentUpdate,_tmpPrivacyLevel);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<AccountabilityContact>> getSpendingAlertContacts() {
    final String _sql = "SELECT `accountability_contacts`.`id` AS `id`, `accountability_contacts`.`name` AS `name`, `accountability_contacts`.`relationship` AS `relationship`, `accountability_contacts`.`contactMethod` AS `contactMethod`, `accountability_contacts`.`contactInfo` AS `contactInfo`, `accountability_contacts`.`isActive` AS `isActive`, `accountability_contacts`.`permissionLevel` AS `permissionLevel`, `accountability_contacts`.`sharingPreferences` AS `sharingPreferences`, `accountability_contacts`.`addedDate` AS `addedDate`, `accountability_contacts`.`lastContactDate` AS `lastContactDate`, `accountability_contacts`.`totalInteractions` AS `totalInteractions`, `accountability_contacts`.`successfulInterventions` AS `successfulInterventions`, `accountability_contacts`.`trustLevel` AS `trustLevel`, `accountability_contacts`.`responseRate` AS `responseRate`, `accountability_contacts`.`averageResponseTime` AS `averageResponseTime`, `accountability_contacts`.`preferredContactTime` AS `preferredContactTime`, `accountability_contacts`.`timeZone` AS `timeZone`, `accountability_contacts`.`notificationFrequency` AS `notificationFrequency`, `accountability_contacts`.`supportType` AS `supportType`, `accountability_contacts`.`specializations` AS `specializations`, `accountability_contacts`.`notes` AS `notes`, `accountability_contacts`.`emergencyContact` AS `emergencyContact`, `accountability_contacts`.`canReceiveSpendingAlerts` AS `canReceiveSpendingAlerts`, `accountability_contacts`.`canReceiveBudgetUpdates` AS `canReceiveBudgetUpdates`, `accountability_contacts`.`canReceiveGoalProgress` AS `canReceiveGoalProgress`, `accountability_contacts`.`canReceiveEmergencyAlerts` AS `canReceiveEmergencyAlerts`, `accountability_contacts`.`mutualAccountability` AS `mutualAccountability`, `accountability_contacts`.`partnerUserId` AS `partnerUserId`, `accountability_contacts`.`consentGiven` AS `consentGiven`, `accountability_contacts`.`consentDate` AS `consentDate`, `accountability_contacts`.`lastConsentUpdate` AS `lastConsentUpdate`, `accountability_contacts`.`privacyLevel` AS `privacyLevel` FROM accountability_contacts WHERE canReceiveSpendingAlerts = 1 AND isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"accountability_contacts"}, new Callable<List<AccountabilityContact>>() {
      @Override
      @NonNull
      public List<AccountabilityContact> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfName = 1;
          final int _cursorIndexOfRelationship = 2;
          final int _cursorIndexOfContactMethod = 3;
          final int _cursorIndexOfContactInfo = 4;
          final int _cursorIndexOfIsActive = 5;
          final int _cursorIndexOfPermissionLevel = 6;
          final int _cursorIndexOfSharingPreferences = 7;
          final int _cursorIndexOfAddedDate = 8;
          final int _cursorIndexOfLastContactDate = 9;
          final int _cursorIndexOfTotalInteractions = 10;
          final int _cursorIndexOfSuccessfulInterventions = 11;
          final int _cursorIndexOfTrustLevel = 12;
          final int _cursorIndexOfResponseRate = 13;
          final int _cursorIndexOfAverageResponseTime = 14;
          final int _cursorIndexOfPreferredContactTime = 15;
          final int _cursorIndexOfTimeZone = 16;
          final int _cursorIndexOfNotificationFrequency = 17;
          final int _cursorIndexOfSupportType = 18;
          final int _cursorIndexOfSpecializations = 19;
          final int _cursorIndexOfNotes = 20;
          final int _cursorIndexOfEmergencyContact = 21;
          final int _cursorIndexOfCanReceiveSpendingAlerts = 22;
          final int _cursorIndexOfCanReceiveBudgetUpdates = 23;
          final int _cursorIndexOfCanReceiveGoalProgress = 24;
          final int _cursorIndexOfCanReceiveEmergencyAlerts = 25;
          final int _cursorIndexOfMutualAccountability = 26;
          final int _cursorIndexOfPartnerUserId = 27;
          final int _cursorIndexOfConsentGiven = 28;
          final int _cursorIndexOfConsentDate = 29;
          final int _cursorIndexOfLastConsentUpdate = 30;
          final int _cursorIndexOfPrivacyLevel = 31;
          final List<AccountabilityContact> _result = new ArrayList<AccountabilityContact>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AccountabilityContact _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpRelationship;
            if (_cursor.isNull(_cursorIndexOfRelationship)) {
              _tmpRelationship = null;
            } else {
              _tmpRelationship = _cursor.getString(_cursorIndexOfRelationship);
            }
            final String _tmpContactMethod;
            if (_cursor.isNull(_cursorIndexOfContactMethod)) {
              _tmpContactMethod = null;
            } else {
              _tmpContactMethod = _cursor.getString(_cursorIndexOfContactMethod);
            }
            final String _tmpContactInfo;
            if (_cursor.isNull(_cursorIndexOfContactInfo)) {
              _tmpContactInfo = null;
            } else {
              _tmpContactInfo = _cursor.getString(_cursorIndexOfContactInfo);
            }
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            final String _tmpPermissionLevel;
            if (_cursor.isNull(_cursorIndexOfPermissionLevel)) {
              _tmpPermissionLevel = null;
            } else {
              _tmpPermissionLevel = _cursor.getString(_cursorIndexOfPermissionLevel);
            }
            final String _tmpSharingPreferences;
            if (_cursor.isNull(_cursorIndexOfSharingPreferences)) {
              _tmpSharingPreferences = null;
            } else {
              _tmpSharingPreferences = _cursor.getString(_cursorIndexOfSharingPreferences);
            }
            final LocalDateTime _tmpAddedDate;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfAddedDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfAddedDate);
            }
            _tmpAddedDate = __converters.toLocalDateTime(_tmp_1);
            final LocalDateTime _tmpLastContactDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfLastContactDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfLastContactDate);
            }
            _tmpLastContactDate = __converters.toLocalDateTime(_tmp_2);
            final int _tmpTotalInteractions;
            _tmpTotalInteractions = _cursor.getInt(_cursorIndexOfTotalInteractions);
            final int _tmpSuccessfulInterventions;
            _tmpSuccessfulInterventions = _cursor.getInt(_cursorIndexOfSuccessfulInterventions);
            final int _tmpTrustLevel;
            _tmpTrustLevel = _cursor.getInt(_cursorIndexOfTrustLevel);
            final double _tmpResponseRate;
            _tmpResponseRate = _cursor.getDouble(_cursorIndexOfResponseRate);
            final Integer _tmpAverageResponseTime;
            if (_cursor.isNull(_cursorIndexOfAverageResponseTime)) {
              _tmpAverageResponseTime = null;
            } else {
              _tmpAverageResponseTime = _cursor.getInt(_cursorIndexOfAverageResponseTime);
            }
            final String _tmpPreferredContactTime;
            if (_cursor.isNull(_cursorIndexOfPreferredContactTime)) {
              _tmpPreferredContactTime = null;
            } else {
              _tmpPreferredContactTime = _cursor.getString(_cursorIndexOfPreferredContactTime);
            }
            final String _tmpTimeZone;
            if (_cursor.isNull(_cursorIndexOfTimeZone)) {
              _tmpTimeZone = null;
            } else {
              _tmpTimeZone = _cursor.getString(_cursorIndexOfTimeZone);
            }
            final String _tmpNotificationFrequency;
            if (_cursor.isNull(_cursorIndexOfNotificationFrequency)) {
              _tmpNotificationFrequency = null;
            } else {
              _tmpNotificationFrequency = _cursor.getString(_cursorIndexOfNotificationFrequency);
            }
            final String _tmpSupportType;
            if (_cursor.isNull(_cursorIndexOfSupportType)) {
              _tmpSupportType = null;
            } else {
              _tmpSupportType = _cursor.getString(_cursorIndexOfSupportType);
            }
            final String _tmpSpecializations;
            if (_cursor.isNull(_cursorIndexOfSpecializations)) {
              _tmpSpecializations = null;
            } else {
              _tmpSpecializations = _cursor.getString(_cursorIndexOfSpecializations);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final boolean _tmpEmergencyContact;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfEmergencyContact);
            _tmpEmergencyContact = _tmp_3 != 0;
            final boolean _tmpCanReceiveSpendingAlerts;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfCanReceiveSpendingAlerts);
            _tmpCanReceiveSpendingAlerts = _tmp_4 != 0;
            final boolean _tmpCanReceiveBudgetUpdates;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfCanReceiveBudgetUpdates);
            _tmpCanReceiveBudgetUpdates = _tmp_5 != 0;
            final boolean _tmpCanReceiveGoalProgress;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfCanReceiveGoalProgress);
            _tmpCanReceiveGoalProgress = _tmp_6 != 0;
            final boolean _tmpCanReceiveEmergencyAlerts;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfCanReceiveEmergencyAlerts);
            _tmpCanReceiveEmergencyAlerts = _tmp_7 != 0;
            final boolean _tmpMutualAccountability;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfMutualAccountability);
            _tmpMutualAccountability = _tmp_8 != 0;
            final String _tmpPartnerUserId;
            if (_cursor.isNull(_cursorIndexOfPartnerUserId)) {
              _tmpPartnerUserId = null;
            } else {
              _tmpPartnerUserId = _cursor.getString(_cursorIndexOfPartnerUserId);
            }
            final boolean _tmpConsentGiven;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfConsentGiven);
            _tmpConsentGiven = _tmp_9 != 0;
            final LocalDateTime _tmpConsentDate;
            final String _tmp_10;
            if (_cursor.isNull(_cursorIndexOfConsentDate)) {
              _tmp_10 = null;
            } else {
              _tmp_10 = _cursor.getString(_cursorIndexOfConsentDate);
            }
            _tmpConsentDate = __converters.toLocalDateTime(_tmp_10);
            final LocalDateTime _tmpLastConsentUpdate;
            final String _tmp_11;
            if (_cursor.isNull(_cursorIndexOfLastConsentUpdate)) {
              _tmp_11 = null;
            } else {
              _tmp_11 = _cursor.getString(_cursorIndexOfLastConsentUpdate);
            }
            _tmpLastConsentUpdate = __converters.toLocalDateTime(_tmp_11);
            final String _tmpPrivacyLevel;
            if (_cursor.isNull(_cursorIndexOfPrivacyLevel)) {
              _tmpPrivacyLevel = null;
            } else {
              _tmpPrivacyLevel = _cursor.getString(_cursorIndexOfPrivacyLevel);
            }
            _item = new AccountabilityContact(_tmpId,_tmpName,_tmpRelationship,_tmpContactMethod,_tmpContactInfo,_tmpIsActive,_tmpPermissionLevel,_tmpSharingPreferences,_tmpAddedDate,_tmpLastContactDate,_tmpTotalInteractions,_tmpSuccessfulInterventions,_tmpTrustLevel,_tmpResponseRate,_tmpAverageResponseTime,_tmpPreferredContactTime,_tmpTimeZone,_tmpNotificationFrequency,_tmpSupportType,_tmpSpecializations,_tmpNotes,_tmpEmergencyContact,_tmpCanReceiveSpendingAlerts,_tmpCanReceiveBudgetUpdates,_tmpCanReceiveGoalProgress,_tmpCanReceiveEmergencyAlerts,_tmpMutualAccountability,_tmpPartnerUserId,_tmpConsentGiven,_tmpConsentDate,_tmpLastConsentUpdate,_tmpPrivacyLevel);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getContactById(final long id,
      final Continuation<? super AccountabilityContact> arg1) {
    final String _sql = "SELECT * FROM accountability_contacts WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<AccountabilityContact>() {
      @Override
      @Nullable
      public AccountabilityContact call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfRelationship = CursorUtil.getColumnIndexOrThrow(_cursor, "relationship");
          final int _cursorIndexOfContactMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "contactMethod");
          final int _cursorIndexOfContactInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "contactInfo");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfPermissionLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "permissionLevel");
          final int _cursorIndexOfSharingPreferences = CursorUtil.getColumnIndexOrThrow(_cursor, "sharingPreferences");
          final int _cursorIndexOfAddedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "addedDate");
          final int _cursorIndexOfLastContactDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastContactDate");
          final int _cursorIndexOfTotalInteractions = CursorUtil.getColumnIndexOrThrow(_cursor, "totalInteractions");
          final int _cursorIndexOfSuccessfulInterventions = CursorUtil.getColumnIndexOrThrow(_cursor, "successfulInterventions");
          final int _cursorIndexOfTrustLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "trustLevel");
          final int _cursorIndexOfResponseRate = CursorUtil.getColumnIndexOrThrow(_cursor, "responseRate");
          final int _cursorIndexOfAverageResponseTime = CursorUtil.getColumnIndexOrThrow(_cursor, "averageResponseTime");
          final int _cursorIndexOfPreferredContactTime = CursorUtil.getColumnIndexOrThrow(_cursor, "preferredContactTime");
          final int _cursorIndexOfTimeZone = CursorUtil.getColumnIndexOrThrow(_cursor, "timeZone");
          final int _cursorIndexOfNotificationFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "notificationFrequency");
          final int _cursorIndexOfSupportType = CursorUtil.getColumnIndexOrThrow(_cursor, "supportType");
          final int _cursorIndexOfSpecializations = CursorUtil.getColumnIndexOrThrow(_cursor, "specializations");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfEmergencyContact = CursorUtil.getColumnIndexOrThrow(_cursor, "emergencyContact");
          final int _cursorIndexOfCanReceiveSpendingAlerts = CursorUtil.getColumnIndexOrThrow(_cursor, "canReceiveSpendingAlerts");
          final int _cursorIndexOfCanReceiveBudgetUpdates = CursorUtil.getColumnIndexOrThrow(_cursor, "canReceiveBudgetUpdates");
          final int _cursorIndexOfCanReceiveGoalProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "canReceiveGoalProgress");
          final int _cursorIndexOfCanReceiveEmergencyAlerts = CursorUtil.getColumnIndexOrThrow(_cursor, "canReceiveEmergencyAlerts");
          final int _cursorIndexOfMutualAccountability = CursorUtil.getColumnIndexOrThrow(_cursor, "mutualAccountability");
          final int _cursorIndexOfPartnerUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "partnerUserId");
          final int _cursorIndexOfConsentGiven = CursorUtil.getColumnIndexOrThrow(_cursor, "consentGiven");
          final int _cursorIndexOfConsentDate = CursorUtil.getColumnIndexOrThrow(_cursor, "consentDate");
          final int _cursorIndexOfLastConsentUpdate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastConsentUpdate");
          final int _cursorIndexOfPrivacyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "privacyLevel");
          final AccountabilityContact _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpRelationship;
            if (_cursor.isNull(_cursorIndexOfRelationship)) {
              _tmpRelationship = null;
            } else {
              _tmpRelationship = _cursor.getString(_cursorIndexOfRelationship);
            }
            final String _tmpContactMethod;
            if (_cursor.isNull(_cursorIndexOfContactMethod)) {
              _tmpContactMethod = null;
            } else {
              _tmpContactMethod = _cursor.getString(_cursorIndexOfContactMethod);
            }
            final String _tmpContactInfo;
            if (_cursor.isNull(_cursorIndexOfContactInfo)) {
              _tmpContactInfo = null;
            } else {
              _tmpContactInfo = _cursor.getString(_cursorIndexOfContactInfo);
            }
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            final String _tmpPermissionLevel;
            if (_cursor.isNull(_cursorIndexOfPermissionLevel)) {
              _tmpPermissionLevel = null;
            } else {
              _tmpPermissionLevel = _cursor.getString(_cursorIndexOfPermissionLevel);
            }
            final String _tmpSharingPreferences;
            if (_cursor.isNull(_cursorIndexOfSharingPreferences)) {
              _tmpSharingPreferences = null;
            } else {
              _tmpSharingPreferences = _cursor.getString(_cursorIndexOfSharingPreferences);
            }
            final LocalDateTime _tmpAddedDate;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfAddedDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfAddedDate);
            }
            _tmpAddedDate = __converters.toLocalDateTime(_tmp_1);
            final LocalDateTime _tmpLastContactDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfLastContactDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfLastContactDate);
            }
            _tmpLastContactDate = __converters.toLocalDateTime(_tmp_2);
            final int _tmpTotalInteractions;
            _tmpTotalInteractions = _cursor.getInt(_cursorIndexOfTotalInteractions);
            final int _tmpSuccessfulInterventions;
            _tmpSuccessfulInterventions = _cursor.getInt(_cursorIndexOfSuccessfulInterventions);
            final int _tmpTrustLevel;
            _tmpTrustLevel = _cursor.getInt(_cursorIndexOfTrustLevel);
            final double _tmpResponseRate;
            _tmpResponseRate = _cursor.getDouble(_cursorIndexOfResponseRate);
            final Integer _tmpAverageResponseTime;
            if (_cursor.isNull(_cursorIndexOfAverageResponseTime)) {
              _tmpAverageResponseTime = null;
            } else {
              _tmpAverageResponseTime = _cursor.getInt(_cursorIndexOfAverageResponseTime);
            }
            final String _tmpPreferredContactTime;
            if (_cursor.isNull(_cursorIndexOfPreferredContactTime)) {
              _tmpPreferredContactTime = null;
            } else {
              _tmpPreferredContactTime = _cursor.getString(_cursorIndexOfPreferredContactTime);
            }
            final String _tmpTimeZone;
            if (_cursor.isNull(_cursorIndexOfTimeZone)) {
              _tmpTimeZone = null;
            } else {
              _tmpTimeZone = _cursor.getString(_cursorIndexOfTimeZone);
            }
            final String _tmpNotificationFrequency;
            if (_cursor.isNull(_cursorIndexOfNotificationFrequency)) {
              _tmpNotificationFrequency = null;
            } else {
              _tmpNotificationFrequency = _cursor.getString(_cursorIndexOfNotificationFrequency);
            }
            final String _tmpSupportType;
            if (_cursor.isNull(_cursorIndexOfSupportType)) {
              _tmpSupportType = null;
            } else {
              _tmpSupportType = _cursor.getString(_cursorIndexOfSupportType);
            }
            final String _tmpSpecializations;
            if (_cursor.isNull(_cursorIndexOfSpecializations)) {
              _tmpSpecializations = null;
            } else {
              _tmpSpecializations = _cursor.getString(_cursorIndexOfSpecializations);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final boolean _tmpEmergencyContact;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfEmergencyContact);
            _tmpEmergencyContact = _tmp_3 != 0;
            final boolean _tmpCanReceiveSpendingAlerts;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfCanReceiveSpendingAlerts);
            _tmpCanReceiveSpendingAlerts = _tmp_4 != 0;
            final boolean _tmpCanReceiveBudgetUpdates;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfCanReceiveBudgetUpdates);
            _tmpCanReceiveBudgetUpdates = _tmp_5 != 0;
            final boolean _tmpCanReceiveGoalProgress;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfCanReceiveGoalProgress);
            _tmpCanReceiveGoalProgress = _tmp_6 != 0;
            final boolean _tmpCanReceiveEmergencyAlerts;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfCanReceiveEmergencyAlerts);
            _tmpCanReceiveEmergencyAlerts = _tmp_7 != 0;
            final boolean _tmpMutualAccountability;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfMutualAccountability);
            _tmpMutualAccountability = _tmp_8 != 0;
            final String _tmpPartnerUserId;
            if (_cursor.isNull(_cursorIndexOfPartnerUserId)) {
              _tmpPartnerUserId = null;
            } else {
              _tmpPartnerUserId = _cursor.getString(_cursorIndexOfPartnerUserId);
            }
            final boolean _tmpConsentGiven;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfConsentGiven);
            _tmpConsentGiven = _tmp_9 != 0;
            final LocalDateTime _tmpConsentDate;
            final String _tmp_10;
            if (_cursor.isNull(_cursorIndexOfConsentDate)) {
              _tmp_10 = null;
            } else {
              _tmp_10 = _cursor.getString(_cursorIndexOfConsentDate);
            }
            _tmpConsentDate = __converters.toLocalDateTime(_tmp_10);
            final LocalDateTime _tmpLastConsentUpdate;
            final String _tmp_11;
            if (_cursor.isNull(_cursorIndexOfLastConsentUpdate)) {
              _tmp_11 = null;
            } else {
              _tmp_11 = _cursor.getString(_cursorIndexOfLastConsentUpdate);
            }
            _tmpLastConsentUpdate = __converters.toLocalDateTime(_tmp_11);
            final String _tmpPrivacyLevel;
            if (_cursor.isNull(_cursorIndexOfPrivacyLevel)) {
              _tmpPrivacyLevel = null;
            } else {
              _tmpPrivacyLevel = _cursor.getString(_cursorIndexOfPrivacyLevel);
            }
            _result = new AccountabilityContact(_tmpId,_tmpName,_tmpRelationship,_tmpContactMethod,_tmpContactInfo,_tmpIsActive,_tmpPermissionLevel,_tmpSharingPreferences,_tmpAddedDate,_tmpLastContactDate,_tmpTotalInteractions,_tmpSuccessfulInterventions,_tmpTrustLevel,_tmpResponseRate,_tmpAverageResponseTime,_tmpPreferredContactTime,_tmpTimeZone,_tmpNotificationFrequency,_tmpSupportType,_tmpSpecializations,_tmpNotes,_tmpEmergencyContact,_tmpCanReceiveSpendingAlerts,_tmpCanReceiveBudgetUpdates,_tmpCanReceiveGoalProgress,_tmpCanReceiveEmergencyAlerts,_tmpMutualAccountability,_tmpPartnerUserId,_tmpConsentGiven,_tmpConsentDate,_tmpLastConsentUpdate,_tmpPrivacyLevel);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg1);
  }

  @Override
  public Object getActiveContactCount(final Continuation<? super Integer> arg0) {
    final String _sql = "SELECT COUNT(*) FROM accountability_contacts WHERE isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Object getAverageResponseRate(final Continuation<? super Double> arg0) {
    final String _sql = "SELECT AVG(responseRate) FROM accountability_contacts WHERE isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Object getAverageTrustLevel(final Continuation<? super Double> arg0) {
    final String _sql = "SELECT AVG(trustLevel) FROM accountability_contacts WHERE isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
