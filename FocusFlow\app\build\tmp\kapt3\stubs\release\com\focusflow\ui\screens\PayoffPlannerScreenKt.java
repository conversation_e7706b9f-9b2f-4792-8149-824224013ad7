package com.focusflow.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000p\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u0006\n\u0002\b\u0002\u001a\b\u0010\u0000\u001a\u00020\u0001H\u0007\u001a6\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH\u0007\u001a\u0018\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\u000bH\u0007\u001a\u001e\u0010\u000f\u001a\u00020\u00012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\f\u001a\u00020\rH\u0007\u001aX\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u00062\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00010\u00132\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0017\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u0006H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u001d\u0010\u001e\u001aH\u0010\u001f\u001a\u00020\u00012\u0006\u0010 \u001a\u00020!2\u0012\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\u00010#2\u0006\u0010$\u001a\u00020\u00062\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00010\u00132\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00010\u0013H\u0007\u001ax\u0010\'\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\u0012\u0010(\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010#2\u0006\u0010)\u001a\u00020\u00152\u0012\u0010*\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u00010#2\u0006\u0010+\u001a\u00020\u00152\u0012\u0010,\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u00010#2\u0006\u0010-\u001a\u00020\u00152\u0012\u0010.\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u00010#H\u0007\u001a6\u0010/\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u00062\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00010\u00132\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0017\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u0019H\u0007\u001a\u0010\u00100\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u0004H\u0007\u001a \u00101\u001a\u00020\u00012\f\u00102\u001a\b\u0012\u0004\u0012\u00020\u00010\u00132\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a.\u00103\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\f\u001a\u00020\rH\u0007\u001a\u0010\u00104\u001a\u00020\u00012\u0006\u00105\u001a\u000206H\u0007\u001a\u0010\u00107\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u0004H\u0007\u001a\u0010\u00108\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u0004H\u0007\u001a\u001e\u00109\u001a\u00020\u00012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\f\u001a\u00020\rH\u0007\u001aN\u0010:\u001a\u00020\u00012\u0006\u0010 \u001a\u00020!2\u0012\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\u00010#2\u0006\u0010-\u001a\u00020\u00152\u0012\u0010.\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u00010#2\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00010\u0013H\u0007\u001a*\u0010;\u001a\u00020\u00012\u0006\u0010<\u001a\u00020\u00152\u0006\u0010=\u001a\u00020\u00152\u0006\u0010\u001a\u001a\u00020\u001bH\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b>\u0010?\u001a0\u0010@\u001a\u00020\u00012\u0006\u0010A\u001a\u00020B2\u0006\u0010<\u001a\u00020\u00152\u0006\u0010C\u001a\u00020\u00062\u0006\u0010D\u001a\u00020\u00062\u0006\u0010E\u001a\u00020BH\u0007\u001a6\u0010F\u001a\u00020G2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010-\u001a\u00020\u00152\u0006\u0010)\u001a\u00020\u00152\u0006\u0010+\u001a\u00020\u00152\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u0002\u001a\u0018\u0010H\u001a\u00020\u00152\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0003\u001a\u00020\u0004H\u0002\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006I"}, d2 = {"EmptyStateMessage", "", "EnhancedPayoffResultsSection", "payoffPlan", "Lcom/focusflow/ui/viewmodel/PayoffPlan;", "showComparison", "", "creditCards", "", "Lcom/focusflow/data/model/CreditCard;", "goalType", "Lcom/focusflow/ui/screens/PayoffGoalType;", "viewModel", "Lcom/focusflow/ui/viewmodel/DebtViewModel;", "EnhancedPayoffSummaryCard", "EnhancedStrategyComparisonCard", "EnhancedStrategyOption", "selected", "onClick", "Lkotlin/Function0;", "title", "", "subtitle", "description", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "color", "Landroidx/compose/ui/graphics/Color;", "showAdvanced", "EnhancedStrategyOption-vmayBhU", "(ZLkotlin/jvm/functions/Function0;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/graphics/vector/ImageVector;JZ)V", "EnhancedStrategySelectionCard", "selectedStrategy", "Lcom/focusflow/ui/viewmodel/PayoffStrategy;", "onStrategySelected", "Lkotlin/Function1;", "showAdvancedOptions", "onToggleAdvancedOptions", "onGeneratePlan", "GoalSelectionCard", "onGoalTypeChanged", "targetDate", "onTargetDateChanged", "targetPayment", "onTargetPaymentChanged", "extraPayment", "onExtraPaymentChanged", "GoalTypeOption", "MonthlyScheduleCard", "PayoffPlannerScreen", "onNavigateBack", "PayoffResultsSection", "PayoffStepItem", "step", "Lcom/focusflow/ui/viewmodel/PayoffStep;", "PayoffSummaryCard", "PayoffTimelineCard", "StrategyComparisonCard", "StrategySelectionCard", "SummaryItem", "label", "value", "SummaryItem-mxwnekA", "(Ljava/lang/String;Ljava/lang/String;J)V", "TimelineMilestone", "month", "", "isStart", "isEnd", "totalMonths", "calculateExtraPayment", "", "getGoalAchievementMessage", "app_release"})
public final class PayoffPlannerScreenKt {
    
    @androidx.compose.runtime.Composable
    public static final void PayoffPlannerScreen(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.DebtViewModel viewModel) {
    }
    
    private static final double calculateExtraPayment(com.focusflow.ui.screens.PayoffGoalType goalType, java.lang.String extraPayment, java.lang.String targetDate, java.lang.String targetPayment, java.util.List<com.focusflow.data.model.CreditCard> creditCards) {
        return 0.0;
    }
    
    @androidx.compose.runtime.Composable
    public static final void GoalSelectionCard(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.screens.PayoffGoalType goalType, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.ui.screens.PayoffGoalType, kotlin.Unit> onGoalTypeChanged, @org.jetbrains.annotations.NotNull
    java.lang.String targetDate, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTargetDateChanged, @org.jetbrains.annotations.NotNull
    java.lang.String targetPayment, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTargetPaymentChanged, @org.jetbrains.annotations.NotNull
    java.lang.String extraPayment, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onExtraPaymentChanged) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void GoalTypeOption(boolean selected, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.graphics.vector.ImageVector icon) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EmptyStateMessage() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EnhancedStrategySelectionCard(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffStrategy selectedStrategy, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.ui.viewmodel.PayoffStrategy, kotlin.Unit> onStrategySelected, boolean showAdvancedOptions, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onToggleAdvancedOptions, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onGeneratePlan) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void StrategySelectionCard(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffStrategy selectedStrategy, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.ui.viewmodel.PayoffStrategy, kotlin.Unit> onStrategySelected, @org.jetbrains.annotations.NotNull
    java.lang.String extraPayment, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onExtraPaymentChanged, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onGeneratePlan) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EnhancedPayoffResultsSection(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffPlan payoffPlan, boolean showComparison, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.CreditCard> creditCards, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.screens.PayoffGoalType goalType, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.DebtViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void PayoffResultsSection(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffPlan payoffPlan, boolean showComparison, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.CreditCard> creditCards, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.DebtViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void PayoffSummaryCard(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffPlan payoffPlan) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void StrategyComparisonCard(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.CreditCard> creditCards, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.DebtViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void PayoffStepItem(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffStep step) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EnhancedPayoffSummaryCard(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffPlan payoffPlan, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.screens.PayoffGoalType goalType) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void PayoffTimelineCard(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffPlan payoffPlan) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void TimelineMilestone(int month, @org.jetbrains.annotations.NotNull
    java.lang.String label, boolean isStart, boolean isEnd, int totalMonths) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void MonthlyScheduleCard(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffPlan payoffPlan) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EnhancedStrategyComparisonCard(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.CreditCard> creditCards, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.DebtViewModel viewModel) {
    }
    
    private static final java.lang.String getGoalAchievementMessage(com.focusflow.ui.screens.PayoffGoalType goalType, com.focusflow.ui.viewmodel.PayoffPlan payoffPlan) {
        return null;
    }
}