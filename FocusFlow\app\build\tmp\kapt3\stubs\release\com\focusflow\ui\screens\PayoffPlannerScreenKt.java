package com.focusflow.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000B\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a*\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\b\u0010\t\u001a \u0010\n\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\u00052\u0006\u0010\f\u001a\u00020\u00052\u0006\u0010\r\u001a\u00020\u0005H\u0007\u001a\b\u0010\u000e\u001a\u00020\u0001H\u0007\u001a,\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u00032\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00122\u0006\u0010\u0013\u001a\u00020\u0005H\u0007\u001a \u0010\u0014\u001a\u00020\u00012\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00010\u00162\b\b\u0002\u0010\u0017\u001a\u00020\u0018H\u0007\u001a6\u0010\u0019\u001a\u00020\u00012\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001b2\b\u0010\u001d\u001a\u0004\u0018\u00010\u001e2\u0012\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u00010\u0012H\u0007\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006 "}, d2 = {"DebtMetricColumn", "", "label", "", "amount", "", "color", "Landroidx/compose/ui/graphics/Color;", "DebtMetricColumn-mxwnekA", "(Ljava/lang/String;DJ)V", "DebtOverviewCard", "totalDebt", "minimumPayments", "availableForDebt", "EmptyStateCard", "ExtraPaymentInputCard", "currentAmount", "onAmountChanged", "Lkotlin/Function1;", "availableAmount", "PayoffPlannerScreen", "onNavigateBack", "Lkotlin/Function0;", "viewModel", "Lcom/focusflow/ui/viewmodel/PayoffPlannerViewModel;", "StrategyComparisonCard", "avalanche", "Lcom/focusflow/data/model/PayoffComparison;", "snowball", "selectedStrategy", "Lcom/focusflow/ui/viewmodel/PayoffStrategy;", "onStrategySelected", "app_release"})
public final class PayoffPlannerScreenKt {
    
    @androidx.compose.runtime.Composable
    public static final void PayoffPlannerScreen(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffPlannerViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void DebtOverviewCard(double totalDebt, double minimumPayments, double availableForDebt) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void ExtraPaymentInputCard(@org.jetbrains.annotations.NotNull
    java.lang.String currentAmount, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onAmountChanged, double availableAmount) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void StrategyComparisonCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PayoffComparison avalanche, @org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PayoffComparison snowball, @org.jetbrains.annotations.Nullable
    com.focusflow.ui.viewmodel.PayoffStrategy selectedStrategy, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.ui.viewmodel.PayoffStrategy, kotlin.Unit> onStrategySelected) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EmptyStateCard() {
    }
}