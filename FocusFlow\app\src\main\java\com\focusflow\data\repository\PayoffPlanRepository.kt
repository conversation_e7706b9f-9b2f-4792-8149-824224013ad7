package com.focusflow.data.repository

import com.focusflow.data.dao.PayoffPlanDao
import com.focusflow.data.dao.PaymentScheduleDao
import com.focusflow.data.dao.PayoffMilestoneDao
import com.focusflow.data.model.PayoffPlan
import com.focusflow.data.model.PaymentSchedule
import com.focusflow.data.model.PayoffMilestone
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDate
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PayoffPlanRepository @Inject constructor(
    private val payoffPlanDao: PayoffPlanDao,
    private val paymentScheduleDao: PaymentScheduleDao,
    private val payoffMilestoneDao: PayoffMilestoneDao
) {
    // PayoffPlan operations
    fun getAllActivePayoffPlans(): Flow<List<PayoffPlan>> = payoffPlanDao.getAllActivePayoffPlans()

    suspend fun getPayoffPlanById(planId: Long): PayoffPlan? = payoffPlanDao.getPayoffPlanById(planId)

    suspend fun getCurrentActivePayoffPlan(): PayoffPlan? = payoffPlanDao.getCurrentActivePayoffPlan()

    fun getCurrentActivePayoffPlanFlow(): Flow<PayoffPlan?> = payoffPlanDao.getCurrentActivePayoffPlanFlow()

    suspend fun insertPayoffPlan(payoffPlan: PayoffPlan): Long = payoffPlanDao.insertPayoffPlan(payoffPlan)

    suspend fun updatePayoffPlan(payoffPlan: PayoffPlan) = payoffPlanDao.updatePayoffPlan(payoffPlan)

    suspend fun deletePayoffPlan(payoffPlan: PayoffPlan) = payoffPlanDao.deletePayoffPlan(payoffPlan)

    suspend fun setActivePayoffPlan(planId: Long) {
        payoffPlanDao.deactivateOtherPlans(planId)
    }

    fun getPayoffPlansByStrategy(strategy: String): Flow<List<PayoffPlan>> = 
        payoffPlanDao.getPayoffPlansByStrategy(strategy)

    // PaymentSchedule operations
    fun getPaymentScheduleByPlan(planId: Long): Flow<List<PaymentSchedule>> = 
        paymentScheduleDao.getPaymentScheduleByPlan(planId)

    suspend fun getPaymentScheduleByPlanSync(planId: Long): List<PaymentSchedule> = 
        paymentScheduleDao.getPaymentScheduleByPlanSync(planId)

    fun getPaymentScheduleByCard(cardId: Long): Flow<List<PaymentSchedule>> = 
        paymentScheduleDao.getPaymentScheduleByCard(cardId)

    fun getPaymentsDueInRange(startDate: LocalDate, endDate: LocalDate): Flow<List<PaymentSchedule>> = 
        paymentScheduleDao.getPaymentsDueInRange(startDate, endDate)

    suspend fun insertPaymentSchedules(paymentSchedules: List<PaymentSchedule>) = 
        paymentScheduleDao.insertPaymentSchedules(paymentSchedules)

    suspend fun deletePaymentSchedulesByPlan(planId: Long) = 
        paymentScheduleDao.deletePaymentSchedulesByPlan(planId)

    // PayoffMilestone operations
    fun getMilestonesByPlan(planId: Long): Flow<List<PayoffMilestone>> = 
        payoffMilestoneDao.getMilestonesByPlan(planId)

    suspend fun getMilestonesByPlanSync(planId: Long): List<PayoffMilestone> = 
        payoffMilestoneDao.getMilestonesByPlanSync(planId)

    fun getUpcomingMilestones(date: LocalDate): Flow<List<PayoffMilestone>> = 
        payoffMilestoneDao.getUpcomingMilestones(date)

    fun getCompletedMilestones(): Flow<List<PayoffMilestone>> = 
        payoffMilestoneDao.getCompletedMilestones()

    suspend fun getNextMilestone(planId: Long): PayoffMilestone? = 
        payoffMilestoneDao.getNextMilestone(planId)

    suspend fun insertMilestones(milestones: List<PayoffMilestone>) = 
        payoffMilestoneDao.insertMilestones(milestones)

    suspend fun markMilestoneCompleted(milestoneId: Long, completedDate: LocalDate) = 
        payoffMilestoneDao.markMilestoneCompleted(milestoneId, completedDate)

    suspend fun deleteMilestonesByPlan(planId: Long) = 
        payoffMilestoneDao.deleteMilestonesByPlan(planId)

    // Combined operations
    suspend fun createCompletePayoffPlan(
        plan: PayoffPlan,
        schedules: List<PaymentSchedule>,
        milestones: List<PayoffMilestone>
    ): Long {
        val planId = insertPayoffPlan(plan)
        
        // Update schedules and milestones with the new plan ID
        val updatedSchedules = schedules.map { it.copy(payoffPlanId = planId) }
        val updatedMilestones = milestones.map { it.copy(payoffPlanId = planId) }
        
        insertPaymentSchedules(updatedSchedules)
        insertMilestones(updatedMilestones)
        
        return planId
    }

    suspend fun deleteCompletePayoffPlan(planId: Long) {
        deletePaymentSchedulesByPlan(planId)
        deleteMilestonesByPlan(planId)
        payoffPlanDao.getPayoffPlanById(planId)?.let { plan ->
            payoffPlanDao.deletePayoffPlan(plan)
        }
    }
}
