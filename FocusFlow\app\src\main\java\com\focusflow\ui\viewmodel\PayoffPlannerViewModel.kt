package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.*
import com.focusflow.data.repository.CreditCardRepository
import com.focusflow.data.repository.PayoffPlanRepository
import com.focusflow.data.repository.BudgetCategoryRepository
import com.focusflow.data.repository.UserPreferencesRepository
import com.focusflow.data.model.*
import javax.inject.Inject
import kotlin.math.ceil

@HiltViewModel
class PayoffPlannerViewModel @Inject constructor(
    private val creditCardRepository: CreditCardRepository,
    private val payoffPlanRepository: PayoffPlanRepository,
    private val budgetCategoryRepository: BudgetCategoryRepository,
    private val userPreferencesRepository: UserPreferencesRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(PayoffPlannerUiState())
    val uiState: StateFlow<PayoffPlannerUiState> = _uiState.asStateFlow()

    val allCreditCards = creditCardRepository.getAllActiveCreditCards()
    val currentPayoffPlan = payoffPlanRepository.getCurrentActivePayoffPlanFlow()

    init {
        loadPayoffPlannerData()
    }

    private fun loadPayoffPlannerData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                // Load current debt information
                val totalDebt = creditCardRepository.getTotalDebt()
                val totalMinimumPayments = creditCardRepository.getTotalMinimumPaymentsDue(
                    Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date.plus(30, DateTimeUnit.DAY)
                )
                
                // Load budget information for debt payment allocation
                val preferences = userPreferencesRepository.getUserPreferencesSync()
                val budgetPeriod = preferences?.budgetPeriod ?: "weekly"
                
                // Look for existing debt payment budget category
                val debtPaymentCategory = budgetCategoryRepository.getBudgetCategoryByName("Debt Payment")
                val availableForDebtPayment = debtPaymentCategory?.let { 
                    (it.allocatedAmount - it.spentAmount).coerceAtLeast(0.0) 
                } ?: 0.0
                
                _uiState.value = _uiState.value.copy(
                    totalDebt = totalDebt,
                    totalMinimumPayments = totalMinimumPayments,
                    budgetPeriod = budgetPeriod,
                    availableForDebtPayment = availableForDebtPayment,
                    isLoading = false
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load payoff planner data: ${e.message}"
                )
            }
        }
    }

    fun generatePayoffComparison(extraPayment: Double = 0.0) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isGeneratingPlan = true, error = null)
                
                allCreditCards.first().let { cards ->
                    if (cards.isEmpty()) {
                        _uiState.value = _uiState.value.copy(
                            isGeneratingPlan = false,
                            error = "No credit cards found. Add credit cards first."
                        )
                        return@let
                    }

                    // Generate both strategies for comparison
                    val avalancheComparison = calculatePayoffStrategy(cards, PayoffStrategy.AVALANCHE, extraPayment)
                    val snowballComparison = calculatePayoffStrategy(cards, PayoffStrategy.SNOWBALL, extraPayment)
                    
                    _uiState.value = _uiState.value.copy(
                        avalancheComparison = avalancheComparison,
                        snowballComparison = snowballComparison,
                        isGeneratingPlan = false
                    )
                }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isGeneratingPlan = false,
                    error = "Failed to generate payoff comparison: ${e.message}"
                )
            }
        }
    }

    private fun calculatePayoffStrategy(
        cards: List<CreditCard>,
        strategy: PayoffStrategy,
        extraPayment: Double
    ): PayoffComparison {
        if (cards.isEmpty()) {
            return PayoffComparison(
                strategy = strategy,
                totalMonths = 0,
                totalInterestPaid = 0.0,
                totalPayments = 0.0,
                monthlyPayment = 0.0,
                payoffDate = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
            )
        }

        // Sort cards based on strategy
        val sortedCards = when (strategy) {
            PayoffStrategy.AVALANCHE -> cards.sortedByDescending { it.interestRate }
            PayoffStrategy.SNOWBALL -> cards.sortedBy { it.currentBalance }
        }

        val cardBalances = sortedCards.associate { it.id to it.currentBalance }.toMutableMap()
        val totalMinimumPayments = sortedCards.sumOf { it.minimumPayment }
        val totalAvailablePayment = totalMinimumPayments + extraPayment

        var month = 1
        var totalInterestPaid = 0.0

        // Simulate payoff process
        while (cardBalances.values.any { it > 0.01 } && month <= 600) { // Cap at 50 years
            var remainingExtraPayment = extraPayment

            // Make minimum payments on all cards first
            for (card in sortedCards) {
                val currentBalance = cardBalances[card.id] ?: 0.0
                if (currentBalance <= 0.01) continue

                val monthlyInterest = currentBalance * (card.interestRate / 100 / 12)
                val minimumPayment = card.minimumPayment.coerceAtMost(currentBalance + monthlyInterest)
                val principalPayment = (minimumPayment - monthlyInterest).coerceAtLeast(0.0)

                totalInterestPaid += monthlyInterest
                cardBalances[card.id] = (currentBalance - principalPayment).coerceAtLeast(0.0)
            }

            // Apply extra payment to target card (first card with balance in sorted order)
            val targetCard = sortedCards.firstOrNull { (cardBalances[it.id] ?: 0.0) > 0.01 }
            if (targetCard != null && remainingExtraPayment > 0) {
                val currentBalance = cardBalances[targetCard.id] ?: 0.0
                val extraPaymentAmount = remainingExtraPayment.coerceAtMost(currentBalance)
                cardBalances[targetCard.id] = (currentBalance - extraPaymentAmount).coerceAtLeast(0.0)
            }

            month++
        }

        val totalMonths = month - 1
        val totalPayments = totalAvailablePayment * totalMonths
        val payoffDate = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
            .plus(totalMonths, DateTimeUnit.MONTH)

        return PayoffComparison(
            strategy = strategy,
            totalMonths = totalMonths,
            totalInterestPaid = totalInterestPaid,
            totalPayments = totalPayments,
            monthlyPayment = totalAvailablePayment,
            payoffDate = payoffDate
        )
    }

    fun createPayoffPlan(strategy: PayoffStrategy, extraPayment: Double, planName: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isCreatingPlan = true, error = null)
                
                allCreditCards.first().let { cards ->
                    if (cards.isEmpty()) {
                        _uiState.value = _uiState.value.copy(
                            isCreatingPlan = false,
                            error = "No credit cards found. Add credit cards first."
                        )
                        return@let
                    }

                    val comparison = when (strategy) {
                        PayoffStrategy.AVALANCHE -> _uiState.value.avalancheComparison
                        PayoffStrategy.SNOWBALL -> _uiState.value.snowballComparison
                    }

                    if (comparison == null) {
                        _uiState.value = _uiState.value.copy(
                            isCreatingPlan = false,
                            error = "Please generate payoff comparison first."
                        )
                        return@let
                    }

                    // Create the payoff plan
                    val plan = PayoffPlan(
                        name = planName,
                        strategy = strategy.name,
                        totalExtraPayment = extraPayment,
                        totalMonths = comparison.totalMonths,
                        totalInterestSaved = 0.0, // Calculate based on minimum payment scenario
                        totalPayments = comparison.totalPayments,
                        createdAt = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()),
                        targetPayoffDate = comparison.payoffDate,
                        monthlyBudgetAllocation = extraPayment
                    )

                    // Generate payment schedules and milestones
                    val schedules = generatePaymentSchedules(cards, strategy, extraPayment)
                    val milestones = generateMilestones(cards, strategy, comparison.totalMonths)

                    // Save to database
                    val planId = payoffPlanRepository.createCompletePayoffPlan(plan, schedules, milestones)
                    payoffPlanRepository.setActivePayoffPlan(planId)

                    _uiState.value = _uiState.value.copy(
                        isCreatingPlan = false,
                        selectedStrategy = strategy
                    )
                }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isCreatingPlan = false,
                    error = "Failed to create payoff plan: ${e.message}"
                )
            }
        }
    }

    private fun generatePaymentSchedules(
        cards: List<CreditCard>,
        strategy: PayoffStrategy,
        extraPayment: Double
    ): List<PaymentSchedule> {
        // This would contain the detailed month-by-month payment schedule
        // Implementation similar to the existing DebtViewModel logic but stored as entities
        return emptyList() // Placeholder for now
    }

    private fun generateMilestones(
        cards: List<CreditCard>,
        strategy: PayoffStrategy,
        totalMonths: Int
    ): List<PayoffMilestone> {
        val milestones = mutableListOf<PayoffMilestone>()
        val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
        
        // Add quarter milestones
        val quarterMonths = totalMonths / 4
        if (quarterMonths > 0) {
            milestones.add(
                PayoffMilestone(
                    payoffPlanId = 0, // Will be updated when saved
                    milestoneType = "QUARTER_COMPLETE",
                    targetDate = today.plus(quarterMonths, DateTimeUnit.MONTH),
                    targetAmount = cards.sumOf { it.currentBalance } * 0.75,
                    description = "25% of debt paid off! 🎉",
                    celebrationMessage = "Great progress! You're a quarter of the way to debt freedom!"
                )
            )
        }
        
        // Add individual card payoff milestones
        cards.forEach { card ->
            milestones.add(
                PayoffMilestone(
                    payoffPlanId = 0,
                    milestoneType = "CARD_PAID_OFF",
                    targetDate = today.plus(totalMonths / 2, DateTimeUnit.MONTH), // Estimate
                    targetAmount = 0.0,
                    description = "${card.name} paid off! 🎊",
                    celebrationMessage = "Congratulations! You've completely paid off ${card.name}!"
                )
            )
        }
        
        return milestones
    }

    fun updateExtraPayment(amount: Double) {
        _uiState.value = _uiState.value.copy(extraPaymentAmount = amount)
    }

    fun selectStrategy(strategy: PayoffStrategy) {
        _uiState.value = _uiState.value.copy(selectedStrategy = strategy)
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class PayoffPlannerUiState(
    val totalDebt: Double = 0.0,
    val totalMinimumPayments: Double = 0.0,
    val budgetPeriod: String = "weekly",
    val availableForDebtPayment: Double = 0.0,
    val extraPaymentAmount: Double = 0.0,
    val selectedStrategy: PayoffStrategy? = null,
    val avalancheComparison: PayoffComparison? = null,
    val snowballComparison: PayoffComparison? = null,
    val isLoading: Boolean = false,
    val isGeneratingPlan: Boolean = false,
    val isCreatingPlan: Boolean = false,
    val error: String? = null
)

data class PayoffComparison(
    val strategy: PayoffStrategy,
    val totalMonths: Int,
    val totalInterestPaid: Double,
    val totalPayments: Double,
    val monthlyPayment: Double,
    val payoffDate: LocalDate
)

enum class PayoffStrategy {
    SNOWBALL, // Pay smallest balance first
    AVALANCHE // Pay highest interest rate first
}
