package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\"\b\u0086\b\u0018\u00002\u00020\u0001Bi\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0003\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0007\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0002\u0010\u0010J\t\u0010 \u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010!\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\u001bJ\u0010\u0010\"\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u001eJ\u000b\u0010#\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\t\u0010$\u001a\u00020\u0003H\u00c6\u0003J\t\u0010%\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010&\u001a\u0004\u0018\u00010\rH\u00c6\u0003J\t\u0010\'\u001a\u00020\u0007H\u00c6\u0003J\u000b\u0010(\u001a\u0004\u0018\u00010\tH\u00c6\u0003Jr\u0010)\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\b\b\u0002\u0010\n\u001a\u00020\u00032\b\b\u0002\u0010\u000b\u001a\u00020\u00032\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r2\b\b\u0002\u0010\u000e\u001a\u00020\u00072\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\tH\u00c6\u0001\u00a2\u0006\u0002\u0010*J\u0013\u0010+\u001a\u00020\u00032\b\u0010,\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010-\u001a\u00020\u0007H\u00d6\u0001J\t\u0010.\u001a\u00020\tH\u00d6\u0001R\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u000e\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0013\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0013\u0010\f\u001a\u0004\u0018\u00010\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0013\u0010\u000f\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0016R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0012R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0012R\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\n\n\u0002\u0010\u001c\u001a\u0004\b\u001a\u0010\u001bR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\n\n\u0002\u0010\u001f\u001a\u0004\b\u001d\u0010\u001e\u00a8\u0006/"}, d2 = {"Lcom/focusflow/ui/viewmodel/ImpulseControlUiState;", "", "isLoading", "", "lastAddedItemId", "", "recommendedDelayPeriod", "", "delayRecommendationReason", "", "isBreathingExerciseActive", "breathingExerciseCompleted", "delayStatistics", "Lcom/focusflow/service/DelayStatistics;", "coolingOffPeriod", "error", "(ZLjava/lang/Long;Ljava/lang/Integer;Ljava/lang/String;ZZLcom/focusflow/service/DelayStatistics;ILjava/lang/String;)V", "getBreathingExerciseCompleted", "()Z", "getCoolingOffPeriod", "()I", "getDelayRecommendationReason", "()Ljava/lang/String;", "getDelayStatistics", "()Lcom/focusflow/service/DelayStatistics;", "getError", "getLastAddedItemId", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getRecommendedDelayPeriod", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(ZLjava/lang/Long;Ljava/lang/Integer;Ljava/lang/String;ZZLcom/focusflow/service/DelayStatistics;ILjava/lang/String;)Lcom/focusflow/ui/viewmodel/ImpulseControlUiState;", "equals", "other", "hashCode", "toString", "app_debug"})
public final class ImpulseControlUiState {
    private final boolean isLoading = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Long lastAddedItemId = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer recommendedDelayPeriod = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String delayRecommendationReason = null;
    private final boolean isBreathingExerciseActive = false;
    private final boolean breathingExerciseCompleted = false;
    @org.jetbrains.annotations.Nullable
    private final com.focusflow.service.DelayStatistics delayStatistics = null;
    private final int coolingOffPeriod = 0;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String error = null;
    
    public ImpulseControlUiState(boolean isLoading, @org.jetbrains.annotations.Nullable
    java.lang.Long lastAddedItemId, @org.jetbrains.annotations.Nullable
    java.lang.Integer recommendedDelayPeriod, @org.jetbrains.annotations.Nullable
    java.lang.String delayRecommendationReason, boolean isBreathingExerciseActive, boolean breathingExerciseCompleted, @org.jetbrains.annotations.Nullable
    com.focusflow.service.DelayStatistics delayStatistics, int coolingOffPeriod, @org.jetbrains.annotations.Nullable
    java.lang.String error) {
        super();
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Long getLastAddedItemId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getRecommendedDelayPeriod() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getDelayRecommendationReason() {
        return null;
    }
    
    public final boolean isBreathingExerciseActive() {
        return false;
    }
    
    public final boolean getBreathingExerciseCompleted() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.service.DelayStatistics getDelayStatistics() {
        return null;
    }
    
    public final int getCoolingOffPeriod() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getError() {
        return null;
    }
    
    public ImpulseControlUiState() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Long component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component4() {
        return null;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean component6() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.service.DelayStatistics component7() {
        return null;
    }
    
    public final int component8() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.viewmodel.ImpulseControlUiState copy(boolean isLoading, @org.jetbrains.annotations.Nullable
    java.lang.Long lastAddedItemId, @org.jetbrains.annotations.Nullable
    java.lang.Integer recommendedDelayPeriod, @org.jetbrains.annotations.Nullable
    java.lang.String delayRecommendationReason, boolean isBreathingExerciseActive, boolean breathingExerciseCompleted, @org.jetbrains.annotations.Nullable
    com.focusflow.service.DelayStatistics delayStatistics, int coolingOffPeriod, @org.jetbrains.annotations.Nullable
    java.lang.String error) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}