package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0002\b2\b\u0086\b\u0018\u00002\u00020\u0001B\u00a5\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0003\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\t\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0016\u001a\u00020\t\u00a2\u0006\u0002\u0010\u0017J\t\u0010.\u001a\u00020\u0003H\u00c6\u0003J\t\u0010/\u001a\u00020\u0011H\u00c6\u0003J\t\u00100\u001a\u00020\u0007H\u00c6\u0003J\t\u00101\u001a\u00020\u0003H\u00c6\u0003J\t\u00102\u001a\u00020\u0003H\u00c6\u0003J\t\u00103\u001a\u00020\u0003H\u00c6\u0003J\t\u00104\u001a\u00020\tH\u00c6\u0003J\u0010\u00105\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\'J\u0010\u00106\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010*J\u000b\u00107\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\t\u00108\u001a\u00020\u0003H\u00c6\u0003J\t\u00109\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010:\u001a\u0004\u0018\u00010\rH\u00c6\u0003J\u000b\u0010;\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\t\u0010<\u001a\u00020\u0003H\u00c6\u0003J\u00ae\u0001\u0010=\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\b\b\u0002\u0010\n\u001a\u00020\u00032\b\b\u0002\u0010\u000b\u001a\u00020\u00032\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\t2\b\b\u0002\u0010\u000f\u001a\u00020\u00032\b\b\u0002\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u00072\b\b\u0002\u0010\u0013\u001a\u00020\u00032\b\b\u0002\u0010\u0014\u001a\u00020\u00032\b\b\u0002\u0010\u0015\u001a\u00020\u00032\b\b\u0002\u0010\u0016\u001a\u00020\tH\u00c6\u0001\u00a2\u0006\u0002\u0010>J\u0013\u0010?\u001a\u00020\u00032\b\u0010@\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010A\u001a\u00020\u0007H\u00d6\u0001J\t\u0010B\u001a\u00020\tH\u00d6\u0001R\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u0016\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u0012\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0013\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001bR\u0013\u0010\f\u001a\u0004\u0018\u00010\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0011\u0010\u0014\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0019R\u0011\u0010\u0013\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0019R\u0011\u0010\u0015\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0019R\u0013\u0010\u000e\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u001bR\u0011\u0010\u000f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u0019R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0019R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0019R\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\n\n\u0002\u0010(\u001a\u0004\b&\u0010\'R\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\n\n\u0002\u0010+\u001a\u0004\b)\u0010*R\u0011\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010-\u00a8\u0006C"}, d2 = {"Lcom/focusflow/ui/viewmodel/ImpulseControlUiState;", "", "isLoading", "", "lastAddedItemId", "", "recommendedDelayPeriod", "", "delayRecommendationReason", "", "isBreathingExerciseActive", "breathingExerciseCompleted", "delayStatistics", "Lcom/focusflow/service/DelayStatistics;", "error", "impulseControlEnabled", "spendingThreshold", "", "coolingOffPeriodSeconds", "enableReflectionQuestions", "enableBudgetWarnings", "enableWishlistSuggestions", "budgetPeriod", "(ZLjava/lang/Long;Ljava/lang/Integer;Ljava/lang/String;ZZLcom/focusflow/service/DelayStatistics;Ljava/lang/String;ZDIZZZLjava/lang/String;)V", "getBreathingExerciseCompleted", "()Z", "getBudgetPeriod", "()Ljava/lang/String;", "getCoolingOffPeriodSeconds", "()I", "getDelayRecommendationReason", "getDelayStatistics", "()Lcom/focusflow/service/DelayStatistics;", "getEnableBudgetWarnings", "getEnableReflectionQuestions", "getEnableWishlistSuggestions", "getError", "getImpulseControlEnabled", "getLastAddedItemId", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getRecommendedDelayPeriod", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getSpendingThreshold", "()D", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(ZLjava/lang/Long;Ljava/lang/Integer;Ljava/lang/String;ZZLcom/focusflow/service/DelayStatistics;Ljava/lang/String;ZDIZZZLjava/lang/String;)Lcom/focusflow/ui/viewmodel/ImpulseControlUiState;", "equals", "other", "hashCode", "toString", "app_release"})
public final class ImpulseControlUiState {
    private final boolean isLoading = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Long lastAddedItemId = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer recommendedDelayPeriod = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String delayRecommendationReason = null;
    private final boolean isBreathingExerciseActive = false;
    private final boolean breathingExerciseCompleted = false;
    @org.jetbrains.annotations.Nullable
    private final com.focusflow.service.DelayStatistics delayStatistics = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String error = null;
    private final boolean impulseControlEnabled = false;
    private final double spendingThreshold = 0.0;
    private final int coolingOffPeriodSeconds = 0;
    private final boolean enableReflectionQuestions = false;
    private final boolean enableBudgetWarnings = false;
    private final boolean enableWishlistSuggestions = false;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String budgetPeriod = null;
    
    public ImpulseControlUiState(boolean isLoading, @org.jetbrains.annotations.Nullable
    java.lang.Long lastAddedItemId, @org.jetbrains.annotations.Nullable
    java.lang.Integer recommendedDelayPeriod, @org.jetbrains.annotations.Nullable
    java.lang.String delayRecommendationReason, boolean isBreathingExerciseActive, boolean breathingExerciseCompleted, @org.jetbrains.annotations.Nullable
    com.focusflow.service.DelayStatistics delayStatistics, @org.jetbrains.annotations.Nullable
    java.lang.String error, boolean impulseControlEnabled, double spendingThreshold, int coolingOffPeriodSeconds, boolean enableReflectionQuestions, boolean enableBudgetWarnings, boolean enableWishlistSuggestions, @org.jetbrains.annotations.NotNull
    java.lang.String budgetPeriod) {
        super();
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Long getLastAddedItemId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getRecommendedDelayPeriod() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getDelayRecommendationReason() {
        return null;
    }
    
    public final boolean isBreathingExerciseActive() {
        return false;
    }
    
    public final boolean getBreathingExerciseCompleted() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.service.DelayStatistics getDelayStatistics() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getError() {
        return null;
    }
    
    public final boolean getImpulseControlEnabled() {
        return false;
    }
    
    public final double getSpendingThreshold() {
        return 0.0;
    }
    
    public final int getCoolingOffPeriodSeconds() {
        return 0;
    }
    
    public final boolean getEnableReflectionQuestions() {
        return false;
    }
    
    public final boolean getEnableBudgetWarnings() {
        return false;
    }
    
    public final boolean getEnableWishlistSuggestions() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getBudgetPeriod() {
        return null;
    }
    
    public ImpulseControlUiState() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final double component10() {
        return 0.0;
    }
    
    public final int component11() {
        return 0;
    }
    
    public final boolean component12() {
        return false;
    }
    
    public final boolean component13() {
        return false;
    }
    
    public final boolean component14() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Long component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component4() {
        return null;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean component6() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.service.DelayStatistics component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component8() {
        return null;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.viewmodel.ImpulseControlUiState copy(boolean isLoading, @org.jetbrains.annotations.Nullable
    java.lang.Long lastAddedItemId, @org.jetbrains.annotations.Nullable
    java.lang.Integer recommendedDelayPeriod, @org.jetbrains.annotations.Nullable
    java.lang.String delayRecommendationReason, boolean isBreathingExerciseActive, boolean breathingExerciseCompleted, @org.jetbrains.annotations.Nullable
    com.focusflow.service.DelayStatistics delayStatistics, @org.jetbrains.annotations.Nullable
    java.lang.String error, boolean impulseControlEnabled, double spendingThreshold, int coolingOffPeriodSeconds, boolean enableReflectionQuestions, boolean enableBudgetWarnings, boolean enableWishlistSuggestions, @org.jetbrains.annotations.NotNull
    java.lang.String budgetPeriod) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}