package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\b\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ4\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u000f2\u0006\u0010\u001e\u001a\u00020\u000f2\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010\u000f2\b\b\u0002\u0010 \u001a\u00020!J\u0006\u0010\"\u001a\u00020\u001aJ\u000e\u0010#\u001a\u00020\u001a2\u0006\u0010$\u001a\u00020\u0013J\u001c\u0010%\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020!0&2\u0006\u0010\'\u001a\u00020\u000fH\u0002J\u001a\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00130\u000e0\r2\u0006\u0010\u001d\u001a\u00020\u000fJ\"\u0010)\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00130\u000e0\r2\u0006\u0010*\u001a\u00020!2\u0006\u0010+\u001a\u00020!J\b\u0010,\u001a\u00020\u001aH\u0002J\u000e\u0010-\u001a\u00020\u001a2\u0006\u0010$\u001a\u00020\u0013R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u001d\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00130\u000e0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0011R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006."}, d2 = {"Lcom/focusflow/ui/viewmodel/ExpenseViewModel;", "Landroidx/lifecycle/ViewModel;", "expenseRepository", "Lcom/focusflow/data/repository/ExpenseRepository;", "userPreferencesRepository", "Lcom/focusflow/data/repository/UserPreferencesRepository;", "gamificationService", "Lcom/focusflow/service/GamificationService;", "(Lcom/focusflow/data/repository/ExpenseRepository;Lcom/focusflow/data/repository/UserPreferencesRepository;Lcom/focusflow/service/GamificationService;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/focusflow/ui/viewmodel/ExpenseUiState;", "allCategories", "Lkotlinx/coroutines/flow/Flow;", "", "", "getAllCategories", "()Lkotlinx/coroutines/flow/Flow;", "allExpenses", "Lcom/focusflow/data/model/Expense;", "getAllExpenses", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "addExpense", "", "amount", "", "category", "description", "merchant", "date", "Lkotlinx/datetime/LocalDateTime;", "clearError", "deleteExpense", "expense", "getCurrentPeriodDates", "Lkotlin/Pair;", "period", "getExpensesByCategory", "getExpensesByDateRange", "startDate", "endDate", "loadCurrentPeriodExpenses", "updateExpense", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class ExpenseViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.ExpenseRepository expenseRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.GamificationService gamificationService = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.ExpenseUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.ExpenseUiState> uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Expense>> allExpenses = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.Flow<java.util.List<java.lang.String>> allCategories = null;
    
    @javax.inject.Inject
    public ExpenseViewModel(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.ExpenseRepository expenseRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.service.GamificationService gamificationService) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.ExpenseUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Expense>> getAllExpenses() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<java.lang.String>> getAllCategories() {
        return null;
    }
    
    private final void loadCurrentPeriodExpenses() {
    }
    
    public final void addExpense(double amount, @org.jetbrains.annotations.NotNull
    java.lang.String category, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.Nullable
    java.lang.String merchant, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime date) {
    }
    
    public final void deleteExpense(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Expense expense) {
    }
    
    public final void updateExpense(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Expense expense) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Expense>> getExpensesByCategory(@org.jetbrains.annotations.NotNull
    java.lang.String category) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Expense>> getExpensesByDateRange(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime startDate, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime endDate) {
        return null;
    }
    
    public final void clearError() {
    }
    
    private final kotlin.Pair<kotlinx.datetime.LocalDateTime, kotlinx.datetime.LocalDateTime> getCurrentPeriodDates(java.lang.String period) {
        return null;
    }
}