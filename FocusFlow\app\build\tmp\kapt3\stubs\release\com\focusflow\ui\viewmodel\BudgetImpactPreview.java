package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0016\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B=\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0005\u0012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00030\n\u0012\u0006\u0010\u000b\u001a\u00020\f\u00a2\u0006\u0002\u0010\rJ\u000b\u0010\u0019\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0005H\u00c6\u0003J\u000f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00030\nH\u00c6\u0003J\t\u0010\u001e\u001a\u00020\fH\u00c6\u0003JM\u0010\u001f\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00052\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00030\n2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u00c6\u0001J\u0013\u0010 \u001a\u00020\u00072\b\u0010!\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\"\u001a\u00020#H\u00d6\u0001J\t\u0010$\u001a\u00020%H\u00d6\u0001R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00030\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018\u00a8\u0006&"}, d2 = {"Lcom/focusflow/ui/viewmodel/BudgetImpactPreview;", "", "targetCategory", "Lcom/focusflow/data/model/BudgetCategory;", "purchaseAmount", "", "wouldExceedBudget", "", "remainingAfterPurchase", "alternativeCategories", "", "totalBudgetImpact", "Lcom/focusflow/ui/viewmodel/TotalBudgetImpact;", "(Lcom/focusflow/data/model/BudgetCategory;DZDLjava/util/List;Lcom/focusflow/ui/viewmodel/TotalBudgetImpact;)V", "getAlternativeCategories", "()Ljava/util/List;", "getPurchaseAmount", "()D", "getRemainingAfterPurchase", "getTargetCategory", "()Lcom/focusflow/data/model/BudgetCategory;", "getTotalBudgetImpact", "()Lcom/focusflow/ui/viewmodel/TotalBudgetImpact;", "getWouldExceedBudget", "()Z", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "other", "hashCode", "", "toString", "", "app_release"})
public final class BudgetImpactPreview {
    @org.jetbrains.annotations.Nullable
    private final com.focusflow.data.model.BudgetCategory targetCategory = null;
    private final double purchaseAmount = 0.0;
    private final boolean wouldExceedBudget = false;
    private final double remainingAfterPurchase = 0.0;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.BudgetCategory> alternativeCategories = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.ui.viewmodel.TotalBudgetImpact totalBudgetImpact = null;
    
    public BudgetImpactPreview(@org.jetbrains.annotations.Nullable
    com.focusflow.data.model.BudgetCategory targetCategory, double purchaseAmount, boolean wouldExceedBudget, double remainingAfterPurchase, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.BudgetCategory> alternativeCategories, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.TotalBudgetImpact totalBudgetImpact) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.BudgetCategory getTargetCategory() {
        return null;
    }
    
    public final double getPurchaseAmount() {
        return 0.0;
    }
    
    public final boolean getWouldExceedBudget() {
        return false;
    }
    
    public final double getRemainingAfterPurchase() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.BudgetCategory> getAlternativeCategories() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.viewmodel.TotalBudgetImpact getTotalBudgetImpact() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.BudgetCategory component1() {
        return null;
    }
    
    public final double component2() {
        return 0.0;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.BudgetCategory> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.viewmodel.TotalBudgetImpact component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.viewmodel.BudgetImpactPreview copy(@org.jetbrains.annotations.Nullable
    com.focusflow.data.model.BudgetCategory targetCategory, double purchaseAmount, boolean wouldExceedBudget, double remainingAfterPurchase, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.BudgetCategory> alternativeCategories, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.TotalBudgetImpact totalBudgetImpact) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}