package com.focusflow.data.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import com.focusflow.data.model.PayoffMilestone
import kotlinx.datetime.LocalDate

@Dao
interface PayoffMilestoneDao {
    @Query("SELECT * FROM payoff_milestones WHERE payoffPlanId = :planId ORDER BY targetDate ASC")
    fun getMilestonesByPlan(planId: Long): Flow<List<PayoffMilestone>>

    @Query("SELECT * FROM payoff_milestones WHERE payoffPlanId = :planId ORDER BY targetDate ASC")
    suspend fun getMilestonesByPlanSync(planId: Long): List<PayoffMilestone>

    @Query("SELECT * FROM payoff_milestones WHERE isCompleted = 0 AND targetDate <= :date ORDER BY targetDate ASC")
    fun getUpcomingMilestones(date: LocalDate): Flow<List<PayoffMilestone>>

    @Query("SELECT * FROM payoff_milestones WHERE isCompleted = 1 ORDER BY completedDate DESC")
    fun getCompletedMilestones(): Flow<List<PayoffMilestone>>

    @Query("SELECT * FROM payoff_milestones WHERE payoffPlanId = :planId AND isCompleted = 0 ORDER BY targetDate ASC LIMIT 1")
    suspend fun getNextMilestone(planId: Long): PayoffMilestone?

    @Query("SELECT COUNT(*) FROM payoff_milestones WHERE payoffPlanId = :planId AND isCompleted = 1")
    suspend fun getCompletedMilestoneCount(planId: Long): Int

    @Query("SELECT COUNT(*) FROM payoff_milestones WHERE payoffPlanId = :planId")
    suspend fun getTotalMilestoneCount(planId: Long): Int

    @Insert
    suspend fun insertMilestone(milestone: PayoffMilestone): Long

    @Insert
    suspend fun insertMilestones(milestones: List<PayoffMilestone>)

    @Update
    suspend fun updateMilestone(milestone: PayoffMilestone)

    @Delete
    suspend fun deleteMilestone(milestone: PayoffMilestone)

    @Query("DELETE FROM payoff_milestones WHERE payoffPlanId = :planId")
    suspend fun deleteMilestonesByPlan(planId: Long)

    @Query("UPDATE payoff_milestones SET isCompleted = 1, completedDate = :completedDate WHERE id = :milestoneId")
    suspend fun markMilestoneCompleted(milestoneId: Long, completedDate: LocalDate)
}
