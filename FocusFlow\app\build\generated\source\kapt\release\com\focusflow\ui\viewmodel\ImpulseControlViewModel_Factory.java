package com.focusflow.ui.viewmodel;

import com.focusflow.data.repository.BudgetCategoryRepository;
import com.focusflow.data.repository.UserPreferencesRepository;
import com.focusflow.data.repository.WishlistRepository;
import com.focusflow.service.PurchaseDelayService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ImpulseControlViewModel_Factory implements Factory<ImpulseControlViewModel> {
  private final Provider<WishlistRepository> wishlistRepositoryProvider;

  private final Provider<PurchaseDelayService> purchaseDelayServiceProvider;

  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  private final Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider;

  public ImpulseControlViewModel_Factory(Provider<WishlistRepository> wishlistRepositoryProvider,
      Provider<PurchaseDelayService> purchaseDelayServiceProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider) {
    this.wishlistRepositoryProvider = wishlistRepositoryProvider;
    this.purchaseDelayServiceProvider = purchaseDelayServiceProvider;
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
    this.budgetCategoryRepositoryProvider = budgetCategoryRepositoryProvider;
  }

  @Override
  public ImpulseControlViewModel get() {
    return newInstance(wishlistRepositoryProvider.get(), purchaseDelayServiceProvider.get(), userPreferencesRepositoryProvider.get(), budgetCategoryRepositoryProvider.get());
  }

  public static ImpulseControlViewModel_Factory create(
      Provider<WishlistRepository> wishlistRepositoryProvider,
      Provider<PurchaseDelayService> purchaseDelayServiceProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider) {
    return new ImpulseControlViewModel_Factory(wishlistRepositoryProvider, purchaseDelayServiceProvider, userPreferencesRepositoryProvider, budgetCategoryRepositoryProvider);
  }

  public static ImpulseControlViewModel newInstance(WishlistRepository wishlistRepository,
      PurchaseDelayService purchaseDelayService,
      UserPreferencesRepository userPreferencesRepository,
      BudgetCategoryRepository budgetCategoryRepository) {
    return new ImpulseControlViewModel(wishlistRepository, purchaseDelayService, userPreferencesRepository, budgetCategoryRepository);
  }
}
